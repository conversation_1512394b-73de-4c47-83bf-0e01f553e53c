# Setup Python under Ubuntu for _LOCAL_ testing:

Under Ubuntu, the way virtual environments are handled changed with version 24.04.1 LTS at the latest, maybe a sub-version before (not sure). To set up Python to run the PlayWright and Tavern tests the following steps are recommended:

(Guide assumes you use zsh. The installation under bash it will work very similar, but it is untested)

### We need to setup pyenv first to manage testing dependencies:

```
$ sudo apt update
```

```
$ sudo apt install -y make build-essential libssl-dev zlib1g-dev \libbz2-dev libreadline-dev libsqlite3-dev wget curl llvm \libncurses5-dev libncursesw5-dev xz-utils tk-dev libffi-dev \liblzma-dev python3-openssl git
```

```
$ curl https://pyenv.run | bash
```

### Edit the _Z shell run commands_ file via ...
```
$ nano ~/.zshrc
```

### ... and append the following to your .zshrc:

``` bash
# setup python environments
export PYENV_ROOT="$HOME/.pyenv"
[[ -d $PYENV_ROOT/bin ]] && export PATH="$PYENV_ROOT/bin:$PATH"
eval "$(pyenv init -)"
# start it automatically
eval "$(pyenv virtualenv-init -)"
```

### Install the required Python version via pyenv:

```
$ pyenv install 3.12.3
```

### Check if pyenv is correctly setup by typing:

```
$ pyenv versions
```

### You should get **two** Python instances listed like so:

```
* system (set by /home/<USER>/.pyenv/version)
  3.12.3
```

If the 3.12.3 installment appears in the list the setup was successful and you can proceed.

### Configure the testing-environment:

```
$ pyenv virtualenv 3.12.3 testing
```

### Activate the created environment via:

```
$ pyenv activate testing
```

### Pip-install dependencies the usual way utilizing the ```requirements.txt``` file inside the ```tests-qa``` directory:

```
$ pip install -r requirements.txt
```


# Install Common-QA Library

### Clone the ```taas-qa-common``` repository and create symlinks to the necessary files from that repository within this project.

-> cd into the folder where you usually store your working copies.

```
$ git clone https://git.fra1.us.utimaco.cloud/taas/taas-qa/taas-qa-common.git
```

-> cd into the ```tests-qa``` directory and create the following symlinks:

```
ln -s PATH_TO_PROJECTS_DIR/taas-qa-common/common_used/conftest.py ./conftest.py
```

```
ln -s PATH_TO_PROJECTS_DIR/taas-qa-common/common_used/subclasses ./subclasses
```

