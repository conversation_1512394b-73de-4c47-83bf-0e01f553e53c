import logging, pytest, traceback, time
import random
import string

from pom import POM
from subclasses.common_ui_lib import TAASCUIL

# Skip the entire file if PROD is True
@pytest.fixture(autouse=True, scope="module")
def skip_if_prod(PROD):
    if PROD:
        pytest.skip("Skipping tests in production environment")

def test_ts(
                                request,
                                getPage,
                                running_on,
                                getProjectVars,
    ):

    testID          = "KMAAS-99"
    testname        = "Create GUI for deleting keys in an Azure instance"
    testDescription = f"PlayWright-Test: '{testID}-{testname}'"
    status          = "ready"

    pom    = POM(getPage)
    cuil   = TAASCUIL(getPage)
    page   = pom.page
    pv     = getProjectVars

    QA_AZURE_TENANT_ID     = pv['QA_AZURE_TENANT_ID']
    QA_AZURE_CLIENT_ID     = pv['QA_AZURE_CLIENT_ID']
    QA_AZURE_CLIENT_SECRET = pv['QA_AZURE_CLIENT_SECRET']
    QA_AZURE_KEY_VAULT     = pv['key_vault_name']
    QA_MFA_SECRET          = pv['QA_MFA_SECRET' ]

    cloud_instance_name    = pv['cloud_instance_name']
    pom.instance_name      = cloud_instance_name

    logging.info(testDescription)

    try:
        cuil.goto(pv['ci_url'] if running_on == "gitlab" else pv['dev_url'])

        cuil.log_teststep("Login")
        cuil.login (pv, QA_MFA_SECRET)

        cuil.isTitle  ("Enterprise Key Manager as a Service")

        cuil.log_teststep("Click 'Cloud Clients' -> 'Azure'")
        cuil.click_main_menu_item ({'Cloud Clients':'Azure'})

        # page.pause()
        

        # -------------------------------------------------------------------------------------------------------------

        cuil.log_teststep("PRECONDITION: If NOT present we create cloud instance (like in https://utimaco.atlassian.net/browse/KMAAS-42)")
        # precondition: we need an azure instance
        if not page.get_by_role("cell", name=pom.instance_name, exact=True).is_visible():
            logging.info(f"Create Instance: {pom.instance_name}")
            pom.precondition_create_cloud_instance(
                        pom.instance_name,
                        QA_AZURE_TENANT_ID,
                        QA_AZURE_CLIENT_ID,
                        QA_AZURE_CLIENT_SECRET,
                        QA_AZURE_KEY_VAULT
            )

        # -------------------------------------------------------------------------------------------------------------

        cuil.log_teststep("Click “Manage Keys” in Azure row")
        pom.click_cloud_instance_manage_keys(pom.instance_name)

        cuil.log_teststep("If the instance has keys already we delete all of them")
        pom.delete_all_keys()

        # -------------------------------------------------------------------------------------------------------------

        keyname = pom.make_keyname("<instanceName>_RSA-2048_<random>")

        pom.precondition_create_key(
                    cloud_instance_name,
                    keyname,
                    QA_AZURE_KEY_VAULT
        )

        # -------------------------------------------------------------------------------------------------------------

        # Teardown:
        cuil.log_teststep("Teardown")

        pom.delete_all_keys()
        cuil.click_main_menu_item ({'Cloud Clients':'Azure'})
        pom.click_cloud_instance_delete_instance(pom.instance_name)
        #page.pause()
        pom.await_toaster(f"Cloud Instance {pom.instance_name} [Azure] deleted successfully")


    except Exception: cuil.handle_exception(request)
    else: logging.info(f"TESTEND:PASS:{testDescription}")
