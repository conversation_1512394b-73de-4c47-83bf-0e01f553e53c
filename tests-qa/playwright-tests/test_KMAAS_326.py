import logging, pytest, traceback, time
from playwright.sync_api import expect
from pom import POM
from subclasses.common_ui_lib import TAASCUIL

# Skip the entire file if PROD is True
@pytest.fixture(autouse=True, scope="module")
def skip_if_prod(PROD):
    if PROD:
        pytest.skip("Skipping tests in production environment")
        
def test_ts(
                                request,
                                getPage,
                                running_on,
                                getProjectVars,
    ):

    testID          = "KMAAS-326"
    testname        = "Disable Utimaco link (top-left text-logo) to TaaS portal"
    testDescription = f"PlayWright-Test: '{testID}-{testname}'"
    status          = "ready"
    
    pom    = POM(getPage)
    cuil   = TAASCUIL(getPage)
    page   = pom.page
    pv     = getProjectVars

    QA_MFA_SECRET          = pv['QA_MFA_SECRET' ]

    try:
        logging.info(testDescription)

        cuil.goto(pv['ci_url'] if running_on == "gitlab" else pv['dev_url'])

        cuil.log_teststep("Login")
        cuil.login (pv, QA_MFA_SECRET)
        
        cuil.isTitle  ("Enterprise Key Manager as a Service")

        cuil.log_teststep("Click 'Cloud Clients' -> 'Azure'")
        cuil.click_main_menu_item ({'Cloud Clients':'Azure'})

        time.sleep(1)
        
        page.locator("lib-taas-logo-svg").click()
        
        expect(page.locator("#addCloudInstanceOpen")).to_be_visible()


    except Exception: cuil.handle_exception(request)
    else: logging.info(f"TESTEND:PASS:{testDescription}")
