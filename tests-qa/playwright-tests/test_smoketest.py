import logging, pytest, traceback, time
from pom import POM
from subclasses.common_ui_lib import TAASCUIL
from playwright.sync_api import expect
from datetime import datetime

def test_ts(
                                request,
                                getPage,
                                running_on,
                                getProjectVars,
    ):

    testID          = "KMAAS-460"
    testname        = "EKMaaS Smoketest"
    testDescription = f"PlayWright-Test: '{testID}-{testname}'"
    status          = "partly"

    cuil   = TAASCUIL(getPage,testID,testname)
    pom    = POM(getPage,cuil)
    page   = pom.page
    pv     = getProjectVars

    QA_MFA_SECRET          = pv['QA_MFA_SECRET' ]
    
    # LANG
    en = True
    de = False

    logging.info(testDescription)

    try:

        target = pv['ci_url'] if running_on == "gitlab" else pv['dev_url']
        
        # -------------------------------------------------------------------------------------------------------------

        cuil.log_teststep("Check Login page response")
        cuil.check_response(target, "/")

        # -------------------------------------------------------------------------------------------------------------

        cuil.log_teststep("Login")
        cuil.login (pv, QA_MFA_SECRET)
        cuil.isTitle ("Enterprise Key Manager as a Service")

        # -------------------------------------------------------------------------------------------------------------

        if en:
            prefix = "/en/"

            cuil.log_teststep("Test if all EN Sub-Pages respond with 200")
            cuil.check_response_list (
                target, 
                [
                    f"{prefix}cloud-clients/azure",
                    f"{prefix}cloud-clients/aws",
                    f"{prefix}cloud-clients/gcp",
                    f"{prefix}enterprise-clients/kmip",
                    f"{prefix}enterprise-clients/tde",
                    f"{prefix}enterprise-clients/rest",
                    f"{prefix}access-management/users",
                    f"{prefix}access-management/groups",
                    f"{prefix}audit-logs",
                    f"{prefix}settings",
                    f"{prefix}dashboard"
                ]
            )

        # -------------------------------------------------------------------------------------------------------------

        cuil.log_teststep("Checking Main Menu Entries")
        cuil.verify_main_menu_structure(
            {
                'Dashboard': None,   
                'Cloud Clients': {
                    'Azure': None,
                    'AWS': None,
                    'Google': None,
                },
                'Enterprise Clients': {
                    'KMIP': None,
                    'TDE': None,
                    'REST': None,
                },
                'Access Management': {
                    'Users': None,
                    'Groups': None,
                },
                'Audit Log': None,   
                'Setting': None,   
            }
        )

        # -------------------------------------------------------------------------------------------------------------
        
        if de:
            
            prefix = "/de/"

            cuil.log_teststep("Test if all DE Sub-Pages respond with 200")
            cuil.check_response_list (
                target, 
                [
                    f"{prefix}cloud-clients/azure",
                    f"{prefix}cloud-clients/aws",
                    f"{prefix}cloud-clients/gcp",
                    f"{prefix}enterprise-clients/kmip",
                    f"{prefix}enterprise-clients/tde",
                    f"{prefix}enterprise-clients/rest",
                    f"{prefix}access-management/users",
                    f"{prefix}access-management/groups",
                    f"{prefix}audit-logs",
                    f"{prefix}settings",
                    f"{prefix}dashboard"
                ]
            )

        # -------------------------------------------------------------------------------------------------------------

        if de:
            cuil.log_teststep("Switch back to english")
            prefix = "/en/"
            cuil.check_response(target,f"{prefix}dashboard")

        # -------------------------------------------------------------------------------------------------------------

        cuil.log_teststep("Check Dashboard Content")
        cuil.text_visible("Feature Under Development")

        # -------------------------------------------------------------------------------------------------------------

        cuil.log_teststep("Click 'Cloud Clients' -> 'Azure'")
        cuil.click_main_menu_item ({'Cloud Clients':'Azure'})
        cuil.wait_for_quiet_network()

        cuil.log_teststep("Check AWS Content")
        cuil.testids_visible([
            "kmaas.header.cell.instanceName",
            "kmaas.header.cell.cloudType",
            "kmaas.header.cell.createdDate",
            "kmaas.header.cell.lastUpdated",
            "kmaas.header.cell.action",
        ])

        # -------------------------------------------------------------------------------------------------------------

        cuil.log_teststep("Click 'Cloud Clients' -> 'AWS'")
        cuil.click_main_menu_item ({'Cloud Clients':'AWS'})
        cuil.wait_for_quiet_network()

        cuil.log_teststep("Check AWS Content")
        cuil.text_visible("Feature Under Development")

        # -------------------------------------------------------------------------------------------------------------

        cuil.log_teststep("Click 'Cloud Clients' -> 'Google'")
        cuil.click_main_menu_item ({'Cloud Clients':'Google'})
        cuil.wait_for_quiet_network()

        cuil.log_teststep("Check Google Content")
        cuil.text_visible("Feature Under Development")

        # -------------------------------------------------------------------------------------------------------------

        cuil.log_teststep("Click 'Enterprise Clients' -> 'KMIP'")
        cuil.click_main_menu_item ({'Enterprise Clients':'KMIP'})
        cuil.wait_for_quiet_network()

        cuil.log_teststep("Check KMIP Content")
        cuil.testids_visible([
            "kmip-clients.header.cell.clientId",
            "kmip-clients.header.cell.createdBy",
            "kmip-clients.header.cell.creationDate",
            "kmip-clients.header.cell.certificateExpirationDate",
            "kmip-clients.header.cell.lastModified",
            "kmip-clients.header.cell.action",
        ])

        # -------------------------------------------------------------------------------------------------------------

        cuil.log_teststep("Click 'Enterprise Clients' -> 'TDE'")
        cuil.click_main_menu_item ({'Enterprise Clients':'TDE'})
        cuil.wait_for_quiet_network()

        cuil.log_teststep("Check TDE Content")
        cuil.text_visible("Feature Under Development")

        # -------------------------------------------------------------------------------------------------------------

        cuil.log_teststep("Click 'Enterprise Clients' -> 'REST'")
        cuil.click_main_menu_item ({'Enterprise Clients':'REST'})
        cuil.wait_for_quiet_network()

        cuil.log_teststep("Check REST Content")
        cuil.text_visible("Feature Under Development")

        # -------------------------------------------------------------------------------------------------------------

        cuil.log_teststep("Click 'Access Management' -> 'Users'")
        cuil.click_main_menu_item ({'Access Management':'Users'})
        cuil.wait_for_quiet_network()

        cuil.log_teststep("Check Users Content")
        cuil.text_visible("Feature Under Development")

        # -------------------------------------------------------------------------------------------------------------

        cuil.log_teststep("Click 'Access Management' -> 'Groups'")
        cuil.click_main_menu_item ({'Access Management':'Groups'})
        cuil.wait_for_quiet_network()

        cuil.log_teststep("Check Groups Content")
        cuil.text_visible("Feature Under Development")

        # -------------------------------------------------------------------------------------------------------------

        cuil.log_teststep("Click 'Audit Log'")
        cuil.click_main_menu_item ({'Audit Log': None })
        cuil.wait_for_quiet_network()

        cuil.log_teststep("Check Audit Log Content")

        # no data-testid's here...
        cuil.text_visible("Date & Time")
        cuil.text_visible("Key Name")
        cuil.text_visible("Operation")
        cuil.text_visible("Status")
        cuil.text_visible("Message")
        cuil.text_visible("User")
        cuil.text_visible("IP Address")

        # -------------------------------------------------------------------------------------------------------------

        cuil.log_teststep("Click 'Settings'")
        cuil.click_main_menu_item ({'Setting': None })
        cuil.wait_for_quiet_network()

        cuil.log_teststep("Check Settings Content")
        cuil.text_visible("Feature Under Development")

        # -------------------------------------------------------------------------------------------------------------

    except Exception: cuil.handle_exception(request)
    else: logging.info(f"TESTEND:PASS:{testDescription}")
