"""
Testcase:
- login
- click main Menu “Cloud Instances”  
- PRECONDITION: Create cloud instance (like in https://utimaco.atlassian.net/browse/KMAAS-42)
- check if instance was created (table entry)
- click on instance name to enter edit popup modal
- make client secret visible 
- check if correct values are present
- try to change key vaults entry -> enter not existing vault name
- ...
- await "Invalid key vault name or failed to connect with the cloud instance." error
- ...
- TEARDOWN: Delete cloud instance (like in https://utimaco.atlassian.net/browse/KMAAS-98)



JIRA Ticket: https://utimaco.atlassian.net/browse/KMAAS-100
"""

import logging, pytest, traceback
from pom import POM
from subclasses.common_ui_lib import TAASCUIL
from playwright.sync_api import expect

# Skip the entire file if PROD is True
@pytest.fixture(autouse=True, scope="module")
def skip_if_prod(PROD):
    if PROD:
        pytest.skip("Skipping tests in production environment")

def test_ts(
                                request,
                                getPage,
                                running_on,
                                getSessionVars,
                                getProjectVars,
                                config_variables,
                                json_data,

                                QA_AZURE_TENANT_ID,
                                QA_AZURE_CLIENT_ID,
                                QA_AZURE_CLIENT_SECRET,
                                QA_AZURE_KEY_VAULT

    ):

    testname = "GUI for creating new Azure Cloud instance"

    testDescription = f"Test: '{testname}'"

    try:
        logging.info(testDescription)

        logging.info(f"SessionVars: {str(getSessionVars)}")
        logging.info(f"config_variables: {str(config_variables)}")
        logging.info(f"json_data: {str(json_data)}")

        pom    = POM(getPage)
        cuil   = TAASCUIL(getPage)
        page   = pom.page
        sv     = getSessionVars
        pv     = getProjectVars

        cloud_instance_name = "qa-dus-test-instance"

        cuil.goto(pv['ci_url'] if running_on == "gitlab" else pv['dev_url'])

        cuil.login (sv)

        cuil.isTitle  ("Enterprise Key Manager as a Service")

        cuil.click_main_menu_item ({'Cloud Instances':None})

        # precondition: we need an azure instance
        pom.precondition_create_cloud_instance(
                    cloud_instance_name,
                    QA_AZURE_TENANT_ID,
                    QA_AZURE_CLIENT_ID,
                    QA_AZURE_CLIENT_SECRET,
                    QA_AZURE_KEY_VAULT
        )

        # -------------------------------------------------------------------------------------------------------------
          
        # check table and click entry
        pom.check_instance_existence(cloud_instance_name)
        pom.click_instance_table_entry(cloud_instance_name)

        # -------------------------------------------------------------------------------------------------------------

        # popup opens
        pom.check_edit_instance_popup_elements()

        # click on the eye to make secret visible
        pom.click_secret_eye()

        pom.check_instance_values(
                    cloud_instance_name,
                    QA_AZURE_TENANT_ID,
                    QA_AZURE_CLIENT_ID,
                    QA_AZURE_CLIENT_SECRET,
                    QA_AZURE_KEY_VAULT
        )

        # -------------------------------------------------------------------------------------------------------------

        pom.fill_key_Vaults("None existing Vault")


        page.pause()

        # change key vault to inexistent, we expect error:
        # -> Invalid key vault name or failed to connect with the cloud instance.
        


        pom.click_cancel_button()

        # -------------------------------------------------------------------------------------------------------------
 

        # -------------------------------------------------------------------------------------------------------------

        # delete instance for cleanup 
        pom.delete_instance(cloud_instance_name)

        page.pause()

        # -------------------------------------------------------------------------------------------------------------

    except Exception: cuil.handle_exception(request)
    else: logging.info(f"TESTEND:PASS:{testDescription}")
