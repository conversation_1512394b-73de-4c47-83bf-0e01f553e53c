import logging, pytest, requests

def test_health():
    testDescription = f"Test: 'Healthcheck'"
    logging.info(testDescription)
    check_ekmaas_service_health()

# maybe for other health checks:
# https://registration-api.dev.services.utimaco.com/actuator/health
# https://sso.dev.services.utimaco.com/health
 
# check health status of EKMAAS
def check_ekmaas_service_health():

    url = "https://eu-nl.ekmaas.preprod.services.utimaco.com/actuator/health"
    expected_response = {"status": "UP", "groups": ["liveness", "readiness"]}

    try:
        # Ignore SSL certificate verification
        response = requests.get(url, verify=False)
        
        # Check that we received a 200 OK
        if response.status_code != 200:
            logging.error("Service is down: received HTTP status %d", response.status_code)
            pytest.fail("Service is down: Non-200 HTTP response")
        
        # Attempt to parse JSON
        try:
            data = response.json()
        except ValueError:
            logging.error("Service is down: response is not valid JSON.")
            pytest.fail("Service is down: Invalid JSON in response")
        
        # Check if the JSON matches the expected structure
        if data != expected_response:
            logging.error("Service is down: Unexpected response. Got: %s", data)
            pytest.fail("Service is down: Incorrect response from service")
        
        # If we reach here, the service is UP
        logging.info("Service is UP and responding as expected.")

    except requests.RequestException as e:
        logging.exception("Failed to fetch service status.")
        pytest.fail(f"Service is down: Could not retrieve status. Exception: {e}")
