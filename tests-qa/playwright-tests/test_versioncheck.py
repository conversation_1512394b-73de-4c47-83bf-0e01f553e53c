import logging, time
from subclasses.common_ui_lib import T<PERSON><PERSON><PERSON><PERSON>

def test_version(
                                request,
                                getPage,
                                running_on,
                                getProjectVars,
    ):

    testID          = "Versioncheck"
    testname        = "Versioncheck"
    testDescription = f"PlayWright-Test: '{testID}-{testname}'"
    status          = "ready"

    cuil   = TAASCUIL(getPage,testID,testname)
    pv     = getProjectVars

    QA_MFA_SECRET = pv['QA_MFA_SECRET']

    logging.info(testDescription)

    try:

        cuil.goto(pv['ci_url'] if running_on == "gitlab" else pv['dev_url'])

        cuil.log_teststep("Login")
        cuil.login (pv, QA_MFA_SECRET)

        cuil.isTitle ("Enterprise Key Manager as a Service")

        # -------------------------------------------------------------------------------------------------------------

        cuil.log_teststep("Revealing version info via easteregg key-combi and write it to version.txt")

        cuil.log_teststep("Click 'Cloud Clients' -> 'Azure'")
        cuil.click_main_menu_item ({'Cloud Clients':'Azure'})
        
        cuil.check_cuil_version()

    except Exception: cuil.handle_exception(request)
    else: logging.info(f"TESTEND:PASS:{testDescription}")