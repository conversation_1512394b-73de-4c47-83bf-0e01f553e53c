import logging, pytest, traceback, time
import random
import string
from pprint import pformat

from pom import POM
from subclasses.common_ui_lib import TAASCUIL
from playwright.sync_api import expect

# Skip the entire file if PROD is True
@pytest.fixture(autouse=True, scope="module")
def skip_if_prod(PROD):
    if PROD:
        pytest.skip("Skipping tests in production environment")

def test_ts(
                                request,
                                getPage,
                                running_on,
                                getProjectVars,
    ):

    testID          = "KMAAS-96"
    testname        = "Create GUI for creating new keys in existing Azure instance"
    testDescription = f"PlayWright-Test: '{testID}-{testname}'"
    status          = "ready"

        # 2x successfully completed 04.06.2025
        # 1x successfully completed 11.06.2025
        
        # but still potentially unstable.
        

    cuil   = TAASCUIL(getPage,testID=testID, testName=testname)
    pom    = POM(getPage,cuil)
    page   = pom.page
    pv     = getProjectVars

    pom.instance_name         = pv['cloud_instance_name']
    pom.instance_name2vaults  = pv['cloud_instance_name_2vaults']

    QA_MFA_SECRET          = pv['QA_MFA_SECRET' ]
    
    QA_AZURE_TENANT_ID     = pv['QA_AZURE_TENANT_ID']
    QA_AZURE_CLIENT_ID     = pv['QA_AZURE_CLIENT_ID']
    QA_AZURE_CLIENT_SECRET = pv['QA_AZURE_CLIENT_SECRET']
    QA_AZURE_KEY_VAULT     = pv['key_vault_name']
    
    QA_AZURE_TENANT_ID_2      = pv['QA_AZURE_TENANT_ID_2']
    QA_AZURE_CLIENT_ID_2      = pv['QA_AZURE_CLIENT_ID_2']
    QA_AZURE_CLIENT_SECRET_2  = pv['QA_AZURE_CLIENT_SECRET_2']
    QA_AZURE_KEY_VAULT_2A     = pv['QA_AZURE_KEY_VAULT_2A']
    QA_AZURE_KEY_VAULT_2B     = pv['QA_AZURE_KEY_VAULT_2B']

    logging.info(testDescription)

    processed_variants = []

    try:

        cuil.goto(pv['ci_url'] if running_on == "gitlab" else pv['dev_url'])

        cuil.log_teststep("Login")
        cuil.login (pv, QA_MFA_SECRET)
        
        # pom.pause()

        cuil.isTitle ("Enterprise Key Manager as a Service")

        cuil.log_teststep("Click 'Cloud Clients' -> 'Azure'")
        cuil.click_main_menu_item ({'Cloud Clients':'Azure'})


        # page.pause()
        # -------------------------------------------------------------------------------------------------------------


        # logging.debug(f"QA_AZURE_TENANT_ID: {QA_AZURE_TENANT_ID}")
        # logging.debug(f"QA_AZURE_CLIENT_ID: {QA_AZURE_CLIENT_ID}")
        # logging.debug(f"QA_AZURE_CLIENT_SECRET: {QA_AZURE_CLIENT_SECRET}")
        # logging.debug(f"QA_AZURE_KEY_VAULT: {QA_AZURE_KEY_VAULT}")

        # page.pause()
        time.sleep(2)

        cuil.log_teststep("PRECONDITION: If NOT present we create cloud instance (like in https://utimaco.atlassian.net/browse/KMAAS-42)")
        # precondition: we need an azure instance
        if not page.get_by_role("cell", name=pom.instance_name, exact=True).is_visible():
            pom.precondition_create_cloud_instance(
                        pom.instance_name,
                        QA_AZURE_TENANT_ID,
                        QA_AZURE_CLIENT_ID,
                        QA_AZURE_CLIENT_SECRET,
                        QA_AZURE_KEY_VAULT
            )
            
        cuil.log_teststep("PRECONDITION: If NOT present we create A SECOND cloud instance with TWO vaults")
        if not page.get_by_role("cell", name=pom.instance_name2vaults, exact=True).is_visible():
            vaults = f"{QA_AZURE_KEY_VAULT_2A},{QA_AZURE_KEY_VAULT_2B}"
            pom.precondition_create_cloud_instance(
                        pom.instance_name2vaults,
                        QA_AZURE_TENANT_ID_2,
                        QA_AZURE_CLIENT_ID_2,
                        QA_AZURE_CLIENT_SECRET_2,
                        vaults
            )

        # -------------------------------------------------------------------------------------------------------------

        cuil.log_teststep("Click “Manage Keys” in Azure row")
        pom.click_cloud_instance_manage_keys(pom.instance_name)
        cuil.log_teststep("If the instance has keys already we delete all of them")
        pom.delete_all_keys()

        cuil.log_teststep("Click 'Cloud Clients' -> 'Azure'")
        cuil.click_main_menu_item ({'Cloud Clients':'Azure'})

        cuil.log_teststep("Click “Manage Keys” in Azure row of second cloud instance")
        pom.click_cloud_instance_manage_keys(pom.instance_name2vaults)
        cuil.log_teststep("If the second cloud instance has keys already we delete all of them")
        pom.delete_all_keys()

        cuil.log_teststep("Click 'Cloud Clients' -> 'Azure'")
        cuil.click_main_menu_item ({'Cloud Clients':'Azure'})
        pom.click_cloud_instance_manage_keys(pom.instance_name)

        # page.pause()

        # -------------------------------------------------------------------------------------------------------------

        # Test data for iteration
        
        test_variants = [

            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_1",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-2048_<random>",
                "algorithm"                 : "RSA-2048",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName>_RSA-2048_<random>",
                "checkbox_enabled"          : True,
                "key_vault"                 : "TaaS-EKMaaS-QA-THO",
                "activation_date"           : "<currentDateTime>",
                "activation_date_timezone"  : "<randomTimeZone>",
                "expiration_date"           : "02/13/2030, 00:00 AM",
                "expiration_date_timezone"  : "<randomTimeZone>",
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : True,
                "button_upload_expectedres" : "Key [<keyname>] has been successfully uploaded to <instanceName> [Azure].",
                "button_cancel_2"           : False,
                "button_okay"               : True,
                "button_okay_expectedres"   : False,
                "checks"                    : False,
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_2",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-3072_<random>",
                "algorithm"                 : "RSA-3072",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName>_RSA-3072_<random>",
                "checkbox_enabled"          : True,
                "key_vault"                 : "TaaS-EKMaaS-QA-THO",
                "activation_date"           : "<currentDateTime>",
                "activation_date_timezone"  : "<randomTimeZone>",
                "expiration_date"           : "02/13/2030, 00:00 AM",
                "expiration_date_timezone"  : "<randomTimeZone>",
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : True,
                "button_upload_expectedres" : "Key [<keyname>] has been successfully uploaded to <instanceName> [Azure].",
                "button_cancel_2"           : False,
                "button_okay"               : True,
                "button_okay_expectedres"   : False,
                "checks"                    : False,
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_3",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-4096_<random>",
                "algorithm"                 : "RSA-4096",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName>_RSA-4096_<random>",
                "checkbox_enabled"          : True,
                "key_vault"                 : "TaaS-EKMaaS-QA-THO",
                "activation_date"           : "<currentDateTime>",
                "activation_date_timezone"  : "<randomTimeZone>",
                "expiration_date"           : "02/13/2030, 00:00 AM",
                "expiration_date_timezone"  : "<randomTimeZone>",
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : True,
                "button_upload_expectedres" : "Key [<keyname>] has been successfully uploaded to <instanceName> [Azure].",
                "button_cancel_2"           : False,
                "button_okay"               : True,
                "button_okay_expectedres"   : False,
                "checks"                    : False,
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_4",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-2048_<random>",
                "algorithm"                 : "RSA-2048",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName>_RSA-2048_<random>",
                "checkbox_enabled"          : False,
                "key_vault"                 : "TaaS-EKMaaS-QA-THO",
                "activation_date"           : "<currentDateTime>",
                "activation_date_timezone"  : "<randomTimeZone>",
                "expiration_date"           : "02/13/2030, 00:00 AM",
                "expiration_date_timezone"  : "<randomTimeZone>",
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : True,
                "button_upload_expectedres" : "Key [<keyname>] has been successfully uploaded to <instanceName> [Azure].",
                "button_cancel_2"           : False,
                "button_okay"               : True,
                "button_okay_expectedres"   : False,
                "checks"                    : False,
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_5",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-3072_<random>",
                "algorithm"                 : "RSA-3072",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName>_RSA-3072_<random>",
                "checkbox_enabled"          : False,
                "key_vault"                 : "TaaS-EKMaaS-QA-THO",
                "activation_date"           : "<currentDateTime>",
                "activation_date_timezone"  : "<randomTimeZone>",
                "expiration_date"           : "02/13/2030, 00:00 AM",
                "expiration_date_timezone"  : "<randomTimeZone>",
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : True,
                "button_upload_expectedres" : "Key [<keyname>] has been successfully uploaded to <instanceName> [Azure].",
                "button_cancel_2"           : False,
                "button_okay"               : True,
                "button_okay_expectedres"   : False,
                "checks"                    : False,
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_6",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-4096_<random>",
                "algorithm"                 : "RSA-4096",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName>_RSA-4096_<random>",
                "checkbox_enabled"          : False,
                "key_vault"                 : "TaaS-EKMaaS-QA-THO",
                "activation_date"           : "<currentDateTime>",
                "activation_date_timezone"  : "<randomTimeZone>",
                "expiration_date"           : "02/13/2030, 00:00 AM",
                "expiration_date_timezone"  : "<randomTimeZone>",
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : True,
                "button_upload_expectedres" : "Key [<keyname>] has been successfully uploaded to <instanceName> [Azure].",
                "button_cancel_2"           : False,
                "button_okay"               : True,
                "button_okay_expectedres"   : False,
                "checks"                    : False,
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_7",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-2048_<random>",
                "algorithm"                 : "RSA-2048",
                "button_create"             : False,
                "button_create_expectedres" : False,
                "button_cancel_1"           : True,
                "button_next"               : False,
                "button_close"              : False,
                "cloud_key_name"            : False,
                "checkbox_enabled"          : False,
                "key_vault"                 : False,
                "activation_date"           : False,
                "activation_date_timezone"  : False,
                "expiration_date"           : False,
                "expiration_date_timezone"  : False,
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : False,
                "button_upload_expectedres" : False,
                "button_cancel_2"           : False,
                "button_okay"               : False,
                "button_okay_expectedres"   : False,
                "checks"                    : "KeyNotCreated",
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_8",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-3072_<random>",
                "algorithm"                 : "RSA-3072",
                "button_create"             : False,
                "button_create_expectedres" : False,
                "button_cancel_1"           : True,
                "button_next"               : False,
                "button_close"              : False,
                "cloud_key_name"            : False,
                "checkbox_enabled"          : False,
                "key_vault"                 : False,
                "activation_date"           : False,
                "activation_date_timezone"  : False,
                "expiration_date"           : False,
                "expiration_date_timezone"  : False,
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : False,
                "button_upload_expectedres" : False,
                "button_cancel_2"           : False,
                "button_okay"               : False,
                "button_okay_expectedres"   : False,
                "checks"                    : "KeyNotCreated",
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_9",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-4096_<random>",
                "algorithm"                 : "RSA-4096",
                "button_create"             : False,
                "button_create_expectedres" : False,
                "button_cancel_1"           : True,
                "button_next"               : False,
                "button_close"              : False,
                "cloud_key_name"            : False,
                "checkbox_enabled"          : False,
                "key_vault"                 : False,
                "activation_date"           : False,
                "activation_date_timezone"  : False,
                "expiration_date"           : False,
                "expiration_date_timezone"  : False,
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : False,
                "button_upload_expectedres" : False,
                "button_cancel_2"           : False,
                "button_okay"               : False,
                "button_okay_expectedres"   : False,
                "checks"                    : "KeyNotCreated",
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_10",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-2048_<random>",
                "algorithm"                 : "RSA-2048",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName>_RSA-2048_<random>",
                "checkbox_enabled"          : True,
                "key_vault"                 : "TaaS-EKMaaS-QA-THO",
                "activation_date"           : "<currentDateTime>",
                "activation_date_timezone"  : "<randomTimeZone>",
                "expiration_date"           : "02/13/2030, 00:00 AM",
                "expiration_date_timezone"  : "<randomTimeZone>",
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : False,
                "button_upload_expectedres" : False,
                "button_cancel_2"           : True,
                "button_okay"               : False,
                "button_okay_expectedres"   : False,
                "checks"                    : "No Key Name Status Not Uploaded Enabled No No Key Vault", # MOD
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_11",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-3072_<random>",
                "algorithm"                 : "RSA-3072",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName>_RSA-3072_<random>",
                "checkbox_enabled"          : False,
                "key_vault"                 : "TaaS-EKMaaS-QA-THO",
                "activation_date"           : "<currentDateTime>",
                "activation_date_timezone"  : "<randomTimeZone>",
                "expiration_date"           : "02/13/2030, 00:00 AM",
                "expiration_date_timezone"  : "<randomTimeZone>",
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : False,
                "button_upload_expectedres" : False,
                "button_cancel_2"           : True,
                "button_okay"               : False,
                "button_okay_expectedres"   : False,
                "checks"                    : "No Key Name Status Not Uploaded Enabled No No Key Vault",
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_12",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-4096_<random>",
                "algorithm"                 : "RSA-4096",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName>_RSA-4096_<random>",
                "checkbox_enabled"          : False,
                "key_vault"                 : "TaaS-EKMaaS-QA-THO",
                "activation_date"           : "<currentDateTime>",
                "activation_date_timezone"  : "<randomTimeZone>",
                "expiration_date"           : "02/13/2030, 00:00 AM",
                "expiration_date_timezone"  : "<randomTimeZone>",
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : False,
                "button_upload_expectedres" : False,
                "button_cancel_2"           : True,
                "button_okay"               : False,
                "button_okay_expectedres"   : False,
                "checks"                    : "No Key Name Status Not Uploaded Enabled No No Key Vault",
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_13",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-2048_<random>",
                "algorithm"                 : "RSA-2048",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName>_RSA-2048_<random>",
                "checkbox_enabled"          : True,
                "key_vault"                 : "TaaS-EKMaaS-QA-THO",
                "activation_date"           : "<currentDateTime>",
                "activation_date_timezone"  : "<randomTimeZone>",
                "expiration_date"           : "02/13/2030, 00:00 AM",
                "expiration_date_timezone"  : "<randomTimeZone>",
                "tags_+"                    : False, # MOD
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : True,
                "button_upload_expectedres" : "Key [<keyname>] has been successfully uploaded to <instanceName> [Azure].",
                "button_cancel_2"           : False,
                "button_okay"               : True,
                "button_okay_expectedres"   : False,
                "checks"                    : False,
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_14",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-2048_<random>",
                "algorithm"                 : "RSA-2048",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName>_RSA-2048_<random>",
                "checkbox_enabled"          : True,
                "key_vault"                 : "TaaS-EKMaaS-QA-THO",
                "activation_date"           : "<currentDateTime>",
                "activation_date_timezone"  : "<randomTimeZone>",
                "expiration_date"           : "02/13/2030, 00:00 AM",
                "expiration_date_timezone"  : "<randomTimeZone>",
                "tags_+"                    : True,
                "tag_name"                  : "Tag-Name-<random>",
                "tag_value"                 : False,
                "button_upload"             : True,
                "button_upload_expectedres" : "Key [<keyname>] has been successfully uploaded to <instanceName> [Azure].",
                "button_cancel_2"           : False,
                "button_okay"               : True,
                "button_okay_expectedres"   : False,
                "checks"                    : False,
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_15",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-2048_<random>",
                "algorithm"                 : "RSA-2048",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName>_RSA-2048_<random>",
                "checkbox_enabled"          : True,
                "key_vault"                 : "TaaS-EKMaaS-QA-THO",
                "activation_date"           : "<currentDateTime>",
                "activation_date_timezone"  : "<randomTimeZone>",
                "expiration_date"           : "02/13/2030, 00:00 AM",
                "expiration_date_timezone"  : "<randomTimeZone>",
                "tags_+"                    : True,
                "tag_name"                  : False,
                "tag_value"                 : "Tag-Value-<random>",
                "button_upload"             : True,
                "button_upload_expectedres" : "Key [<keyname>] has been successfully uploaded to <instanceName> [Azure].",
                "button_cancel_2"           : False,
                "button_okay"               : True,
                "button_okay_expectedres"   : False,
                "checks"                    : False,
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_16",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-2048_<random>",
                "algorithm"                 : "RSA-2048",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName>_RSA-2048_<random>",
                "checkbox_enabled"          : True,
                "key_vault"                 : "TaaS-EKMaaS-QA-THO",
                "activation_date"           : "<currentDateTime>",
                "activation_date_timezone"  : "<randomTimeZone>",
                "expiration_date"           : "02/13/2030, 00:00 AM",
                "expiration_date_timezone"  : "<randomTimeZone>",
                "tags_+"                    : True,
                "tag_name"                  : "Tag-Name-<random>",
                "tag_value"                 : "Tag-Value-<random>",
                "button_upload"             : True,
                "button_upload_expectedres" : "Key [<keyname>] has been successfully uploaded to <instanceName> [Azure].",
                "button_cancel_2"           : False,
                "button_okay"               : True,
                "button_okay_expectedres"   : False,
                "checks"                    : False,
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_17",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-2048_<random>",
                "algorithm"                 : "RSA-2048",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName>_RSA-2048_<random>",
                "checkbox_enabled"          : False,
                "key_vault"                 : "TaaS-EKMaaS-QA-THO",
                "activation_date"           : "<currentDateTime>",
                "activation_date_timezone"  : "<randomTimeZone>",
                "expiration_date"           : "02/13/2030, 00:00 AM",
                "expiration_date_timezone"  : "<randomTimeZone>",
                "tags_+"                    : True,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : True,
                "button_upload_expectedres" : "Key [<keyname>] has been successfully uploaded to <instanceName> [Azure].",
                "button_cancel_2"           : False,
                "button_okay"               : True,
                "button_okay_expectedres"   : False,
                "checks"                    : False,
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_18",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-2048_<random>",
                "algorithm"                 : "RSA-2048",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName>_RSA-2048_<random>",
                "checkbox_enabled"          : False,
                "key_vault"                 : "TaaS-EKMaaS-QA-THO",
                "activation_date"           : "<currentDateTime>",
                "activation_date_timezone"  : "<randomTimeZone>",
                "expiration_date"           : "02/13/2030, 00:00 AM",
                "expiration_date_timezone"  : "<randomTimeZone>",
                "tags_+"                    : True,
                "tag_name"                  : "Tag-Name-<random>",
                "tag_value"                 : False,
                "button_upload"             : True,
                "button_upload_expectedres" : "Key [<keyname>] has been successfully uploaded to <instanceName> [Azure].",
                "button_cancel_2"           : False,
                "button_okay"               : True,
                "button_okay_expectedres"   : False,
                "checks"                    : False,
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_19",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-2048_<random>",
                "algorithm"                 : "RSA-2048",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName>_RSA-2048_<random>",
                "checkbox_enabled"          : False,
                "key_vault"                 : "TaaS-EKMaaS-QA-THO",
                "activation_date"           : "<currentDateTime>",
                "activation_date_timezone"  : "<randomTimeZone>",
                "expiration_date"           : "02/13/2030, 00:00 AM",
                "expiration_date_timezone"  : "<randomTimeZone>",
                "tags_+"                    : True,
                "tag_name"                  : False,
                "tag_value"                 : "Tag-Value-<random>",
                "button_upload"             : True,
                "button_upload_expectedres" : "Key [<keyname>] has been successfully uploaded to <instanceName> [Azure].",
                "button_cancel_2"           : False,
                "button_okay"               : True,
                "button_okay_expectedres"   : False,
                "checks"                    : False,
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_20",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-2048_<random>",
                "algorithm"                 : "RSA-2048",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName>_RSA-2048_<random>",
                "checkbox_enabled"          : False,
                "key_vault"                 : "TaaS-EKMaaS-QA-THO",
                "activation_date"           : "<currentDateTime>",
                "activation_date_timezone"  : "<randomTimeZone>",
                "expiration_date"           : "02/13/2030, 00:00 AM",
                "expiration_date_timezone"  : "<randomTimeZone>",
                "tags_+"                    : True,
                "tag_name"                  : "Tag-Name-<random>",
                "tag_value"                 : "Tag-Value-<random>",
                "button_upload"             : True,
                "button_upload_expectedres" : "Key [<keyname>] has been successfully uploaded to <instanceName> [Azure].",
                "button_cancel_2"           : False,
                "button_okay"               : True,
                "button_okay_expectedres"   : False,
                "checks"                    : False,
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_21",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-2048_<random>",
                "algorithm"                 : "RSA-2048",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName>_RSA-2048_<random>_new",
                "checkbox_enabled"          : True,
                "key_vault"                 : "TaaS-EKMaaS-QA-THO",
                "activation_date"           : "<currentDateTime>",
                "activation_date_timezone"  : "<randomTimeZone>",
                "expiration_date"           : "02/13/2030, 00:00 AM",
                "expiration_date_timezone"  : "<randomTimeZone>",
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : True,
                "button_upload_expectedres" : "Key [<keyname>] has been successfully uploaded to <instanceName> [Azure].",
                "button_cancel_2"           : False,
                "button_okay"               : True,
                "button_okay_expectedres"   : False,
                "checks"                    : "New Name",
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_22",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-2048_<random>",
                "algorithm"                 : "RSA-2048",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName>_RSA-2048_<random>_new",
                "checkbox_enabled"          : True,
                "key_vault"                 : "TaaS-EKMaaS-QA-THO",
                "activation_date"           : "<currentDateTime>",
                "activation_date_timezone"  : "<randomTimeZone>",
                "expiration_date"           : "02/13/2030, 00:00 AM",
                "expiration_date_timezone"  : "<randomTimeZone>",
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : True,
                "button_upload_expectedres" : "Key [<keyname>] has been successfully uploaded to <instanceName> [Azure].",
                "button_cancel_2"           : False,
                "button_okay"               : True,
                "button_okay_expectedres"   : False,
                "checks"                    : "New Name",
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_23",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : False,
                "algorithm"                 : False,
                "button_create"             : True,
                "button_create_expectedres" : "Missing 2 Items",
                "button_cancel_1"           : True,
                "button_next"               : False,
                "button_close"              : False,
                "cloud_key_name"            : False,
                "checkbox_enabled"          : False,
                "key_vault"                 : False,
                "activation_date"           : False,
                "activation_date_timezone"  : False,
                "expiration_date"           : False,
                "expiration_date_timezone"  : False,
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : False,
                "button_upload_expectedres" : False,
                "button_cancel_2"           : False,
                "button_okay"               : False,
                "button_okay_expectedres"   : False,
                "checks"                    : False,
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_24",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>__<random>",
                "algorithm"                 : False,
                "button_create"             : True,
                "button_create_expectedres" : "Missing 1 Item",
                "button_cancel_1"           : True,
                "button_next"               : False,
                "button_close"              : False,
                "cloud_key_name"            : False,
                "checkbox_enabled"          : False,
                "key_vault"                 : False,
                "activation_date"           : False,
                "activation_date_timezone"  : False,
                "expiration_date"           : False,
                "expiration_date_timezone"  : False,
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : False,
                "button_upload_expectedres" : False,
                "button_cancel_2"           : False,
                "button_okay"               : False,
                "button_okay_expectedres"   : False,
                "checks"                    : False,
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_25",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : False,
                "algorithm"                 : "RSA-2048",
                "button_create"             : False,
                "button_create_expectedres" : "Missing 1 Item",
                "button_cancel_1"           : True,
                "button_next"               : False,
                "button_close"              : False,
                "cloud_key_name"            : False,
                "checkbox_enabled"          : False,
                "key_vault"                 : False,
                "activation_date"           : False,
                "activation_date_timezone"  : False,
                "expiration_date"           : False,
                "expiration_date_timezone"  : False,
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : False,
                "button_upload_expectedres" : False,
                "button_cancel_2"           : False,
                "button_okay"               : False,
                "button_okay_expectedres"   : False,
                "checks"                    : False,
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_26",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-2048_<random>",
                "algorithm"                 : "RSA-2048",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : False,
                "checkbox_enabled"          : True,
                "key_vault"                 : False,
                "activation_date"           : False,
                "activation_date_timezone"  : False,
                "expiration_date"           : False,
                "expiration_date_timezone"  : False,
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : True,
                "button_upload_expectedres" : "Missing 2 items",
                "button_cancel_2"           : True,
                "button_okay"               : False,
                "button_okay_expectedres"   : False,
                "checks"                    : "Check Upload No",
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_27",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-2048_<random>",
                "algorithm"                 : "RSA-2048",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName>_RSA-2048_<random>_new",
                "checkbox_enabled"          : True,
                "key_vault"                 : False,
                "activation_date"           : False,
                "activation_date_timezone"  : False,
                "expiration_date"           : False,
                "expiration_date_timezone"  : False,
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : True,
                "button_upload_expectedres" : "Missing 1 Item",
                "button_cancel_2"           : True,
                "button_okay"               : False,
                "button_okay_expectedres"   : False,
                "checks"                    : "Check Upload No",
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_28",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-2048_<random>",
                "algorithm"                 : "RSA-2048",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : False,
                "checkbox_enabled"          : True,
                "key_vault"                 : "TaaS-EKMaaS-QA-THO",
                "activation_date"           : False,
                "activation_date_timezone"  : False,
                "expiration_date"           : False,
                "expiration_date_timezone"  : False,
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : True,
                "button_upload_expectedres" : "Missing 1 Item",
                "button_cancel_2"           : True,
                "button_okay"               : False,
                "button_okay_expectedres"   : False,
                "checks"                    : "Check Upload No",
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_29",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-2048_<random>",
                "algorithm"                 : "RSA-2048",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName>_RSA-2048_<random>",
                "checkbox_enabled"          : True,
                "key_vault"                 : "TaaS-EKMaaS-QA-THO",
                "activation_date"           : "<currentDateTime>",
                "activation_date_timezone"  : "<randomTimeZone>",
                "expiration_date"           : "02/13/2030, 00:00 AM",
                "expiration_date_timezone"  : "<randomTimeZone>",
                "tags_+"                    : 2,
                "tag_name"                  : "Tag-Name-<random>-1 Tag-Name-<random>-2",
                "tag_value"                 : "Tag-Value-<random>-1 Tag-Value-<random>-2",
                "button_upload"             : True,
                "button_upload_expectedres" : False,
                "button_cancel_2"           : False,
                "button_okay"               : True,
                "button_okay_expectedres"   : False,
                "checks"                    : "Sonderlocke 2 Tags",
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_30",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-2048_<random>",
                "algorithm"                 : "RSA-2048",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName>_RSA-2048_<random>",
                "checkbox_enabled"          : False,
                "key_vault"                 : "TaaS-EKMaaS-QA-THO",
                "activation_date"           : "<currentDateTime>",
                "activation_date_timezone"  : "<randomTimeZone>",
                "expiration_date"           : "02/13/2030, 00:00 AM",
                "expiration_date_timezone"  : "<randomTimeZone>",
                "tags_+"                    : 2,
                "tag_name"                  : "Tag-Name-<random>-1 Tag-Name-<random>-2",
                "tag_value"                 : "Tag-Value-<random>-1 Tag-Value-<random>-2",
                "button_upload"             : True,
                "button_upload_expectedres" : False,
                "button_cancel_2"           : False,
                "button_okay"               : True,
                "button_okay_expectedres"   : False,
                "checks"                    : "Sonderlocke 2 Tags",
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_31",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName_2vaults>_RSA-2048_<random>",#MOD
                "algorithm"                 : "RSA-2048",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName_2vaults>_RSA-2048_<random>", #MOD
                "checkbox_enabled"          : True,
                "key_vault"                 : "keyvault-ekmaas-test-003", #MOD
                "activation_date"           : "<currentDateTime>",
                "activation_date_timezone"  : "<randomTimeZone>",
                "expiration_date"           : "02/13/2030, 00:00 AM",
                "expiration_date_timezone"  : "<randomTimeZone>",
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : True,
                "button_upload_expectedres" : False,
                "button_cancel_2"           : False,
                "button_okay"               : True,
                "button_okay_expectedres"   : False,
                "checks"                    : "Sonderlocke 2 KeyVaults",
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_32",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName_2vaults>_RSA-2048_<random>", #MOD
                "algorithm"                 : "RSA-2048",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName_2vaults>_RSA-2048_<random>", #MOD
                "checkbox_enabled"          : True,
                "key_vault"                 : "keyvault-ekmaas-test-004", #MOD
                "activation_date"           : "<currentDateTime>",
                "activation_date_timezone"  : "<randomTimeZone>",
                "expiration_date"           : "02/13/2030, 00:00 AM",
                "expiration_date_timezone"  : "<randomTimeZone>",
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : True,
                "button_upload_expectedres" : False,
                "button_cancel_2"           : False,
                "button_okay"               : True,
                "button_okay_expectedres"   : False,
                "checks"                    : "Sonderlocke 2 KeyVaults",
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_33",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName_2vaults>_RSA-2048_<random>", #MOD
                "algorithm"                 : "RSA-2048",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName_2vaults>_RSA-2048_<random>", #MOD
                "checkbox_enabled"          : False,
                "key_vault"                 : "keyvault-ekmaas-test-003", #MOD
                "activation_date"           : "<currentDateTime>",
                "activation_date_timezone"  : "<randomTimeZone>",
                "expiration_date"           : "02/13/2030, 00:00 AM",
                "expiration_date_timezone"  : "<randomTimeZone>",
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : True,
                "button_upload_expectedres" : False,
                "button_cancel_2"           : False,
                "button_okay"               : True,
                "button_okay_expectedres"   : False,
                "checks"                    : "Sonderlocke 2 KeyVaults",
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_34",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName_2vaults>_RSA-2048_<random>", #MOD
                "algorithm"                 : "RSA-2048",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS", #MOD
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName_2vaults>_RSA-2048_<random>", #MOD
                "checkbox_enabled"          : False,
                "key_vault"                 : "keyvault-ekmaas-test-004", #MOD
                "activation_date"           : "<currentDateTime>",
                "activation_date_timezone"  : "<randomTimeZone>",
                "expiration_date"           : "02/13/2030, 00:00 AM",
                "expiration_date_timezone"  : "<randomTimeZone>",
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : True,
                "button_upload_expectedres" : False,
                "button_cancel_2"           : False,
                "button_okay"               : True,
                "button_okay_expectedres"   : False,
                "checks"                    : "Sonderlocke 2 KeyVaults",
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_35",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-2048_<random>",
                "algorithm"                 : "RSA-2048",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName>_RSA-2048_<random>",
                "checkbox_enabled"          : True,
                "key_vault"                 : "TaaS-EKMaaS-QA-THO",
                "activation_date"           : "02/13/2030, 00:00 AM",
                "activation_date_timezone"  : "Berlin",
                "expiration_date"           : "<currentDateTime>",
                "expiration_date_timezone"  : "Berlin",
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : True,
                "button_upload_expectedres" : False,
                "button_cancel_2"           : True,
                "button_okay"               : False,
                "button_okay_expectedres"   : "ERROR",
                "checks"                    : "Activation > Expired",
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_36",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-2048_<random>",
                "algorithm"                 : "RSA-2048",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName>_RSA-2048_<random>",
                "checkbox_enabled"          : True,
                "key_vault"                 : "TaaS-EKMaaS-QA-THO",
                "activation_date"           : "<SameDate>",
                "activation_date_timezone"  : "Berlin",
                "expiration_date"           : "<SameDate>",
                "expiration_date_timezone"  : "Berlin",
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : True,
                "button_upload_expectedres" : False,
                "button_cancel_2"           : True,
                "button_okay"               : False, # MOD
                "button_okay_expectedres"   : "ERROR",
                "checks"                    : "Activation == Expired",
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_37",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-2048_<random>",
                "algorithm"                 : "RSA-2048",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName>_RSA-2048_<random>",
                "checkbox_enabled"          : False,
                "key_vault"                 : "TaaS-EKMaaS-QA-THO",
                "activation_date"           : "02/13/2030, 00:00 AM",
                "activation_date_timezone"  : "Berlin",
                "expiration_date"           : "<currentDateTime>",
                "expiration_date_timezone"  : "Berlin",
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : True,
                "button_upload_expectedres" : False,
                "button_cancel_2"           : True,
                "button_okay"               : False,  # MOD
                "button_okay_expectedres"   : "ERROR",
                "checks"                    : "Activation > Expired",
            },
            {
                "ticket"                    : "KMAAS-96",
                "variant"                   : "Add_Key_38",
                "button_manage_keys"        : True,
                "button_create_upload_key"  : True,
                "key_name"                  : "<instanceName>_RSA-2048_<random>",
                "algorithm"                 : "RSA-2048",
                "button_create"             : True,
                "button_create_expectedres" : "Key created successfully on EKMaaS",
                "button_cancel_1"           : False,
                "button_next"               : True,
                "button_close"              : False,
                "cloud_key_name"            : "<instanceName>_RSA-2048_<random>",
                "checkbox_enabled"          : False,
                "key_vault"                 : "TaaS-EKMaaS-QA-THO",
                "activation_date"           : "<SameDate>",
                "activation_date_timezone"  : "Berlin",
                "expiration_date"           : "<SameDate>",
                "expiration_date_timezone"  : "Berlin",
                "tags_+"                    : False,
                "tag_name"                  : False,
                "tag_value"                 : False,
                "button_upload"             : True,
                "button_upload_expectedres" : False,
                "button_cancel_2"           : True,
                "button_okay"               : False,  # MOD
                "button_okay_expectedres"   : "ERROR",
                "checks"                    : "Activation == Expired",
            },
        ]

        variants_to_process = {
            # 1,  
            # 2,  
            # 3,  
            # 4,  
            # 5,  
            # 6,  
            # 7,  
            # 8,  
            # 9,  
            10,
            # 11, 
            # 12, 
            # 13, 
            # 14, 
            # 15, 
            # 16, 
            # 17, 
            # 18, 
            # 19, 
            # 20,
            # 21, 
            # 22, 
            # 23, 
            # 24, 
            # 25,
            # 26, # THIS GENERATES A KEY W/O ANY DATES    -   Bug-Ticket KMAAS-502
            # 27, # THIS GENERATES A KEY W/O ANY DATES    -   Bug-Ticket KMAAS-502
            # 28, # THIS GENERATES A KEY W/O ANY DATES    -   Bug-Ticket KMAAS-502
            # 29, # 2 Tags
            # 30, # 2 Tags
            # 31, # second vault
            # 32, # second vault
            # 33, # second vault
            # 34, # second vault
            # 35,
            # 36,
            # 37,
            # 38
        }
        
        teardown = False

        
        cuil.log_teststep("Perform multiple key creations defined in testmatrix")
        
        for variant in test_variants:
            
            # try:
            #     page.get_by_test_id("kmaas.pagination.itemsperpage").select_option("3: 50", timeout=5000)
            # except: pass
            
            # page.pause()
                
            name = variant["variant"]
            # only process if it ends with _<number> that’s in the set
            if any(name.endswith(f"_{num}") for num in variants_to_process):
                processed_variants.append(variant["variant"])
            else:
                continue
                
            time.sleep(1)

            logging.info(f"------ {variant["variant"]} ---------------------------------------------------------------------------------------------------------------")


            cuil.log_teststep("Click 'Cloud Clients' -> 'Azure'")
            cuil.click_main_menu_item ({'Cloud Clients':'Azure'})

            if variant["checks"] == "Sonderlocke 2 KeyVaults":
                cuil.log_teststep("Click “Manage Keys” in Azure row of 2-VAULTS cloud instance")
                pom.click_cloud_instance_manage_keys(pom.instance_name2vaults)
            else:
                cuil.log_teststep("Click “Manage Keys” in Azure row of 1-VAULT cloud instance")
                pom.click_cloud_instance_manage_keys(pom.instance_name)
                

            logging.info("Click Button 'Create Instance'")
            pom.click_create_upload_key_button()

            # -------------------------------------------------------------------------------------------------------------

            # Generate a key name from template
            keyname = pom.make_keyname(variant["key_name"])

            # get algo position in selectbox
            value_mapping = { "RSA-2048": 1, "RSA-3072": 2, "RSA-4096": 3 }
            algorithm = variant.get("algorithm")
            try:
                algo = value_mapping[algorithm]
            except:
                algo = False
                

            # fill these into popup
            logging.info("Fill Fields")
            pom.fill_key_fields(keyname,algo)

            if variant["button_create"]:
                logging.info("Click Button 'Create'")
                time.sleep(1)
                pom.click_create_button()
                time.sleep(1)
                pom.expectedres(variant["button_create_expectedres"])

            if variant["button_cancel_1"]:
                logging.info("Click Button 'Cancel'")
                pom.click_cancel_button()
                # page.pause()
                
                # Check expected Result
                if variant["checks"]:
                    logging.info("Check expected Result")
                    pom.performCheck(variant["checks"],keyname)

                continue

            # -------------------------------------------------------------------------------------------------------------

            if variant["button_next"]:
                logging.info("Click Button 'Next'")
                pom.click_next_button()

            # -------------------------------------------------------------------------------------------------------------

            # Enable the key
            if variant["checkbox_enabled"]:
                logging.info("Click Checkbox 'Enabled'")
                pom.tick_key_enable_checkbox()

            # -------------------------------------------------------------------------------------------------------------

            # Select the key vault
            if variant["key_vault"]:
                logging.info(f"Select the key vault '{variant["key_vault"]}'")
                pom.select_key_vault_name(variant["key_vault"])

            # -------------------------------------------------------------------------------------------------------------

            # Set validity dates
            logging.info(f"Set validity dates {variant["activation_date"]} / {variant["expiration_date"]}")
            pom.fill_validity_dates(
                activation_date=variant["activation_date"],
                expiration_date=variant["expiration_date"],
            )

            # -------------------------------------------------------------------------------------------------------------
        
            # Add Tag(s)
            num_tags   = variant.get("tags_+", 0)
            
            if num_tags:
                
                # split incoming strings into parallel lists
                
                # if tag_name/key is missing you get "", if it’s present but None you still fallback to ""
                t_names  = variant.get("tag_name", "") or ""
                t_values = variant.get("tag_value", "") or ""

                # splitting an empty string safely returns []
                t_names  = t_names.split()  or [""]
                t_values = t_values.split() or [""]

                logging.debug(f"t_names:{t_names}")
                logging.debug(f"t_values:{t_values}")

                if len(t_names) != num_tags or len(t_values) != num_tags:
                    raise ValueError(
                        f"Expected {num_tags} names & values, "
                        f"got {len(t_names)} names and {len(t_values)} values"
                    )

                # zip into a single list of dicts, exactly like your collect_tag_pairs output
                tag_pairs = [
                    {"name": name, "value": value}
                    for name, value in zip(t_names, t_values)
                ]

                # now collect the *replaced* values
                replaced_pairs = []
                for idx, pair in enumerate(tag_pairs):
                    logging.info(f"Add Tag {pair['name']!r} / {pair['value']!r}")
                    pom.click_add_tag()
                    replaced = pom.fill_tag_fields(
                        name=pair["name"],
                        value=pair["value"],
                        index=idx
                    )
                    replaced_pairs.append(replaced)

                # convert to dicts
                pom.replaced_pairs = [
                    {"name": name, "value": value}
                    for name, value in replaced_pairs
                ]                                

            # -------------------------------------------------------------------------------------------------------------
            
            # Upload Key
            if variant["button_upload"]:
                logging.info(f"Upload Key, Click Upload Button. We expect: '{variant["button_upload_expectedres"]}'")
                pom.click_upload_button()
                
                if variant["button_upload_expectedres"]:
                    ecpectedres = variant["button_upload_expectedres"]
                    ecpectedres = ecpectedres.replace("<keyname>", keyname)
                    ecpectedres = ecpectedres.replace("<instanceName>", pom.instance_name)
                    time.sleep(1)
                    pom.expectedres(ecpectedres)

            # -------------------------------------------------------------------------------------------------------------
            
            # Click Cancel
            if variant["button_cancel_2"]:
                logging.info(f"Upload Key, Click Cancel Button.")
                # pom.pause()
                pom.click_cancel_button()
                
                # Check expected Result
                if variant["checks"]:
                    logging.info("Check expected Result")
                    pom.performCheck(variant["checks"],keyname)
                    pom.delete_all_keys()
                    
                continue

            # -------------------------------------------------------------------------------------------------------------

            # # Click OK
            # if variant["button_okay"]:
            #     logging.info("Click Button 'OK'")
            #     pom.click_ok_button()
            #     logging.info("Check if Key is listed")
            #     pom.check_key_existence_in_list(keyname)

            # -------------------------------------------------------------------------------------------------------------

            # Check expected Result
            if variant["checks"]:
                logging.info("Check expected Result")
                pom.performCheck(variant["checks"],keyname)
            
            # -------------------------------------------------------------------------------------------------------------
            
            pom.delete_all_keys()
            
            
            
        pretty = pformat(processed_variants, indent=2)
        logging.info("Processed variants:\n%s", pretty)

        # pom.pause()

        # Teardown:
        if teardown:
            cuil.log_teststep("Teardown")
            pom.pause()   

            # pom.delete_all_keys()
            cuil.click_main_menu_item ({'Cloud Clients':'Azure'})
            # page.pause()
            pom.click_cloud_instance_delete_instance(pom.instance_name)
            #page.pause()
            pom.await_toaster(f"Cloud Instance {pom.instance_name} [Azure] deleted successfully")
        else:
            pass
            # pom.pause()   


    except Exception:
        pretty = pformat(processed_variants, indent=2)
        logging.info("Processed variants:\n%s", pretty)
        cuil.handle_exception(request)
    else: logging.info(f"TESTEND:PASS:{testDescription}")



