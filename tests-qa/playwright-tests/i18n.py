i18n = {
  "en": {
    "AddCloudInstance: title": "Add Cloud Instance",
    "AddCloudInstance: label field Instance Name": "* Instance Name",
    "AddCloudInstance: label field Cloud Type": "* Cloud Type",
    "AddCloudInstance: option field Select": "Select",
    "AddCloudInstance: Cancel": "Cancel",
    "Field Required Validation Msg": "Field Required",
    "Msg for Validation Check": "Value should be less than",
    "Value Comparison text Msg": "characters",
    "Validate multi-select or value-array input": "Field values must be unique",
    "Tenant ID label": "Tenant ID",
    "Client ID label": "Client ID",
    "Client Secret label": "Client Secret",
    "Key Vault label": "Key Vaults",
    "AddCloudInstance-Add": "Add",
    "AddCloudInstance-Verify": "Verify",
    "AddCloudInstance-New Cloud Instance Validation Msg": "Field can only contain letters, numbers, hyphens, underscores, and periods",
    "CloudListing: Button text for Add Cloud Instance": "Add Cloud Instance",
    "CloudListing: Button text for Manage Keys": "Manage Keys",
    "CloudListing: Button text for Delete Instance": "Delete",
    "CloudListing: Text for No Cloud Instances": "No Cloud Instances Available",
    "CloudListing: Title for Delete Instance": "Delete Cloud Instance",
    "CloudListing: Confirmation text for Delete Instance": "Are you sure you want to delete instance",
    "Edit Cloud Instance: Title": "Edit Cloud Instance",
    "Edit Cloud Instance: label field for Key URI": "URL",
    "Edit Cloud Instance: text for not editable Instance": "Note: This instance is not editable.",
    "EditCloudInstance-button text": "Verify",
    "EditCloudInstance-delete instance warn": "Warning: Instance will be deleted",
    "EditCloudInstance-button text for update": "Update",
    "EditCloudInstance-warning on update inputs": "Warning: Instance will be deleted if no service account are present",
    "Manage Cloud Keys: label for Cloud Instance": "Cloud Instance:",
    "Manage Cloud Keys: button text for Create/Upload key": "Create/Upload Key",
    "Manage Cloud Keys: button text for Create key": "Create Key",
    "Manage Cloud Keys: No keys available text": "No Keys Available",
    "Manage Cloud Keys-KeyDetails: label for Key Name": "Name",
    "Manage Cloud Keys: label for Key URI": "Key URI",
    "Manage Cloud Keys: label for DKE Key URL": "DKE Key URL",
    "Manage Cloud Keys: label for Key Algorithm": "Key Algorithm",
    "Manage Cloud Keys: button text for Close": "Close",
    "Manage Cloud Keys: button text for Yes": "Yes",
    "Manage Cloud Key-Header for confirmation box": "Alert!",
    "Manage Cloud Keys-dropdown label for Select": "Select",
    "Manage Cloud Keys-dropdown label for Edit": "Edit",
    "Manage Cloud Keys-dropdown label for Upload": "Upload",
    "Enabled field": "Enabled",
    "KeyVault field": "Key Vault",
    "Activatin Date field": "Activation Date",
    "Expiration Date field": "Expiration Date",
    "Tags field": "Tags",
    "Manage Cloud Keys": "to",
    "Manage Cloud Keys-Note on deleted keys": "Note: Key will be deleted only from <ns0:ph xmlns:ns0=\"urn:oasis:names:tc:xliff:document:2.0\" id=\"0\" equiv=\"INTERPOLATION\" disp=\"this.selectedCloudType.name\" />",
    "Reupload Key: label for Cloud Key Name": "*Cloud Key Name",
    "Reupload Key: label for EKMaas Key Name": "*EKMaaS Key Name",
    "ReUpload-Key: button text for Upload": "Upload",
    "AddKmipClient:button label for confirm": "Save",
    "ReUpload-Key: button text for Okay": "Okay",
    "ReUpload Key-Header for confirmation box": "Alert!",
    "ReUpload Key-Confirmation Header": "Success!",
    "ReUpload Key-Confirmation box content": "Are you sure you want to create a new version for",
    "Stepper-View: label for Select key": "Select Key",
    "Stepper-View: label for key owner": "Key Owner",
    "Stepper-View: label for key name": "*Key Name",
    "Stepper-View: label for algorithm": "*Algorithm",
    "Stepper-View-FinalUpload: label for EKMaaS Key Name": "EKMaaS Key Name",
    "Stepper-View-Key Selection label for events in stepper view": "Key Selection",
    "Stepper-View-Summary label for events in stepper view": "Summary",
    "Stepper-View-Upload Key label for events in stepper view": "Upload Key",
    "Stepper-View-Create key Name for radio button in stepper view": "Create Key & Upload",
    "Stepper-View-Existing Key Name for radio button in stepper view": "Select Existing Key",
    "Stepper-View-Header for confirmation box": "Alert!",
    "Stepper-View-Msg for Validation Success": "Key created successfully on EKMaaS",
    "Stepper-View-Search Key Warning Msg": "Warning: This key is already uploaded to the cloud instance",
    "Stepper-View-Confirmation Header": "Success!",
    "Stepper-View-Warning text": "Warning:",
    "Stepper-View-Key Form Validity Msg text": "Key is owned by",
    "Stepper-View-Service Account Validation Msg": "Warning: Instance will be deleted if no service account are present",
    "Stepper-View-Invalid Datetime validation Msg": "Invalid datetime value",
    "Upload Key: Header": "Upload to <ns0:ph xmlns:ns0=\"urn:oasis:names:tc:xliff:document:2.0\" id=\"0\" equiv=\"INTERPOLATION\" disp=\"{{cloudInstance.instanceName}}\" />  <ns0:ph xmlns:ns0=\"urn:oasis:names:tc:xliff:document:2.0\" id=\"1\" equiv=\"TAG_IMG\" type=\"image\" disp=\"&lt;img src=&quot;assets/icons/{{cloudInstance.cloudType}}.svg&quot; class=&quot;rowIcon btnPadding&quot; alt=&quot;type&quot; /&gt;\" />  <ns0:ph xmlns:ns0=\"urn:oasis:names:tc:xliff:document:2.0\" id=\"2\" equiv=\"INTERPOLATION_1\" disp=\"{{' '+cloudInstance.cloudType}}\" />",
    "Shared-Input: timezone text": "<ns0:ph xmlns:ns0=\"urn:oasis:names:tc:xliff:document:2.0\" id=\"0\" equiv=\"INTERPOLATION\" disp=\"{{getLocalizedDateTimeLabel(inputField)}}\" /> Timezone Timezone",
    "SharedInput-AddCloudInstance": "Specify comma separated values for multiple key vault",
    "SharedInput-ReUploadKey": "Expiration Date",
    "CloudListing: Button text for Edit Instance": "Edit Instance",
    "Manage Cloud Keys: Warning text showing key deltion is irreversible": "♦ Warning! This action will delete the key from",
    "Stepper-View: button text for Cancel": "",
    "Stepper-View: button text for Next": "Next",
    "Stepper-View: button text for Create": "Create",
    "Stepper-View: button text for Close": "Close",
    "Stepper-View: button text for Upload": "",
    "Stepper-View-Salesforce: button text for Upload": "Upload",
    "Stepper-View: button text for Okay": "Okay",
    "main-menu home link": "Dashboard",
    "AddkmipClient: label field for Client Id": "Client ID",
    "AddkmipClient: label field for Client Id Validation Error": "Client ID is required",
    "AddkmipClient: label field for Password": "Password",
    "AddkmipClient: label field for Password Validation Error": "Password is required and must be at least 12 characters",
    "AddkmipClient: label field for Confirm Password": "Confirm Password",
    "AddkmipClient: label field for Confirm Password Validation Error": "Please confirm your password",
    "AddkmipClient: label field for Password Mismatch Validation Error": "Passwords do not match",
    "AddkmipClient: label field for Client Certificate": "Client Certificate",
    "AddkmipClient: label field for Client Certificate Validation Error": "Client Certificate is required",
    "AddkmipClient: label field for Client Certificate Note": "Note: Client Id and Common Name in the Client Certificate should match",
    "AddKmipClient: Button text for Add Kmip Client": "Add KMIP Client",
    "ViewkmipClient: label field for Created By": "Created By",
    "ViewkmipClient: label field for Last Modified": "Last Modified",
    "ViewkmipClient: label field for Certificate Start Date": "Certificate Start Date",
    "UpdatekmipClient: label field for New Certificate Entry": "New Certificate",
    "UpdatekmipClient: label field for New Password Entry": "New Password",
    "UpdatekmipClient: Error message for Password Validation": "Password must be at least 8 characters long",
    "UpdatekmipClient: Error message for Confirm Password Validation": "Confirm Password is required"
  },
  "se": {
    "AddCloudInstance: title": "Add Cloud Instance",
    "AddCloudInstance: label field Instance Name": "* Instance Name",
    "AddCloudInstance: label field Cloud Type": "* Cloud Type",
    "AddCloudInstance: option field Select": "Select",
    "AddCloudInstance: Cancel": "Cancel",
    "Field Required Validation Msg": "Field Required",
    "Msg for Validation Check": "Value should be less than",
    "Value Comparison text Msg": "characters",
    "Validate multi-select or value-array input": "Field values must be unique",
    "Tenant ID label": "Tenant ID",
    "Client ID label": "Client ID",
    "Client Secret label": "Client Secret",
    "Key Vault label": "Key Vaults",
    "AddCloudInstance-Add": "Add",
    "AddCloudInstance-Verify": "Verify",
    "AddCloudInstance-New Cloud Instance Validation Msg": "Field can only contain letters, numbers, hyphens, underscores, and periods",
    "CloudListing: Button text for Add Cloud Instance": "Add Cloud Instance",
    "CloudListing: Button text for Manage Keys": "Manage Keys",
    "CloudListing: Button text for Delete Instance": "Delete",
    "CloudListing: Text for No Cloud Instances": "No Cloud Instances Available",
    "CloudListing: Title for Delete Instance": "Delete Cloud Instance",
    "CloudListing: Confirmation text for Delete Instance": "Are you sure you want to delete instance",
    "Edit Cloud Instance: Title": "Edit Cloud Instance",
    "Edit Cloud Instance: label field for Key URI": "URL",
    "Edit Cloud Instance: text for not editable Instance": "Note: This instance is not editable.",
    "EditCloudInstance-button text": "Verify",
    "EditCloudInstance-delete instance warn": "Warning: Instance will be deleted",
    "EditCloudInstance-button text for update": "Update",
    "EditCloudInstance-warning on update inputs": "Warning: Instance will be deleted if no service account are present",
    "Manage Cloud Keys: label for Cloud Instance": "Cloud Instance:",
    "Manage Cloud Keys: button text for Create/Upload key": "Create/Upload Key",
    "Manage Cloud Keys: button text for Create key": "Create Key",
    "Manage Cloud Keys: No keys available text": "No Keys Available",
    "Manage Cloud Keys-KeyDetails: label for Key Name": "Name",
    "Manage Cloud Keys: label for Key URI": "Key URI",
    "Manage Cloud Keys: label for DKE Key URL": "DKE Key URL",
    "Manage Cloud Keys: label for Key Algorithm": "Key Algorithm",
    "Manage Cloud Keys: button text for Close": "Close",
    "Manage Cloud Keys: button text for Yes": "Yes",
    "Manage Cloud Key-Header for confirmation box": "Alert!",
    "Manage Cloud Keys-dropdown label for Select": "Select",
    "Manage Cloud Keys-dropdown label for Edit": "Edit",
    "Manage Cloud Keys-dropdown label for Upload": "Upload",
    "Enabled field": "Enabled",
    "KeyVault field": "Key Vault",
    "Activatin Date field": "Activation Date",
    "Expiration Date field": "Expiration Date",
    "Tags field": "Tags",
    "Manage Cloud Keys": "to",
    "Manage Cloud Keys-Note on deleted keys": "Note: Key will be deleted only from <ns0:ph xmlns:ns0=\"urn:oasis:names:tc:xliff:document:2.0\" id=\"0\" equiv=\"INTERPOLATION\" disp=\"this.selectedCloudType.name\" />",
    "Reupload Key: label for Cloud Key Name": "*Cloud Key Name",
    "Reupload Key: label for EKMaas Key Name": "*EKMaaS Key Name",
    "ReUpload-Key: button text for Upload": "Upload",
    "AddKmipClient:button label for confirm": "Save",
    "ReUpload-Key: button text for Okay": "Okay",
    "ReUpload Key-Header for confirmation box": "Alert!",
    "ReUpload Key-Confirmation Header": "Success!",
    "ReUpload Key-Confirmation box content": "Are you sure you want to create a new version for",
    "Stepper-View: label for Select key": "Select Key",
    "Stepper-View: label for key owner": "Key Owner",
    "Stepper-View: label for key name": "*Key Name",
    "Stepper-View: label for algorithm": "*Algorithm",
    "Stepper-View-FinalUpload: label for EKMaaS Key Name": "EKMaaS Key Name",
    "Stepper-View-Key Selection label for events in stepper view": "Key Selection",
    "Stepper-View-Summary label for events in stepper view": "Summary",
    "Stepper-View-Upload Key label for events in stepper view": "Upload Key",
    "Stepper-View-Create key Name for radio button in stepper view": "Create Key & Upload",
    "Stepper-View-Existing Key Name for radio button in stepper view": "Select Existing Key",
    "Stepper-View-Header for confirmation box": "Alert!",
    "Stepper-View-Msg for Validation Success": "Key created successfully on EKMaaS",
    "Stepper-View-Search Key Warning Msg": "Warning: This key is already uploaded to the cloud instance",
    "Stepper-View-Confirmation Header": "Success!",
    "Stepper-View-Warning text": "Warning:",
    "Stepper-View-Key Form Validity Msg text": "Key is owned by",
    "Stepper-View-Service Account Validation Msg": "Warning: Instance will be deleted if no service account are present",
    "Stepper-View-Invalid Datetime validation Msg": "Invalid datetime value",
    "Upload Key: Header": "Upload to <ns0:ph xmlns:ns0=\"urn:oasis:names:tc:xliff:document:2.0\" id=\"0\" equiv=\"INTERPOLATION\" disp=\"{{cloudInstance.instanceName}}\" />  <ns0:ph xmlns:ns0=\"urn:oasis:names:tc:xliff:document:2.0\" id=\"1\" equiv=\"TAG_IMG\" type=\"image\" disp=\"&lt;img src=&quot;assets/icons/{{cloudInstance.cloudType}}.svg&quot; class=&quot;rowIcon btnPadding&quot; alt=&quot;type&quot; /&gt;\" />  <ns0:ph xmlns:ns0=\"urn:oasis:names:tc:xliff:document:2.0\" id=\"2\" equiv=\"INTERPOLATION_1\" disp=\"{{' '+cloudInstance.cloudType}}\" />",
    "Shared-Input: timezone text": "<ns0:ph xmlns:ns0=\"urn:oasis:names:tc:xliff:document:2.0\" id=\"0\" equiv=\"INTERPOLATION\" disp=\"{{getLocalizedDateTimeLabel(inputField)}}\" /> Timezone Timezone",
    "SharedInput-AddCloudInstance": "Specify comma separated values for multiple key vault",
    "SharedInput-ReUploadKey": "Expiration Date",
    "CloudListing: Button text for Edit Instance": "Edit Instance",
    "Manage Cloud Keys: Warning text showing key deltion is irreversible": "♦ Warning! This action will delete the key from",
    "Stepper-View: button text for Cancel": "",
    "Stepper-View: button text for Next": "Next",
    "Stepper-View: button text for Create": "Create",
    "Stepper-View: button text for Close": "Close",
    "Stepper-View: button text for Upload": "",
    "Stepper-View-Salesforce: button text for Upload": "Upload",
    "Stepper-View: button text for Okay": "Okay",
    "main-menu home link": "Dashboard",
    "AddkmipClient: label field for Client Id": "Client ID",
    "AddkmipClient: label field for Client Id Validation Error": "Client ID is required",
    "AddkmipClient: label field for Password": "Password",
    "AddkmipClient: label field for Password Validation Error": "Password is required and must be at least 12 characters",
    "AddkmipClient: label field for Confirm Password": "Confirm Password",
    "AddkmipClient: label field for Confirm Password Validation Error": "Please confirm your password",
    "AddkmipClient: label field for Password Mismatch Validation Error": "Passwords do not match",
    "AddkmipClient: label field for Client Certificate": "Client Certificate",
    "AddkmipClient: label field for Client Certificate Validation Error": "Client Certificate is required",
    "AddkmipClient: label field for Client Certificate Note": "Note: Client Id and Common Name in the Client Certificate should match",
    "AddKmipClient: Button text for Add Kmip Client": "Add KMIP Client",
    "ViewkmipClient: label field for Created By": "Created By",
    "ViewkmipClient: label field for Last Modified": "Last Modified",
    "ViewkmipClient: label field for Certificate Start Date": "Certificate Start Date",
    "UpdatekmipClient: label field for New Certificate Entry": "New Certificate",
    "UpdatekmipClient: label field for New Password Entry": "New Password",
    "UpdatekmipClient: Error message for Password Validation": "Password must be at least 8 characters long",
    "UpdatekmipClient: Error message for Confirm Password Validation": "Confirm Password is required"
  },
  "es": {
    "AddCloudInstance: title": "Add Cloud Instance",
    "AddCloudInstance: label field Instance Name": "* Instance Name",
    "AddCloudInstance: label field Cloud Type": "* Cloud Type",
    "AddCloudInstance: option field Select": "Select",
    "AddCloudInstance: Cancel": "Cancel",
    "Field Required Validation Msg": "Field Required",
    "Msg for Validation Check": "Value should be less than",
    "Value Comparison text Msg": "characters",
    "Validate multi-select or value-array input": "Field values must be unique",
    "Tenant ID label": "Tenant ID",
    "Client ID label": "Client ID",
    "Client Secret label": "Client Secret",
    "Key Vault label": "Key Vaults",
    "AddCloudInstance-Add": "Add",
    "AddCloudInstance-Verify": "Verify",
    "AddCloudInstance-New Cloud Instance Validation Msg": "Field can only contain letters, numbers, hyphens, underscores, and periods",
    "CloudListing: Button text for Add Cloud Instance": "Add Cloud Instance",
    "CloudListing: Button text for Manage Keys": "Manage Keys",
    "CloudListing: Button text for Delete Instance": "Delete",
    "CloudListing: Text for No Cloud Instances": "No Cloud Instances Available",
    "CloudListing: Title for Delete Instance": "Delete Cloud Instance",
    "CloudListing: Confirmation text for Delete Instance": "Are you sure you want to delete instance",
    "Edit Cloud Instance: Title": "Edit Cloud Instance",
    "Edit Cloud Instance: label field for Key URI": "URL",
    "Edit Cloud Instance: text for not editable Instance": "Note: This instance is not editable.",
    "EditCloudInstance-button text": "Verify",
    "EditCloudInstance-delete instance warn": "Warning: Instance will be deleted",
    "EditCloudInstance-button text for update": "Update",
    "EditCloudInstance-warning on update inputs": "Warning: Instance will be deleted if no service account are present",
    "Manage Cloud Keys: label for Cloud Instance": "Cloud Instance:",
    "Manage Cloud Keys: button text for Create/Upload key": "Create/Upload Key",
    "Manage Cloud Keys: button text for Create key": "Create Key",
    "Manage Cloud Keys: No keys available text": "No Keys Available",
    "Manage Cloud Keys-KeyDetails: label for Key Name": "Name",
    "Manage Cloud Keys: label for Key URI": "Key URI",
    "Manage Cloud Keys: label for DKE Key URL": "DKE Key URL",
    "Manage Cloud Keys: label for Key Algorithm": "Key Algorithm",
    "Manage Cloud Keys: button text for Close": "Close",
    "Manage Cloud Keys: button text for Yes": "Yes",
    "Manage Cloud Key-Header for confirmation box": "Alert!",
    "Manage Cloud Keys-dropdown label for Select": "Select",
    "Manage Cloud Keys-dropdown label for Edit": "Edit",
    "Manage Cloud Keys-dropdown label for Upload": "Upload",
    "Enabled field": "Enabled",
    "KeyVault field": "Key Vault",
    "Activatin Date field": "Activation Date",
    "Expiration Date field": "Expiration Date",
    "Tags field": "Tags",
    "Manage Cloud Keys": "to",
    "Manage Cloud Keys-Note on deleted keys": "Note: Key will be deleted only from <ns0:ph xmlns:ns0=\"urn:oasis:names:tc:xliff:document:2.0\" id=\"0\" equiv=\"INTERPOLATION\" disp=\"this.selectedCloudType.name\" />",
    "Reupload Key: label for Cloud Key Name": "*Cloud Key Name",
    "Reupload Key: label for EKMaas Key Name": "*EKMaaS Key Name",
    "ReUpload-Key: button text for Upload": "Upload",
    "AddKmipClient:button label for confirm": "Save",
    "ReUpload-Key: button text for Okay": "Okay",
    "ReUpload Key-Header for confirmation box": "Alert!",
    "ReUpload Key-Confirmation Header": "Success!",
    "ReUpload Key-Confirmation box content": "Are you sure you want to create a new version for",
    "Stepper-View: label for Select key": "Select Key",
    "Stepper-View: label for key owner": "Key Owner",
    "Stepper-View: label for key name": "*Key Name",
    "Stepper-View: label for algorithm": "*Algorithm",
    "Stepper-View-FinalUpload: label for EKMaaS Key Name": "EKMaaS Key Name",
    "Stepper-View-Key Selection label for events in stepper view": "Key Selection",
    "Stepper-View-Summary label for events in stepper view": "Summary",
    "Stepper-View-Upload Key label for events in stepper view": "Upload Key",
    "Stepper-View-Create key Name for radio button in stepper view": "Create Key & Upload",
    "Stepper-View-Existing Key Name for radio button in stepper view": "Select Existing Key",
    "Stepper-View-Header for confirmation box": "Alert!",
    "Stepper-View-Msg for Validation Success": "Key created successfully on EKMaaS",
    "Stepper-View-Search Key Warning Msg": "Warning: This key is already uploaded to the cloud instance",
    "Stepper-View-Confirmation Header": "Success!",
    "Stepper-View-Warning text": "Warning:",
    "Stepper-View-Key Form Validity Msg text": "Key is owned by",
    "Stepper-View-Service Account Validation Msg": "Warning: Instance will be deleted if no service account are present",
    "Stepper-View-Invalid Datetime validation Msg": "Invalid datetime value",
    "Upload Key: Header": "Upload to <ns0:ph xmlns:ns0=\"urn:oasis:names:tc:xliff:document:2.0\" id=\"0\" equiv=\"INTERPOLATION\" disp=\"{{cloudInstance.instanceName}}\" />  <ns0:ph xmlns:ns0=\"urn:oasis:names:tc:xliff:document:2.0\" id=\"1\" equiv=\"TAG_IMG\" type=\"image\" disp=\"&lt;img src=&quot;assets/icons/{{cloudInstance.cloudType}}.svg&quot; class=&quot;rowIcon btnPadding&quot; alt=&quot;type&quot; /&gt;\" />  <ns0:ph xmlns:ns0=\"urn:oasis:names:tc:xliff:document:2.0\" id=\"2\" equiv=\"INTERPOLATION_1\" disp=\"{{' '+cloudInstance.cloudType}}\" />",
    "Shared-Input: timezone text": "<ns0:ph xmlns:ns0=\"urn:oasis:names:tc:xliff:document:2.0\" id=\"0\" equiv=\"INTERPOLATION\" disp=\"{{getLocalizedDateTimeLabel(inputField)}}\" /> Timezone Timezone",
    "SharedInput-AddCloudInstance": "Specify comma separated values for multiple key vault",
    "SharedInput-ReUploadKey": "Expiration Date",
    "CloudListing: Button text for Edit Instance": "Edit Instance",
    "Manage Cloud Keys: Warning text showing key deltion is irreversible": "♦ Warning! This action will delete the key from",
    "Stepper-View: button text for Cancel": "",
    "Stepper-View: button text for Next": "Next",
    "Stepper-View: button text for Create": "Create",
    "Stepper-View: button text for Close": "Close",
    "Stepper-View: button text for Upload": "",
    "Stepper-View-Salesforce: button text for Upload": "Upload",
    "Stepper-View: button text for Okay": "Okay",
    "main-menu home link": "Dashboard",
    "AddkmipClient: label field for Client Id": "Client ID",
    "AddkmipClient: label field for Client Id Validation Error": "Client ID is required",
    "AddkmipClient: label field for Password": "Password",
    "AddkmipClient: label field for Password Validation Error": "Password is required and must be at least 12 characters",
    "AddkmipClient: label field for Confirm Password": "Confirm Password",
    "AddkmipClient: label field for Confirm Password Validation Error": "Please confirm your password",
    "AddkmipClient: label field for Password Mismatch Validation Error": "Passwords do not match",
    "AddkmipClient: label field for Client Certificate": "Client Certificate",
    "AddkmipClient: label field for Client Certificate Validation Error": "Client Certificate is required",
    "AddkmipClient: label field for Client Certificate Note": "Note: Client Id and Common Name in the Client Certificate should match",
    "AddKmipClient: Button text for Add Kmip Client": "Add KMIP Client",
    "ViewkmipClient: label field for Created By": "Created By",
    "ViewkmipClient: label field for Last Modified": "Last Modified",
    "ViewkmipClient: label field for Certificate Start Date": "Certificate Start Date",
    "UpdatekmipClient: label field for New Certificate Entry": "New Certificate",
    "UpdatekmipClient: label field for New Password Entry": "New Password",
    "UpdatekmipClient: Error message for Password Validation": "Password must be at least 8 characters long",
    "UpdatekmipClient: Error message for Confirm Password Validation": "Confirm Password is required"
  },
  "de": {
    "AddCloudInstance: title": "Cloud-Instanz hinzufügen",
    "AddCloudInstance: label field Instance Name": "*Instanzname",
    "AddCloudInstance: label field Cloud Type": "*Cloud-Typ",
    "AddCloudInstance: option field Select": "Wählen",
    "AddCloudInstance: Cancel": "Abbrechen",
    "Field Required Validation Msg": "Feld erforderlich",
    "Msg for Validation Check": "Der Wert sollte kleiner sein als",
    "Value Comparison text Msg": "Zeichen",
    "Validate multi-select or value-array input": "Feldwerte müssen eindeutig sein",
    "Tenant ID label": "Mandanten-ID",
    "Client ID label": "Kunden-ID",
    "Client Secret label": "Client-Geheimnis",
    "Key Vault label": "Schlüsseltresore",
    "AddCloudInstance-Add": "Hinzufügen",
    "AddCloudInstance-Verify": "Verifizieren",
    "AddCloudInstance-New Cloud Instance Validation Msg": "Das Feld darf nur Buchstaben, Zahlen, Bindestriche, Unterstriche und Punkte enthalten",
    "CloudListing: Button text for Add Cloud Instance": "Cloud-Instanz hinzufügen",
    "CloudListing: Button text for Manage Keys": "Schlüssel verwalten",
    "CloudListing: Button text for Delete Instance": "Löschen",
    "CloudListing: Text for No Cloud Instances": "Keine Cloud-Instanzen verfügbar",
    "CloudListing: Title for Delete Instance": "Cloud-Instanz löschen",
    "CloudListing: Confirmation text for Delete Instance": "Sind Sie sicher, dass Sie die Instanz löschen möchten?",
    "Edit Cloud Instance: Title": "Cloud-Instanz bearbeiten",
    "Edit Cloud Instance: label field for Key URI": "URL",
    "Edit Cloud Instance: text for not editable Instance": "Hinweis: Diese Instanz kann nicht bearbeitet werden.",
    "EditCloudInstance-button text": "Verifizieren",
    "EditCloudInstance-delete instance warn": "Warnung: Instanz wird gelöscht",
    "EditCloudInstance-button text for update": "Aktualisieren",
    "EditCloudInstance-warning on update inputs": "Warnung: Instanz wird gelöscht, sofern kein Service-Konto vorhanden ist",
    "Manage Cloud Keys: label for Cloud Instance": "Cloud-Instanz:",
    "Manage Cloud Keys: button text for Create/Upload key": "Schlüssel erstellen/hochladen",
    "Manage Cloud Keys: button text for Create key": "Schlüssel erstellen",
    "Manage Cloud Keys: No keys available text": "Keine Schlüssel vorhanden",
    "Manage Cloud Keys-KeyDetails: label for Key Name": "Name",
    "Manage Cloud Keys: label for Key URI": "Schlüssel-URI",
    "Manage Cloud Keys: label for DKE Key URL": "DKE Schlüssel-URL",
    "Manage Cloud Keys: label for Key Algorithm": "Schlüsselagorithmus",
    "Manage Cloud Keys: button text for Close": "Schließen",
    "Manage Cloud Keys: button text for Yes": "Ja",
    "Manage Cloud Key-Header for confirmation box": "Warnung!",
    "Manage Cloud Keys-dropdown label for Select": "Wählen",
    "Manage Cloud Keys-dropdown label for Edit": "Bearbeiten",
    "Manage Cloud Keys-dropdown label for Upload": "Hochladen",
    "Enabled field": "Aktiviert",
    "KeyVault field": "Schlüsseltresor",
    "Activatin Date field": "Aktivierungsdatum",
    "Expiration Date field": "Ablaufdatum",
    "Tags field": "Tags",
    "Manage Cloud Keys": "zu",
    "Manage Cloud Keys-Note on deleted keys": "Anmerkung: Der Schlüssel wird nur in <ns0:ph xmlns:ns0=\"urn:oasis:names:tc:xliff:document:2.0\" id=\"0\" equiv=\"INTERPOLATION\" disp=\"this.selectedCloudType.name\" /> gelöscht gelöscht",
    "Reupload Key: label for Cloud Key Name": "*Cloud Schlüsselname",
    "Reupload Key: label for EKMaas Key Name": "*EKMaaS Schlüsselname",
    "ReUpload-Key: button text for Upload": "Hochladen",
    "AddKmipClient:button label for confirm": "Speichern",
    "ReUpload-Key: button text for Okay": "OK",
    "ReUpload Key-Header for confirmation box": "Warnung!",
    "ReUpload Key-Confirmation Header": "Erfolg!",
    "ReUpload Key-Confirmation box content": "Sind Sie sicher, dass sie eine neue Version anlegen wollen für",
    "Stepper-View: label for Select key": "Schlüssel wählen",
    "Stepper-View: label for key owner": "Schlüsselbesitzer",
    "Stepper-View: label for key name": "*Schlüsselname",
    "Stepper-View: label for algorithm": "*Algorithmus",
    "Stepper-View-FinalUpload: label for EKMaaS Key Name": "EKMaaS Schlüsselname",
    "Stepper-View-Key Selection label for events in stepper view": "Schlüsselauswahl",
    "Stepper-View-Summary label for events in stepper view": "Zusammenfassung",
    "Stepper-View-Upload Key label for events in stepper view": "Schlüssel hochladen",
    "Stepper-View-Create key Name for radio button in stepper view": "Schlüssel erstellen & hochladen",
    "Stepper-View-Existing Key Name for radio button in stepper view": "Existierenden Schlüssel auswählen",
    "Stepper-View-Header for confirmation box": "Warnung!",
    "Stepper-View-Msg for Validation Success": "Schlüssel erfolgreich in EKMaaS erstellt",
    "Stepper-View-Search Key Warning Msg": "Warnung: Dieser Schlüssel wurde bereits in die Cloud-Instanze hochgeladen",
    "Stepper-View-Confirmation Header": "Erfolg!",
    "Stepper-View-Warning text": "Warnung:",
    "Stepper-View-Key Form Validity Msg text": "Schlüssel ist in Besitz von",
    "Stepper-View-Service Account Validation Msg": "Warnung: Instanz wird gelöscht, wenn kein Service-Konto existiert",
    "Stepper-View-Invalid Datetime validation Msg": "Ungültiger Datumswert",
    "Upload Key: Header": "Hochladen zu <ns0:ph xmlns:ns0=\"urn:oasis:names:tc:xliff:document:2.0\" id=\"0\" equiv=\"INTERPOLATION\" disp=\"{{cloudInstance.instanceName}}\" />  <ns0:ph xmlns:ns0=\"urn:oasis:names:tc:xliff:document:2.0\" id=\"1\" equiv=\"TAG_IMG\" type=\"image\" disp=\"&lt;img src=&quot;assets/icons/{{cloudInstance.cloudType}}.svg&quot; class=&quot;rowIcon btnPadding&quot; alt=&quot;type&quot; /&gt;\" />  <ns0:ph xmlns:ns0=\"urn:oasis:names:tc:xliff:document:2.0\" id=\"2\" equiv=\"INTERPOLATION_1\" disp=\"{{' '+cloudInstance.cloudType}}\" />",
    "Shared-Input: timezone text": "<ns0:ph xmlns:ns0=\"urn:oasis:names:tc:xliff:document:2.0\" id=\"0\" equiv=\"INTERPOLATION\" disp=\"{{getLocalizedDateTimeLabel(inputField)}}\" /> Zeitzone Zeitzone",
    "SharedInput-AddCloudInstance": "Werte für mehrere Schlüsseltresore mit Kommas trennen",
    "SharedInput-ReUploadKey": "Verfallsdatum",
    "CloudListing: Button text for Edit Instance": "Edit Instance",
    "Manage Cloud Keys: Warning text showing key deltion is irreversible": "♦ Warning! This action will delete the key from",
    "Stepper-View: button text for Cancel": "",
    "Stepper-View: button text for Next": "Next",
    "Stepper-View: button text for Create": "Create",
    "Stepper-View: button text for Close": "Close",
    "Stepper-View: button text for Upload": "",
    "Stepper-View-Salesforce: button text for Upload": "Upload",
    "Stepper-View: button text for Okay": "Okay",
    "main-menu home link": "Dashboard",
    "AddkmipClient: label field for Client Id": "Client ID",
    "AddkmipClient: label field for Client Id Validation Error": "Client ID is required",
    "AddkmipClient: label field for Password": "Password",
    "AddkmipClient: label field for Password Validation Error": "Password is required and must be at least 12 characters",
    "AddkmipClient: label field for Confirm Password": "Confirm Password",
    "AddkmipClient: label field for Confirm Password Validation Error": "Please confirm your password",
    "AddkmipClient: label field for Password Mismatch Validation Error": "Passwords do not match",
    "AddkmipClient: label field for Client Certificate": "Client Certificate",
    "AddkmipClient: label field for Client Certificate Validation Error": "Client Certificate is required",
    "AddkmipClient: label field for Client Certificate Note": "Note: Client Id and Common Name in the Client Certificate should match",
    "AddKmipClient: Button text for Add Kmip Client": "Add KMIP Client",
    "ViewkmipClient: label field for Created By": "Created By",
    "ViewkmipClient: label field for Last Modified": "Last Modified",
    "ViewkmipClient: label field for Certificate Start Date": "Certificate Start Date",
    "UpdatekmipClient: label field for New Certificate Entry": "New Certificate",
    "UpdatekmipClient: label field for New Password Entry": "New Password",
    "UpdatekmipClient: Error message for Password Validation": "Password must be at least 8 characters long",
    "UpdatekmipClient: Error message for Confirm Password Validation": "Confirm Password is required"
  }
}
