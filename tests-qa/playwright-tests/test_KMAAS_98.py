"""
Testcase:
login
click main Menu “Cloud Instances”
click “Add Cloud Instance”
enter valid instance name and credentials
select cloud type Azure
click verify
check if new instance appears in the table
try to delete Instance

JIRA Ticket: https://utimaco.atlassian.net/browse/KMAAS-98
"""

import logging, pytest, traceback
from pom import POM
from subclasses.common_ui_lib import TAASCUIL
from playwright.sync_api import expect

# Skip the entire file if PROD is True
@pytest.fixture(autouse=True, scope="module")
def skip_if_prod(PROD):
    if PROD:
        pytest.skip("Skipping tests in production environment")

def test_ts(
                                request,
                                getPage,
                                running_on,
                                getProjectVars,
    ):


    testID          = "KMAAS-98"
    testname        = "Create GUI for deleting Azure instance"
    testDescription = f"PlayWright-Test: '{testID}-{testname}'"
    status          = "ready"

    pom    = POM(getPage)
    cuil   = TAASCUIL(getPage)
    page   = pom.page
    pv     = getProjectVars

    QA_AZURE_TENANT_ID     = pv['QA_AZURE_TENANT_ID']
    QA_AZURE_CLIENT_ID     = pv['QA_AZURE_CLIENT_ID']
    QA_AZURE_CLIENT_SECRET = pv['QA_AZURE_CLIENT_SECRET']
    QA_AZURE_KEY_VAULT     = pv['key_vault_name']
    QA_MFA_SECRET          = pv['QA_MFA_SECRET' ]

    cloud_instance_name    = pv['cloud_instance_name']

    logging.info(testDescription)
    
    try:

        cuil.goto(pv['ci_url'] if running_on == "gitlab" else pv['dev_url'])

        cuil.log_teststep("Login")
        cuil.login (pv, QA_MFA_SECRET)

        cuil.isTitle  ("Enterprise Key Manager as a Service")

        cuil.log_teststep("Click 'Cloud Clients' -> 'Azure'")
        cuil.click_main_menu_item ({'Cloud Clients':'Azure'})

        # -------------------------------------------------------------------------------------------------------------

        page.pause()

        cuil.log_teststep("Click “Add Cloud Instance”")
        pom.click_add_cloud_instance()

        cuil.log_teststep("Check if all elements of the popup are present")
        pom.check_add_instance_popup_elements()

        cuil.log_teststep("Select Azure")
        pom.select_azure()

        # correct values
        cuil.log_teststep("Fill in cloud_instance_name, QA_AZURE_TENANT_ID, QA_AZURE_CLIENT_ID, QA_AZURE_CLIENT_SECRET, QA_AZURE_KEY_VAULT")
        pom.fill_instance_fields(
            cloud_instance_name,
            QA_AZURE_TENANT_ID,
            QA_AZURE_CLIENT_ID,
            QA_AZURE_CLIENT_SECRET,
            QA_AZURE_KEY_VAULT
        )

        cuil.log_teststep("Click “Add”")
        pom.click_add_button()      

        # -------------------------------------------------------------------------------------------------------------

        # check table
        cuil.log_teststep("Check new instance for existance")
        pom.check_instance_existence(cloud_instance_name)

        # -------------------------------------------------------------------------------------------------------------

        # delete instance
        cuil.log_teststep("Delete newly created instance for cleanup")
        pom.click_cloud_instance_delete_instance(cloud_instance_name)

        # -------------------------------------------------------------------------------------------------------------

    except Exception: cuil.handle_exception(request)
    else: logging.info(f"TESTEND:PASS:{testDescription}")
