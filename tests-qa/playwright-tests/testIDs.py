# POM:
 
 # DO
 
def click_add_button(self):
    self.page.get_by_role("button", name="Add", exact=True).click() 

def click_cancel_button(self):
    self.page.get_by_role("button", name="Cancel").click()

def click_close_button(self):
    self.page.get_by_role("button", name="Close").click()

def click_verify_button(self):
    self.page.get_by_role("button", name="Verify").click()
    
def click_next_button(self):
    self.page.get_by_role("button", name="Next", exact=True).click()

def click_ok_button(self):
    self.page.get_by_role("button", name="Okay").click()

def click_create_upload_key_button(self):
    self.page.get_by_role("button", name="Create/Upload Key").click()

def click_create_button(self):
    # click create button
    self.page.get_by_role("button", name="Create", exact=True).click()

def click_upload_button(self):
    self.page.get_by_role("button", name="Upload", exact=True).click()

def click_cloud_instance_manage_keys(self,cloud_instance_name):
    self.page.wait_for_selector(
        'td[data-testid="kmaas.row.id1.cell.keySource"], :has-text("no keys available")',
        state="visible"
    )

# no datatest-id helps here
def click_cloud_instance_edit_instance(self,cloud_instance_name):
    self.page.get_by_role("row", name=f"{cloud_instance_name} type Azure").get_by_role("button").nth(1).click()
    


# no datatest-id helps here
def check_add_instance_popup_elements(self):
    expect(self.page.get_by_text("Add Cloud Instance close")).to_be_visible()
    expect(self.page.get_by_text("close")).to_be_visible()
    expect(self.page.get_by_text("* Instance Name")).to_be_visible()
    expect(self.page.get_by_text("* Cloud Type")).to_be_visible()
    expect(self.page.get_by_role("button", name="Verify")).to_be_visible()
    expect(self.page.get_by_role("button", name="Cancel")).to_be_visible()
    expect(self.page.locator("#editCloudType")).to_be_visible()
    expect(self.page.locator("#addCloudInstanceName")).to_be_visible()


# no datatest-id helps here
def check_edit_instance_popup_elements(self):
    expect(self.page.get_by_text("close")).to_be_visible()
    expect(self.page.locator("#editCloudInstanceModal").get_by_text("Instance Name")).to_be_visible()
    expect(self.page.locator("#editCloudInstanceModal").get_by_text("Cloud Type")).to_be_visible()
    expect(self.page.get_by_text("Tenant ID")).to_be_visible()
    expect(self.page.get_by_text("Client ID")).to_be_visible()
    expect(self.page.get_by_text("Client Secret")).to_be_visible()
    expect(self.page.get_by_text("Key Vaults")).to_be_visible()
    expect(self.page.get_by_text("visibility")).to_be_visible()
    expect(self.page.get_by_role("button", name="Verify")).to_be_visible()
    expect(self.page.get_by_role("button", name="Cancel")).to_be_visible()
    
    
# no datatest-id helps here
def check_add_instance_azure_popup_elements(self):
    expect(self.page.get_by_text("Tenant ID")).to_be_visible()
    expect(self.page.get_by_text("Client ID")).to_be_visible()
    expect(self.page.get_by_text("Client Secret")).to_be_visible()
    expect(self.page.get_by_text("visibility")).to_be_visible()
    expect(self.page.get_by_text("Key Vaults")).to_be_visible()
    expect(self.page.locator("#input_keyVaults")).to_be_visible()
    
    
    
# no datatest-id helps here
def await_key_creation_success(self,keyname,cloud_instance_name):
    expect(self.page.get_by_text("Key created successfully on")).to_be_visible()
    expect(self.page.get_by_text(f"azure_{keyname}", exact=True)).to_be_visible()
    expect(self.page.get_by_text(f"azure_{cloud_instance_name}")).to_be_visible()
    expect(self.page.get_by_label("2Summary").get_by_text("RSA-")).to_be_visible()

    
def fill_instance_fields(
            self, 
            cloudInstanceName,
            tenantID,
            clientID,
            clientSecret,
            keyVaults,
            answer="Successfully connected to the cloud instance"
    ):
    self.page.get_by_role("button", name="Verify").click()
    
    
def fill_tag_fields(self, name, value, index):
    # inputs come in pairs 2,3 for idx=0 ; 4,5 for idx=1 ; 6,7 for idx=2 ; etc.
    base = 2 + index*2
    panel = self.page.get_by_role("tabpanel", name="Upload Key")
    if name:
        name = self.replace_random(name)
        panel.locator("input[type='text']").nth(base).fill(name)
    if value:
        value = self.replace_random(value)
        panel.locator("input[type='text']").nth(base+1).fill(value)
        
    return name,value


# no datatest-id helps here
def delete_key(self,keyname):
    self.page.get_by_role("row", name=f"{keyname}").locator("#manageCloudKeysAction").select_option("Delete")
    try:
        self.page.locator("#deleteFromCloudCheck").check()
        expect(self.page.get_by_text("Warning! This action will delete the key")).to_be_visible(timeout=10000)
    except:
        expect(self.page.get_by_text(f"Are you sure you want to delete key")).to_be_visible(timeout=10000)
    self.page.get_by_role("button", name="Yes").click()
    self.await_toaster("Cloud Instance key")


# no datatest-id helps here
def delete_first_key_without_name(self):
    self.page.locator("#manageCloudKeysAction").first.select_option("Delete")
    
    if self.page.locator("#deleteFromCloudCheck").is_visible():
        self.page.locator("#deleteFromCloudCheck").check()
        expect(self.page.get_by_text("Warning! This action will delete the key")).to_be_visible(timeout=10000)
    else:
        expect(self.page.get_by_text(f"Are you sure you want to delete key")).to_be_visible(timeout=10000)

    self.page.get_by_role("button", name="Yes").click()
    self.close_toaster()


# no datatest-id helps here
# as PRECONDITION from KMAAS-42
def precondition_create_cloud_instance(
        self, 
        cloudInstanceName,
        tenantID,
        clientID,
        clientSecret,
        keyVaults
):
            
    self.click_add_cloud_instance()
    self.select_azure()

    self.fill_instance_fields(
                cloudInstanceName,
                tenantID,
                clientID,
                clientSecret,
                keyVaults
    )

    self.click_add_button()
    self.await_toaster("[Azure] created successfully")
    self.close_toaster()
    
    
    
# as PRECONDITION from KMAAS-96
def precondition_create_key(
        self, 
        cloudInstanceName,
        keyname,
        keyVaults
):
    self.await_generic_message_feedback(f"Key created successfully on EKMaaS")
    self.await_generic_message_feedback("has been successfully uploaded")
    
    
    
def teardown_delete_cloud_instance(self,cuil,cloudInstanceName):
    cuil.click_main_menu_item ({'Cloud Clients':'Azure'})
    self.await_toaster(f"[Azure] deleted successfully.")

    
def click_filter_apply(self):
    self.page.locator("button.primary", has_text="Apply Filter").click()

def click_filter_clear(self):
    self.page.locator("button.primary", has_text="Clear Filter").click()

def click_filter_user(self):
    self.page.locator("mat-label", has_text="User").click()
    
    
def expectedres(self, response):
    
    if response == "Missing 2 Items":
        self.multiple_text_occurrences("Field Required",2)
        return
    if response == "Missing 1 Item":
        self.multiple_text_occurrences("Field Required",1)
        return
    
# --------------------------------------------------------------
        

    

# common IU lib:




















































































