
from pprint import pprint
from datetime import datetime, timedelta, timezone
import subprocess, json


class GRAFANALOG:

    def __init__(self, GRAFANA, APP, LIMIT, SCOPE, BACK) -> None:

        self.GRAFANA = GRAFANA
        self.APP     = APP
        self.LIMIT   = LIMIT
        self.SCOPE   = SCOPE
        self.QUERY   = "query={app="+'"'+self.APP+'"'+"}"
        self.BACK    = BACK


    def get_loki_time_range(self):
        now = datetime.now(timezone.utc)
        start = now - timedelta(minutes=self.BACK)
        fmt = "%Y-%m-%dT%H:%M:%S"
        return start.strftime(fmt), now.strftime(fmt)

    def run_curl_query(self):
        
        start_time, end_time = self.get_loki_time_range()
        
        curl_cmd = [
            "curl",
            "-v",
            "-k",        # allow insecure SSL (like -k in curl)
            "-G",        # GET with data-urlencode
            self.GRAFANA,
            "--data-urlencode", self.QUERY,
            "--data-urlencode", f"start={start_time}Z",         
            "--data-urlencode", f"end={end_time}Z",           
            "--data-urlencode", f"limit={self.LIMIT}",
            "-H", f"X-Scope-OrgID: {self.SCOPE}"
        ]
      
        result = subprocess.run(curl_cmd, capture_output=True, text=True)

        if result.returncode != 0:
            raise RuntimeError(f"curl failed: {result.stderr}")
        
        try:
            return json.loads(result.stdout)
        except json.JSONDecodeError:
            raise RuntimeError("Curl did not return valid JSON:\n" + result.stdout)


    def extract_log_lines(self, result_json):
        """Flatten and sort log lines from Loki JSON response by timestamp prefix."""
        streams = result_json.get("data", {}).get("result", [])
        lines = []

        for stream in streams:
            for _, logline in stream.get("values", []):
                lines.append(logline)

        # Sort by timestamp prefix: '2025-07-22 11:10:00.370'
        def parse_prefix(line):
            try:
                return datetime.strptime(line[:23], "%Y-%m-%d %H:%M:%S.%f")
            except ValueError:
                return datetime.min  # fallback to lowest time if malformed

        lines.sort(key=parse_prefix)
        return lines

    def main(self):
        
        result = self.run_curl_query()
        log_lines = self.extract_log_lines(result)
        error_lines = [line for line in log_lines if "ERROR" in line]
                       
        return (log_lines, error_lines)
        
        
if __name__ == "__main__":
    GL = GRAFANALOG()
    result = GL.main()
    
    for line in result[0]:
        print(line)

    print("\n\n")
    if result[1]:
        for line in result[1]:
            print(line)