import logging, pytest, traceback, time
from playwright.sync_api import expect
from pom import POM
from subclasses.common_ui_lib import TAASCUIL

# Skip the entire file if PROD is True
@pytest.fixture(autouse=True, scope="module")
def skip_if_prod(PROD):
    if PROD:
        pytest.skip("Skipping tests in production environment")

        
def test_ts(
                                request,
                                getPage,
                                running_on,
                                getProjectVars,
    ):

    testID          = "KMAAS-131"
    testname        = "Display Key Details"
    testDescription = f"PlayWright-Test: '{testID}-{testname}'"
    status          = "blocked"
    
    # blocked by https://u-serviceportal.atlassian.net/browse/KMAAS-447

    pom    = POM(getPage)
    cuil   = TAASCUIL(getPage)
    page   = pom.page
    pv     = getProjectVars

    pom.instance_name      = pv['cloud_instance_name']

    QA_MFA_SECRET          = pv['QA_MFA_SECRET' ]
    
    QA_AZURE_TENANT_ID     = pv['QA_AZURE_TENANT_ID']
    QA_AZURE_CLIENT_ID     = pv['QA_AZURE_CLIENT_ID']
    QA_AZURE_CLIENT_SECRET = pv['QA_AZURE_CLIENT_SECRET']
    QA_AZURE_KEY_VAULT     = pv['key_vault_name']


    try:
        logging.info(testDescription)

        cuil.goto(pv['ci_url'] if running_on == "gitlab" else pv['dev_url'])

        cuil.log_teststep("Login")
        cuil.login (pv, QA_MFA_SECRET)
        
        cuil.isTitle  ("Enterprise Key Manager as a Service")

        cuil.log_teststep("Click 'Cloud Clients' -> 'Azure'")
        cuil.click_main_menu_item ({'Cloud Clients':'Azure'})

        pom.pause()
        
        # -------------------------------------------------------------------------------------------------------------

        cuil.log_teststep("PRECONDITION: If NOT present we create cloud instance (like in https://utimaco.atlassian.net/browse/KMAAS-42)")
        # precondition: we need an azure instance
        if not page.get_by_role("cell", name=pom.instance_name, exact=True).is_visible():
            pom.precondition_create_cloud_instance(
                        pom.instance_name,
                        QA_AZURE_TENANT_ID,
                        QA_AZURE_CLIENT_ID,
                        QA_AZURE_CLIENT_SECRET,
                        QA_AZURE_KEY_VAULT
            )

        # -------------------------------------------------------------------------------------------------------------

        keyname = pom.make_keyname("<instanceName>_RSA-2048_<random>")

        cuil.log_teststep("PRECONDITION: Create Key (as in https://utimaco.atlassian.net/browse/KMAAS-96)")
        pom.precondition_create_key(
                    pom.instance_name,
                    keyname,
                    QA_AZURE_KEY_VAULT
        )

        # -------------------------------------------------------------------------------------------------------------

        cuil.log_teststep("Click on table cell of key to view details")
        page.get_by_role("cell", name=keyname, exact=True).click()
        
        pom.pause()

        # -------------------------------------------------------------------------------------------------------------

        cuil.log_teststep("Check the contents of the popup")

        expect(page.locator('.keyDetailsHeader')).to_be_visible()
        expect(page.locator('.keyDetailsHeader')).to_have_text(keyname)
        
        # BUG:  https://u-serviceportal.atlassian.net/browse/KMAAS-447


        # the popup html elements have no clearly identifiable properties
        # so we only check the presence of the keyname and date values

        # expect(page.locator('#keyDetailsModal')).to_have_text(keyname)
        # expect(page.locator('#keyDetailsModal')).to_have_text(QA_AZURE_KEY_VAULT)
        # expect(page.locator('#keyDetailsModal')).to_have_text("12/10/2024, 7:30 PM")
        # expect(page.locator('#keyDetailsModal')).to_have_text("6/10/2025, 7:30 AM")

        # -------------------------------------------------------------------------------------------------------------
        
        cuil.log_teststep("Close popup")
        pom.click_close_button()

        cuil.log_teststep("Check if popup is gone")
        expect(page.locator('#keyDetailsModal')).to_be_hidden()

        # -------------------------------------------------------------------------------------------------------------

        # Teardown:
        cuil.log_teststep("Teardown")
        pom.delete_key(keyname)
        time.sleep(2)
        pom.teardown_delete_cloud_instance(cuil,pom.instance_name)


    except Exception: cuil.handle_exception(request)
    else: logging.info(f"TESTEND:PASS:{testDescription}")
