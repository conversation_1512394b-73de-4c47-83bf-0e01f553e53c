import logging, pytest, traceback, time
from pom import POM
from subclasses.common_ui_lib import TAASCUIL
from playwright.sync_api import expect
from i18n import i18n

# Skip the entire file if PROD is True
@pytest.fixture(autouse=True, scope="module")
def skip_if_prod(PROD):
    if PROD:
        pytest.skip("Skipping tests in production environment")

def test_42(
                                request,
                                getPage,
                                running_on,
                                getProjectVars,
    ):

    testID          = "KMAAS-42"
    testname        = "GUI for creating new Azure Cloud instance"
    testDescription = f"PlayWright-Test: '{testID}-{testname}'"
    status          = "ready"

    pom    = POM(getPage)
    cuil   = TAASCUIL(getPage)
    page   = pom.page
    pv     = getProjectVars

    QA_AZURE_TENANT_ID     = pv['QA_AZURE_TENANT_ID']
    QA_AZURE_CLIENT_ID     = pv['QA_AZURE_CLIENT_ID']
    QA_AZURE_CLIENT_SECRET = pv['QA_AZURE_CLIENT_SECRET']
    QA_AZURE_KEY_VAULT     = pv['key_vault_name']
    QA_MFA_SECRET          = pv['QA_MFA_SECRET' ]

    cloud_instance_name    = pv['cloud_instance_name']

    i18n_forbiddenChars             =  i18n["en"]["AddCloudInstance-New Cloud Instance Validation Msg"]
    i18n_validatingKeyVaultError    =  "An error occurred while validating Key Vault"
    i18n_invalidClientID            =  "Invalid Client ID provided."
    i18n_invalidClientSecret        =  "Invalid Client Secret provided."
    i18n_successfullyConnected      =  "Successfully connected to the cloud instance"
    
   
    i18n_buttonTextForAddCloudInstance =  i18n["en"]["CloudListing: Button text for Add Cloud Instance"]
    i18n_addCloudInstanceAdd           =  i18n["en"]["AddCloudInstance-Add"]
    i18n_addCloudInstanceCancel        =  i18n["en"]["AddCloudInstance: Cancel"]

    
    logging.info(i18n_forbiddenChars)
    logging.info(testDescription)

    try:
        cuil.goto(pv['ci_url'] if running_on == "gitlab" else pv['dev_url'])

        cuil.log_teststep("Login")
        cuil.login (pv, QA_MFA_SECRET)

        cuil.isTitle  ("Enterprise Key Manager as a Service")

        cuil.log_teststep("Click 'Cloud Clients' -> 'Azure'")
        cuil.click_main_menu_item ({'Cloud Clients':'Azure'})
        

        # -------------------------------------------------------------------------------------------------------------
        # empty out old instance and delete it
        
        pom.page.get_by_test_id("kmaas.pagination.itemsperpage").select_option("50")
        if page.get_by_role("cell", name=cloud_instance_name, exact=True).is_visible():
            pom.click_cloud_instance_manage_keys(cloud_instance_name)
            pom.delete_all_keys()
            cuil.click_main_menu_item ({'Cloud Clients':'Azure'})
            pom.page.get_by_test_id("kmaas.pagination.itemsperpage").select_option("50")
            pom.click_cloud_instance_delete_instance(cloud_instance_name)


        # pom.pause()

        
        # -------------------------------------------------------------------------------------------------------------
        

        test_data = [
            
            # KeyName               TenantID             ClientID             ClientSecret             KeyVault             Expected Answer
            [ "ÄÖÜäöü"            , "wrongtext"        , "wrongtext"        , "wrongtext"            , "wrongtext"        , i18n_forbiddenChars          ],
            [ "wrongtext"         , "wrongtext"        , "wrongtext"        , "wrongtext"            , "wrongtext"        , i18n_validatingKeyVaultError ],
            [ "wrongtext"         , "wrongtext"        , "wrongtext"        , "wrongtext"            , QA_AZURE_KEY_VAULT , i18n_invalidClientID         ],
            [ "wrongtext"         , "wrongtext"        , "wrongtext"        , QA_AZURE_CLIENT_SECRET , "wrongtext"        , i18n_validatingKeyVaultError ],
            [ "wrongtext"         , "wrongtext"        , "wrongtext"        , QA_AZURE_CLIENT_SECRET , QA_AZURE_KEY_VAULT , i18n_invalidClientID         ],
            [ "wrongtext"         , "wrongtext"        , QA_AZURE_CLIENT_ID , "wrongtext"            , "wrongtext"        , i18n_validatingKeyVaultError ],
            [ "wrongtext"         , "wrongtext"        , QA_AZURE_CLIENT_ID , "wrongtext"            , QA_AZURE_KEY_VAULT , i18n_invalidClientSecret     ],
            [ "wrongtext"         , "wrongtext"        , QA_AZURE_CLIENT_ID , QA_AZURE_CLIENT_SECRET , "wrongtext"        , i18n_validatingKeyVaultError ],
            [ "wrongtext"         , "wrongtext"        , QA_AZURE_CLIENT_ID , QA_AZURE_CLIENT_SECRET , QA_AZURE_KEY_VAULT , i18n_successfullyConnected   ],
            [ "wrongtext"         , QA_AZURE_TENANT_ID , "wrongtext"        , "wrongtext"            , "wrongtext"        , i18n_validatingKeyVaultError ],
            [ "wrongtext"         , QA_AZURE_TENANT_ID , "wrongtext"        , "wrongtext"            , QA_AZURE_KEY_VAULT , i18n_invalidClientID         ],
            [ "wrongtext"         , QA_AZURE_TENANT_ID , "wrongtext"        , QA_AZURE_CLIENT_SECRET , "wrongtext"        , i18n_validatingKeyVaultError ],
            [ "wrongtext"         , QA_AZURE_TENANT_ID , "wrongtext"        , QA_AZURE_CLIENT_SECRET , QA_AZURE_KEY_VAULT , i18n_invalidClientID         ],
            [ "wrongtext"         , QA_AZURE_TENANT_ID , QA_AZURE_CLIENT_ID , "wrongtext"            , "wrongtext"        , i18n_validatingKeyVaultError ],
            [ "wrongtext"         , QA_AZURE_TENANT_ID , QA_AZURE_CLIENT_ID , "wrongtext"            , QA_AZURE_KEY_VAULT , i18n_invalidClientSecret     ],
            [ "wrongtext"         , QA_AZURE_TENANT_ID , QA_AZURE_CLIENT_ID , QA_AZURE_CLIENT_SECRET , "wrongtext"        , i18n_validatingKeyVaultError ],
            [ "wrongtext"         , QA_AZURE_TENANT_ID , QA_AZURE_CLIENT_ID , QA_AZURE_CLIENT_SECRET , QA_AZURE_KEY_VAULT , i18n_successfullyConnected   ],
            [ cloud_instance_name , "wrongtext"        , "wrongtext"        , "wrongtext"            , "wrongtext"        , i18n_validatingKeyVaultError ],
            [ cloud_instance_name , "wrongtext"        , "wrongtext"        , "wrongtext"            , QA_AZURE_KEY_VAULT , i18n_invalidClientID         ],
            [ cloud_instance_name , "wrongtext"        , "wrongtext"        , QA_AZURE_CLIENT_SECRET , "wrongtext"        , i18n_validatingKeyVaultError ],
            [ cloud_instance_name , "wrongtext"        , "wrongtext"        , QA_AZURE_CLIENT_SECRET , QA_AZURE_KEY_VAULT , i18n_invalidClientID         ],
            [ cloud_instance_name , "wrongtext"        , QA_AZURE_CLIENT_ID , "wrongtext"            , "wrongtext"        , i18n_validatingKeyVaultError ],
            [ cloud_instance_name , "wrongtext"        , QA_AZURE_CLIENT_ID , "wrongtext"            , QA_AZURE_KEY_VAULT , i18n_invalidClientSecret     ],
            [ cloud_instance_name , "wrongtext"        , QA_AZURE_CLIENT_ID , QA_AZURE_CLIENT_SECRET , "wrongtext"        , i18n_validatingKeyVaultError ],
            [ cloud_instance_name , "wrongtext"        , QA_AZURE_CLIENT_ID , QA_AZURE_CLIENT_SECRET , QA_AZURE_KEY_VAULT , i18n_successfullyConnected   ],
            [ cloud_instance_name , QA_AZURE_TENANT_ID , "wrongtext"        , "wrongtext"            , "wrongtext"        , i18n_validatingKeyVaultError ],
            [ cloud_instance_name , QA_AZURE_TENANT_ID , "wrongtext"        , "wrongtext"            , QA_AZURE_KEY_VAULT , i18n_invalidClientID         ],
            [ cloud_instance_name , QA_AZURE_TENANT_ID , "wrongtext"        , QA_AZURE_CLIENT_SECRET , "wrongtext"        , i18n_validatingKeyVaultError ],
            [ cloud_instance_name , QA_AZURE_TENANT_ID , "wrongtext"        , QA_AZURE_CLIENT_SECRET , QA_AZURE_KEY_VAULT , i18n_invalidClientID         ],
            [ cloud_instance_name , QA_AZURE_TENANT_ID , QA_AZURE_CLIENT_ID , "wrongtext"            , "wrongtext"        , i18n_validatingKeyVaultError ],
            [ cloud_instance_name , QA_AZURE_TENANT_ID , QA_AZURE_CLIENT_ID , "wrongtext"            , QA_AZURE_KEY_VAULT , i18n_invalidClientSecret     ],
            [ cloud_instance_name , QA_AZURE_TENANT_ID , QA_AZURE_CLIENT_ID , QA_AZURE_CLIENT_SECRET , "wrongtext"        , i18n_validatingKeyVaultError ],
            [ cloud_instance_name , QA_AZURE_TENANT_ID , QA_AZURE_CLIENT_ID , QA_AZURE_CLIENT_SECRET , QA_AZURE_KEY_VAULT , i18n_successfullyConnected   ]
        ]

        # -------------------------------------------------------------------------------------------------------------

        cuil.log_teststep("Perform various xfail tests and check according error messages")

        for i, data in enumerate(test_data):
            print(i+86, data) # line number of first [ "wrongtext"  ...

            pom.click_add_cloud_instance()
            pom.check_add_instance_popup_elements()
            pom.select_azure()
            
            pom.check_add_instance_azure_popup_elements()

            pom.fill_instance_fields(
                data[0],   # cloud_instance_name
                data[1],   # tennantID
                data[2],   # clientID
                data[3],   # clientSecret
                data[4],   # keyVault
                data[5]    # answer
            )

            # on all runs except the last, click “Cancel”
            if i < len(test_data) - 1:
                # pom.pause()
                pom.click_cancel_button()
            else:
                # on the last run, click “OK”
                # pom.pause()
                pom.click_add_button()

        # -------------------------------------------------------------------------------------------------------------


        cuil.check_toaster_container_present()
        
        # pom.pause()
            
        cuil.check_toaster_content(
            page, 
            "success", 
            "Success", 
            "Cloud Instance qa-dus-TH-test-instance [Azure] created successfully."
        )
        # the success message comes from:
        # /taas-kmaas-rest-api/src/main/java/com/utimaco/taas/taas_key_management_as_a_service/eskmcloud/constants/Messages.java
        
        # -------------------------------------------------------------------------------------------------------------
        
        # check table and click entry
        cuil.log_teststep("Check new instance for existance")
        pom.check_instance_existence(cloud_instance_name)

        cuil.log_teststep("Click “Edit Instance”")
        pom.click_cloud_instance_edit_instance(cloud_instance_name)

        # -------------------------------------------------------------------------------------------------------------

        # popup opens
        cuil.log_teststep("Check if all elements of the popup are present")
        pom.check_edit_instance_popup_elements()

        # click on the eye to make secret visible
        cuil.log_teststep("Click on “Show Secret”")
        pom.click_secret_eye()

        cuil.log_teststep("Check values os cloud_instance_name, QA_AZURE_TENANT_ID, QA_AZURE_CLIENT_ID, QA_AZURE_KEY_VAULT")
        pom.check_instance_values(
                    cloud_instance_name,
                    QA_AZURE_TENANT_ID,
                    QA_AZURE_CLIENT_ID,
                    QA_AZURE_CLIENT_SECRET,
                    QA_AZURE_KEY_VAULT
        )

        cuil.log_teststep("Click on “Cancel”")
        pom.click_cancel_button()

        # -------------------------------------------------------------------------------------------------------------

        # try to create same instance again

        cuil.log_teststep(f"Create instance with same name as the newly created instance -> we expect error message")
        pom.click_add_cloud_instance()
        pom.select_azure()
    
        pom.fill_instance_fields(
                    cloud_instance_name,
                    QA_AZURE_TENANT_ID,
                    QA_AZURE_CLIENT_ID,
                    QA_AZURE_CLIENT_SECRET,
                    QA_AZURE_KEY_VAULT
        )

        pom.click_add_button()      
        pom.await_generic_message_feedback("Cloud Instance qa-dus-TH-test-instance already exist.")
        pom.click_cancel_button()      

        # -------------------------------------------------------------------------------------------------------------

        # delete instance for cleanup 

        cuil.log_teststep(f"Cleaning up -> Delete instance")
        pom.click_cloud_instance_delete_instance(cloud_instance_name)
        time.sleep(.5)
        cuil.log_teststep(f"Verify that instance was deleted")
        cuil.text_hidden(cloud_instance_name)


        # -------------------------------------------------------------------------------------------------------------

    except Exception: cuil.handle_exception(request)
    else: logging.info(f"TESTEND:PASS:{testDescription}")

