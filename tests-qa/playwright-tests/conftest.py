import pytest, os
import logging
from playwright.sync_api import sync_playwright

# for once there are projectVars here
# because only here we test on an online installment instead an dockerized angular project
@pytest.fixture(scope="session")
def getProjectVars():
    logging.info("getProjectVars called")
    logging.info(os.environ.get("CI_COMMIT_REF_NAME"))

    try:
        from dotenv import load_dotenv

        script_dir = os.path.dirname(os.path.abspath(__file__))
        dotenv_path = os.path.join(script_dir, '../.env')
    
        if not load_dotenv(dotenv_path=dotenv_path):
            print(f"Failed to load .env file from {dotenv_path}")
        else:
            print("Successfully loaded .env file")
    except:
        print("We are obviously not running locally - trying to load env-vars from CI...")

    stage = os.environ.get("CI_COMMIT_REF_NAME").lower()

    projectVars = {
        'ci_url'                       : 'http:localhost:4200/',
        'cloud_instance_name'          : 'qa-dus-TH-test-instance',
        'cloud_instance_name_2vaults'  : 'qa-dus-TH-test-instance-2-vaults',
        'key_vault_name'               : 'TaaS-EKMaaS-QA-THO',
    }

    if stage == "develop":
        logging.info("getProjectVars DEV")

        projectVars['dev_url']        = 'http:localhost:4200/'
        projectVars['user']           = "<EMAIL>"
        projectVars['pass']           = "abcABC123!"
        # projectVars['QA_MFA_SECRET']  = os.environ.get("DEV_QA_EKMAAS_MFA_SECRET"),
        projectVars['QA_MFA_SECRET' ] = os.environ.get("PREPROD_QA_USER_MFA_SECRET")

        projectVars['QA_AZURE_TENANT_ID']     = os.environ.get("DEV_QA_EKMAAS_AZURE_TENANT_ID")
        projectVars['QA_AZURE_CLIENT_ID']     = os.environ.get("DEV_QA_EKMAAS_AZURE_CLIENT_ID")
        projectVars['QA_AZURE_CLIENT_SECRET'] = os.environ.get("DEV_QA_EKMAAS_AZURE_CLIENT_SECRET")

        projectVars['QA_AZURE_TENANT_ID_2']     = os.environ.get("DEV_QA_EKMAAS_AZURE_TENANT_ID_2")
        projectVars['QA_AZURE_CLIENT_ID_2']     = os.environ.get("DEV_QA_EKMAAS_AZURE_CLIENT_ID_2A")
        projectVars['QA_AZURE_CLIENT_SECRET_2'] = os.environ.get("DEV_QA_EKMAAS_AZURE_CLIENT_SECRET_2A")

        projectVars['QA_AZURE_KEY_VAULT_2A']    = os.environ.get("QA_AZURE_KEY_VAULT_2A")
        projectVars['QA_AZURE_KEY_VAULT_2B']    = os.environ.get("QA_AZURE_KEY_VAULT_2B")
        
        
    if stage == "preprod":
        
        logging.info("getProjectVars PREPROD")

        projectVars['dev_url']        = 'https://ekmaas.preprod.services.utimaco.com/'
        projectVars['user']           = os.environ.get("PREPROD_QA_USER_MFA_USERNAME")
        projectVars['pass']           = os.environ.get("PREPROD_QA_USER_MFA_PASSWORD")
        projectVars['QA_MFA_SECRET' ] = os.environ.get("PREPROD_QA_USER_MFA_SECRET")

        projectVars['QA_AZURE_TENANT_ID']       = os.environ.get("DEV_QA_EKMAAS_AZURE_TENANT_ID")
        projectVars['QA_AZURE_CLIENT_ID']       = os.environ.get("DEV_QA_EKMAAS_AZURE_CLIENT_ID")
        projectVars['QA_AZURE_CLIENT_SECRET']   = os.environ.get("DEV_QA_EKMAAS_AZURE_CLIENT_SECRET")

        projectVars['QA_AZURE_TENANT_ID_2']     = os.environ.get("DEV_QA_EKMAAS_AZURE_TENANT_ID_2")
        projectVars['QA_AZURE_CLIENT_ID_2']     = os.environ.get("DEV_QA_EKMAAS_AZURE_CLIENT_ID_2A")
        projectVars['QA_AZURE_CLIENT_SECRET_2'] = os.environ.get("DEV_QA_EKMAAS_AZURE_CLIENT_SECRET_2A")
        
        projectVars['QA_AZURE_KEY_VAULT_2A']    = os.environ.get("QA_AZURE_KEY_VAULT_2A")
        projectVars['QA_AZURE_KEY_VAULT_2B']    = os.environ.get("QA_AZURE_KEY_VAULT_2B")


    logging.info(projectVars)
    return projectVars

# as workaround i increased the resolution while test development
# remove this routine in production stage
FIXED_RESOLUTION = {"width": 1920, "height": 1080}

@pytest.fixture(scope="session")
def browser_context_args():
    logging.info(f"Running test with fixed resolution: {FIXED_RESOLUTION['width']}x{FIXED_RESOLUTION['height']}")
    return {
        "ignore_https_errors": True,
        "viewport": FIXED_RESOLUTION
    }

# CAUTION: versioncheck easteregg is NOT WORKING with slow_mo active!
# versioncheck needs to run in its own task without slowing it down.
# @pytest.fixture(scope="session")
# def browser_type_launch_args():
#     return {
#         # "headless": False,
#         "slow_mo": 2000  # milliseconds between each action
#     }