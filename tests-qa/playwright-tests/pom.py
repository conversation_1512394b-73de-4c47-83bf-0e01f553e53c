import logging, time, re
from subclasses.helpers import *
from playwright.sync_api import expect, <PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
from i18n import i18n

class POM:

    def __init__(self, page, cuil=None) -> None:
        print (page)

        self.page = page

        self.AUTIT_LOG_CELL_LOCATORS = {
            "utcDateTime": "td.cdk-column-utcDateTime",
            "keyName": "td.cdk-column-keyName",
            "action": "td.cdk-column-action",
            "status": "td.cdk-column-status",
            "message": "td.cdk-column-message",
            "userId": "td.cdk-column-userId",
            "requestorIpAddress": "td.cdk-column-requestorIpAddress",
        }

        self.AUTIT_LOG_ROW_SELECTOR  = "tr.mdc-data-table__row"

        self.instance_name = ""
        self.instance_name2vaults = ""

        self.cuil = cuil
        
        self.replaced_pairs = []

# ---------------------------------------------------------------------------------

    def pause(self):
        self.page.pause()


    def click_table_headers(self):

            # Wait for the table head to be visible
            self.page.wait_for_selector("thead.p-datatable-thead")

            # Select all headers within the table head
            headers = self.page.query_selector_all("thead.p-datatable-thead th")

            header_labels = []

            # Iterate through the headers and click each one
            for header in headers:
                text_content = header.inner_text()
                logging.info(f"Clicking header: {text_content}")
                header_labels.append(text_content)
                header.click()
            
            return header_labels
    
    def click_header_by_index(self, column_index):
        """
        Clicks the table header of the specified column index.

        :param column_index: Index of the column to click (1-based index)
        """
        # Wait for the table head to be visible
        self.page.wait_for_selector("thead.p-datatable-thead")

        # Select all headers within the table head
        headers = self.page.query_selector_all("thead.p-datatable-thead th")

        # Check if the column index is valid
        if column_index < 1 or column_index > len(headers):
            raise ValueError(f"Invalid column_index: {column_index}. Must be between 1 and {len(headers)}.")

        # Get the header at the specified index (adjust for 0-based indexing)
        header = headers[column_index - 1]
        
        # Extract the text content of the header (optional, for logging)
        header_label = header.inner_text().strip()
        logging.info(f"Clicking header: {header_label}")
        
        # Click the header
        header.click()
        
        return header_label


    def click_add_cloud_instance(self):
        logging.info(f"Click 'Add Cloud instance' and check elements")
        self.page.locator("button#addCloudInstanceOpen").click()
        

    def click_add_button(self):
        logging.info(f"Click Add button")
        self.page.get_by_role("button", name="Add", exact=True).click() 

    def click_cancel_button(self):
        logging.info(f"Click Cancel button")
        self.page.get_by_role("button", name="Cancel").click()

    def click_close_button(self):
        logging.info(f"Click Close button")
        self.page.get_by_role("button", name="Close").click()

    def click_verify_button(self):
        logging.info(f"Click Verify button")
        self.page.get_by_role("button", name="Verify").click()

    def click_yes_button(self):
        logging.info(f"Click Yes button")
        self.page.locator("#deleteConfirmationYes").click()

    def click_next_button(self):
        logging.info(f"Click Next button")
        self.page.get_by_role("button", name="Next", exact=True).click()

    def click_create_button(self):
        # click create button
        self.page.get_by_role("button", name="Create", exact=True).click()



    def click_create_upload_key_button(self):
        logging.info(f"Click 'Create/Upload Key' button")
        self.page.get_by_role("button", name="Create/Upload Key").click()

    def click_cloud_instance_manage_keys(self,cloud_instance_name):
        logging.info(f"Click on the instance entry name cell: {cloud_instance_name}")
        time.sleep(1)
        self.page.get_by_role("row", name=f"{cloud_instance_name}").get_by_role("button").first.click()
        # wait for backend
        logging.info(f"Waiting to see keylist")

        # self.page.locator('button[routerlink="/cloud-instances/"]').wait_for(state="visible")
        
        self.page.wait_for_selector(
            # 'td[data-testid="kmaas.row.id0.cell.keySource"], :has-text("no keys available")',
            'th[data-testid="kmaas.header.cell.name"], :has-text("no keys available")',
            state="visible"
        )

        # <th _ngcontent-ng-c2016189829="" role="columnheader" class="sortable sorted sorted-asc" data-testid="kmaas.header.cell.name" aria-sort="ascending"> Key Name </th>

    def click_cloud_instance_edit_instance(self,cloud_instance_name):
        logging.info(f"Click on the instance entry name cell: {cloud_instance_name}")
        time.sleep(1)
        self.page.get_by_role("row", name=f"{cloud_instance_name} type Azure").get_by_role("button").nth(1).click()

        # wait for backend
        # logging.info(f"Waiting to see keylist")
        # self.page.locator('button[routerlink="/cloud-instances/"]').wait_for(state="visible")

    def click_cloud_instance_delete_instance(self, name):
        logging.info(f"Delete instance for cleanup: {name}")
        self.page.get_by_role("row", name=f"{name}").get_by_role("button").nth(2).click()
        expect(self.page.get_by_text("Are you sure you want to")).to_be_visible()
        self.page.get_by_role("button", name="Confirm", exact=True).click()

    def click_instance_table_entry(self,name):
        logging.info(f"Click on instance entry in table")
        self.page.locator("div").filter(has_text=name).first.click()

    def click_secret_eye(self):
        logging.info(f"Click on 'eye' next to password/secret field'")
        self.page.get_by_text("visibility").click()

    def click_upload_button(self):
        logging.info(f"Click on upload to push key")
        self.page.get_by_role("button", name="Upload", exact=True).click()

    def click_ok_button(self):
        logging.info(f"Click on OK")
        self.pause()
        self.page.get_by_role("button", name="Okay").click()

    def click_temporary_back_button(self):
        logging.info(f"Click on back button")
        self.page.locator("button").filter(has_text="arrow_back_ios").click()

    def tick_key_enable_checkbox(self):
        logging.info(f"Check 'enabled' checkbox")
        self.page.locator("#input_enabled").check()


# ---------------------------------------------------------------------------------


    def check_add_instance_popup_elements(self):
        # Portion checks presence of all components in the Add Cloud Instance popup
        logging.info(f"Check elements of 'Add Cloud instance' popup")

        expect(self.page.locator("[data-testid='addCloudInstance.button.external.close']")).to_be_visible()
        
        expect(self.page.get_by_text("* Instance Name")).to_be_visible()
        expect(self.page.get_by_text("* Cloud Type")).to_be_visible()
        
        expect(self.page.locator("#confirmButtonAddCloudInstance")).to_be_visible()
        expect(self.page.locator("#cancelButtonAddCloudInstance")).to_be_visible()
        
        expect(self.page.locator("#editCloudType")).to_be_visible()
        expect(self.page.locator("#addCloudInstanceName")).to_be_visible()

    def check_edit_instance_popup_elements(self):
        logging.info(f"Check elements of 'Edit Cloud instance' popup")

        expect(self.page.get_by_text("close")).to_be_visible()
        expect(self.page.locator("#editCloudInstanceModal").get_by_text("Instance Name")).to_be_visible()
        expect(self.page.locator("#editCloudInstanceModal").get_by_text("Cloud Type")).to_be_visible()
        expect(self.page.get_by_text("Tenant ID")).to_be_visible()
        expect(self.page.get_by_text("Client ID")).to_be_visible()
        expect(self.page.get_by_text("Client Secret")).to_be_visible()
        expect(self.page.get_by_text("Key Vaults")).to_be_visible()
        expect(self.page.get_by_text("visibility")).to_be_visible()
        expect(self.page.get_by_role("button", name="Verify")).to_be_visible()
        expect(self.page.get_by_role("button", name="Cancel")).to_be_visible()

    def check_add_instance_azure_popup_elements(self):
        # check for completeness of updated popup 
        logging.info(f"Check elements of 'Add Cloud instance' popup")
        
        expect(self.page.get_by_text("Tenant ID")).to_be_visible()
        expect(self.page.get_by_text("Client ID")).to_be_visible()
        expect(self.page.get_by_text("Client Secret")).to_be_visible()
        expect(self.page.get_by_text("visibility")).to_be_visible()
        expect(self.page.get_by_text("Key Vaults")).to_be_visible()
        expect(self.page.locator("#input_keyVaults")).to_be_visible()

    def check_instance_values(
            self,
            cloudInstanceName,
            tenantID,
            clientID,
            clientSecret,
            keyVaults
        ):
        logging.info("Checking the entered values in instance edit.")
        logging.info("We explicitly check that the secret is NOT revealed")

        # Retrieve values from input fields
        name_value           = self.page.locator("#editCloudInstanceName").input_value()
        client_id_value      = self.page.locator("#input_clientID").input_value()
        tenant_id_value      = self.page.locator("#input_tenantID").input_value()
        client_secret_value  = self.page.locator("#input_clientSecret").input_value()
        keyVaults_value      = self.page.locator("#input_keyVaults").input_value()

        # Validate each field
        assert client_id_value == clientID, (
            f"Instance Name mismatch: expected '{cloudInstanceName}', got '{name_value}'"
        )
        assert client_id_value == clientID, (
            f"Client ID mismatch: expected '{clientID}', got '{client_id_value}'"
        )
        assert tenant_id_value == tenantID, (
            f"Tenant ID mismatch: expected '{tenantID}', got '{tenant_id_value}'"
        )
        assert client_secret_value == '', (
            f"Client Secret mismatch: expected '', got '{client_secret_value}'"
        )
        assert keyVaults_value == keyVaults, (
            f"Key Vaults mismatch: expected '{keyVaults}', got '{keyVaults_value}'"
        )

        logging.info("All entered values match the expected values.")

    def check_key_existence_in_list(self,keyname):

        expect(self.page.get_by_role("cell", name=keyname, exact=True,)).to_be_visible(timeout=20_000)
        expect(self.page.get_by_role("cell", name=f"azure_{keyname}(ESKM)")).to_be_visible(timeout=20_000)
        # expect(self.page.get_by_role("cell", name="Uploaded")).to_be_visible()

    def check_instance_existence(self,name):
        logging.info(f"We expect to see the newly created instance in the list")
        expect(self.page.get_by_role("cell", name=name, exact=True)).to_be_visible()


# ---------------------------------------------------------------------------------


    def await_key_creation_success(self,keyname,cloud_instance_name):
        expect(self.page.get_by_text("Key created successfully on")).to_be_visible()
        expect(self.page.get_by_text(f"azure_{keyname}", exact=True)).to_be_visible()
        expect(self.page.get_by_text(f"azure_{cloud_instance_name}")).to_be_visible()
        expect(self.page.get_by_label("2Summary").get_by_text("RSA-")).to_be_visible()

    def await_toaster(self,message):
        # this form of the message awaits should be redone especially for the uniformed toasters we're going to have (CUIL)
        logging.info(f"We expect to see a toaster with specified message:")
        logging.info(message)
        expect(self.page.get_by_text(message)).to_be_visible()
        # try:
        #     self.page.locator('[data-testid=/^toastr\\.container\\.messagecontainer\\.message\\.[0-9]+\\.close$/]').click()
        # except:
        #     logging.info("Toaster-close not hit...")

    def await_generic_message_feedback(self,message):
        logging.info(f"We expect to see a message of some generic format:")
        logging.info(message)
        expect(self.page.get_by_text(message)).to_be_visible(timeout=50000)


# ---------------------------------------------------------------------------------

    def fill_cloudInstanceName(self,cloudInstanceName):
        locator = self.page.locator("#addCloudInstanceName")
        # self.type_instead_of_fill(locator,cloudInstanceName)
        locator.fill(cloudInstanceName)

    def fill_tenantID(self,tenantID):
        locator = self.page.locator("#input_tenantID")
        # self.type_instead_of_fill(locator,tenantID)
        locator.fill(tenantID)

    def fill_clientID(self,clientID):
        locator = self.page.locator("#input_clientID")
        # self.type_instead_of_fill(locator,clientID)
        locator.fill(clientID)

    def fill_clientSecret(self,clientSecret):
        locator = self.page.locator("#input_clientSecret")
        # self.type_instead_of_fill(locator,clientSecret)
        locator.fill(clientSecret)

    def fill_key_vaults(self,keyVaults):
        logging.debug(f"Filling Key-Vaults field: {keyVaults}")
        locator = self.page.locator("#input_keyVaults")
        # self.type_instead_of_fill(locator,keyVaults)
        locator.fill(keyVaults)

    def fill_instance_fields(
              self, 
              cloudInstanceName,
              tenantID,
              clientID,
              clientSecret,
              keyVaults,
              answer="Successfully connected to the cloud instance"
        ):
        logging.info(f"Fill in the data into the instance configuration")

        logging.debug(f"cloudInstanceName: {cloudInstanceName}")
        logging.debug(f"tenantID: {tenantID}")
        logging.debug(f"clientID: {clientID}")
        logging.debug(f"clientSecret: {clientSecret}")
        logging.debug(f"keyVaults: {keyVaults}")

        # fill fields
        self.fill_cloudInstanceName(cloudInstanceName)
        # self.pause()
        self.fill_tenantID(tenantID)
        self.fill_clientID(clientID)
        self.fill_clientSecret(clientSecret)
        self.fill_key_vaults(keyVaults)

        # self.page.pause()

        self.page.get_by_role("button", name="Verify").click()

        # self.page.pause()

        expect(self.page.get_by_text(answer)).to_be_visible(timeout=30000)
        # self.page.get_by_role("button", name="Add", exact=True).click()        



    def fill_key_fields( self, keyname, algo=1):
        
        if keyname:
            # fill in the key name
            self.page.locator("#stepperEskmKey").fill(keyname)
            time.sleep(1)
        
        if algo:
            # select RSA-2048
            self.page.locator("#stepperAlgorithm").select_option(str(algo))
            time.sleep(1)

    def click_add_tag(self):
        self.page.locator("#iconAddValArr").click()





    def fill_tag_fields(self, name, value, index):
        # inputs come in pairs 2,3 for idx=0 ; 4,5 for idx=1 ; 6,7 for idx=2 ; etc.
        logging.info(f"{index} fill_tag_fields: {name} {value} ")
        base = 2 + index*2
        panel = self.page.get_by_role("tabpanel", name="Upload Key")
        if name:
            name = self.replace_random(name)
            logging.debug(f"name: {name}")
            panel.locator("input[type='text']").nth(base).fill(name)
        if value:
            value = self.replace_random(value)
            panel.locator("input[type='text']").nth(base+1).fill(value)
            
        return name,value





    def fill_validity_dates(
                                self, 
                                activation_date: str = None, 
                                expiration_date: str = None, 
    ):
        # WE TAKE DATES IN THIS FORMAT:
        # 02/13/2030, 00:00 AM
        
        full_activation_datetime = False
        full_expiration_datetime = False
        
        if activation_date == "<SameDate>": 
            # Calculate the date two days from now at midnight
            smdt = (datetime.now() + timedelta(days=2)).replace(hour=0, minute=0, second=0, microsecond=0)
            # Format as MM/DD/YYYY, HH:MM AM/PM with 24-hour hour padded and AM/PM
            smdt = smdt.strftime("%m/%d/%Y, %H:%M %p")
            activation_date = smdt
            expiration_date = smdt

        def parse_date(date: str) -> datetime:
            # If the string indicates AM and the hour is "00", replace it with "12"
            if "AM" in date and ", 00:" in date:
                date = date.replace(", 00:", ", 12:")
            # Now parse using the expected format
            dt = datetime.strptime(date, "%m/%d/%Y, %I:%M %p")
            return dt

        # Combine date and time into ISO format if both are provided
        if activation_date:

            if activation_date == "<currentDateTime>":
                # Use the current date and time
                dt = datetime.now()
            else:
                dt = parse_date(activation_date)

            # Format the datetime into an ISO 8601-like string (without seconds)
            full_activation_datetime = dt.strftime("%Y-%m-%dT%H:%M")

            self.page.locator("#input_activationDate").click()
            self.page.locator("#input_activationDate").fill(full_activation_datetime)

        if expiration_date:
            if expiration_date == "<currentDateTime>":
                # Use the current date and time
                dt = datetime.now()
            else:
                dt = parse_date(expiration_date)

            full_expiration_datetime = dt.strftime("%Y-%m-%dT%H:%M")

            self.page.locator("#input_expirationDate").fill(full_expiration_datetime)
            self.page.locator("#input_expirationDate").press("Tab")

        return full_activation_datetime, full_expiration_datetime


# ---------------------------------------------------------------------------------

    def select_key_vault_name(self,option):
        logging.info(f"We select the key vault name in the dropdown '#input_keyVault'")
        self.page.locator("#input_keyVault").select_option(option)

    def select_azure(self):
        # select Azure
        logging.info(f"Select Azure from the dropdown menu")
        self.page.locator("#editCloudType").click()
        self.page.locator("#editCloudType").select_option("Azure")
        # self.pause()
        # self.page.locator("#addCloudInstanceModal").click()

    def make_keyname(self, template):
        if not template: return ""
        rand = ''.join(random.choices(string.ascii_letters + string.digits, k=6))
        # Create the keyname
        keyname = template.replace('<instanceName>', self.instance_name) 
        keyname = keyname.replace('<instanceName_2vaults>', self.instance_name2vaults) 
        keyname = keyname.replace('<random>', rand) 
        keyname = keyname.replace('_', "-") 
        keyname = keyname.strip()
        return keyname

    def replace_random(self, txt):
        rand = ''.join(random.choices(string.ascii_letters + string.digits, k=6))
        if isinstance(txt,  str):
            return txt.replace('<random>', rand).strip()
        else:
            return txt

    def delete_key(self,keyname):
        logging.info(f"Delete key for cleanup: {keyname}")
        self.page.get_by_role("row", name=f"{keyname}").locator("#manageCloudKeysAction").select_option("Delete")


        try:
            self.page.locator("#deleteFromCloudCheck").check()
            expect(self.page.get_by_text("Warning! This action will delete the key")).to_be_visible(timeout=10000)
        except:
            expect(self.page.get_by_text(f"Are you sure you want to delete key")).to_be_visible(timeout=10000)

        # self.page.pause()

        self.page.get_by_role("button", name="Yes").click()

        self.await_toaster("Cloud Instance key")

        # Are you sure you want to delete key


    def delete_first_key_without_name(self):
        logging.info(f"Delete first key in list for cleanup.")
        time.sleep(2)
        self.page.locator("#manageCloudKeysAction").first.select_option("Delete")
        time.sleep(.5)
        
        if self.page.locator("#deleteFromCloudCheck").is_visible():
            self.page.locator("#deleteFromCloudCheck").check()
            expect(self.page.get_by_text("Warning! This action will delete the key")).to_be_visible(timeout=10000)
            # self.page.pause()
        else:
            expect(self.page.get_by_text(f"Are you sure you want to delete key")).to_be_visible(timeout=10000)

        self.page.get_by_role("button", name="Confirm").click()
        self.close_toaster()


        # <button _ngcontent-ng-c902245432="" class="taas-simple-button highlight icon-left"><!--container--><div _ngcontent-ng-c902245432="" class="taas-simple-button-label"></div> Confirm
        # </button>


    def delete_all_keys(self):
        logging.info("Delete all keys for cleanup.")
        while True:
            self.wait_for_spinner_to_go_away()
        
            # time.sleep(5)
            if self.page.locator("#manageCloudKeysAction").count() == 0:
                break
            
            logging.info("found key to delete")
            self.delete_first_key_without_name()





    def get_column_texts(self,column_index):

            # Wait for the table body to load
            self.page.wait_for_selector("tbody.p-datatable-tbody")

            # Select all rows in the table body
            rows = self.page.query_selector_all("tbody.p-datatable-tbody tr")

            # Initialize an array to store texts
            column_texts = []

            # Extract text from the specified column of each row
            for row in rows:
                column = row.query_selector(f"td:nth-child({column_index})")
                if column:
                    text = column.inner_text().strip()  # Clean up the text
                    column_texts.append(text)

            print(f"Extracted texts from column {column_index}: {column_texts}")

            return column_texts


# ---------------------------------------------------------------------------------

    # as PRECONDITION from KMAAS-42
    def precondition_create_cloud_instance(
            self, 
            cloudInstanceName,
            tenantID,
            clientID,
            clientSecret,
            keyVaults
    ):
        logging.info(f"PRECONDITION: Creating Cloud Instance")
                
        self.click_add_cloud_instance()
        self.select_azure()
    
        self.fill_instance_fields(
                    cloudInstanceName,
                    tenantID,
                    clientID,
                    clientSecret,
                    keyVaults
        )

        self.click_add_button()
        self.await_toaster("[Azure] created successfully")
        self.close_toaster()

# ---------------------------------------------------------------------------------

    # as PRECONDITION from KMAAS-96
    def precondition_create_key(
            self, 
            cloudInstanceName,
            keyname,
            keyVaults
    ):

        logging.info(f"PRECONDITION: Creating Key")
        
        try:
            self.click_cloud_instance_manage_keys(cloudInstanceName)
        except: pass
        
        self.click_create_upload_key_button()

        self.fill_key_fields(keyname)
        self.click_create_button()
        self.await_generic_message_feedback(f"Key created successfully on EKMaaS")
        # self.await_key_creation_success(keyname, cloudInstanceName)
        self.click_next_button()
        # we tick the checkbox for "enabled"
        self.tick_key_enable_checkbox()
        # We enter the key vault
        self.select_key_vault_name(keyVaults)

        activation_date="<currentDateTime>"
        expiration_date="02/13/2030, 00:00 AM"

        # Set both dates
        activation_date, expiration_date = self.fill_validity_dates(
            activation_date=activation_date,
            expiration_date=expiration_date,
        )

        self.click_upload_button()
        # this is actually a popup message - not a toaster:
        # we should verify if we need so many different feedback formats
        self.await_generic_message_feedback("has been successfully uploaded")

        self.page.locator("#stepperFinalConOkay").click()

        return activation_date, expiration_date


    def teardown_delete_cloud_instance(self,cuil,cloudInstanceName):
        logging.info(f"TEARDOWN: Cloud Instance")
        cuil.click_main_menu_item ({'Cloud Clients':'Azure'})
        self.click_cloud_instance_delete_instance(self.instance_name)
        self.await_toaster(f"[Azure] deleted successfully.")


# ---------------------------------------------------------------------------------

    # AUDIT LOGS

    def click_filter_user(self):
        self.page.locator("mat-label", has_text="User").click()

    def fill_filter_user(self,name):
        self.click_filter_user()
        # self.page.locator(f"mat-option[ng-reflect-value='{name}']", has_text=f"{name}").click()
        self.page.locator(f"input[formcontrolname='user']").fill(name)

    def click_filter_operation_type(self):
        self.page.locator("mat-label", has_text="Operation Type").click()

    def select_filter_operation_type(self,option:int):
        if option == 0: option = 1 
        options = [
            "Add Cloud Instance",
            "Delete Cloud Instance",
            "Add Key",
            "Delete Key",
            "Edited Key",
            "Update Cloud Instance",
            "Upload Key",
            "Login Attempt",
            "Logout",
            "User Created",
        ]
        option = options[option-1]
        self.click_filter_operation_type()
        self.page.locator(f"mat-option[ng-reflect-value='{option}']", has_text=f"{option}").click()


    def type_instead_of_fill(self, locator, text):
        locator.click()  # Focus on the field
        locator.press('Control+A')  # Select all text (use 'Meta+A' on macOS)
        locator.press('Backspace')  # Delete the selected text
        locator.type(text)

    def fill_filter_keyname(self, keyname):
        locator = self.page.locator("input[formcontrolname='kname']")
        locator.fill(keyname)

    def fill_filter_keyname(self,keyname):
        locator = self.page.locator(f"input[formcontrolname='kname']")
        locator.fill(keyname)

    def fill_filter_from_date(self,date):
        # 2/9/2025
        locator = self.page.locator(f"input[formcontrolname='dateFrom']")
        locator.fill(date)

    def fill_filter_to_date(self,date):
        # 2/9/2025
        locator = self.page.locator(f"input[formcontrolname='dateTo']")
        locator.fill(date)

    def click_filter_apply(self):
        self.page.locator("button.primary", has_text="Apply Filter").click()

    def click_filter_clear(self):
        self.page.locator("button.primary", has_text="Clear Filter").click()

    def close_toaster(self):
        try:
            # Create a locator for all toastr close buttons using a wildcard on the number.
            self.page.locator(".toastr-message-close-buton").first.click()
        except Exception: pass


    def find_row(self, expected_timestamp, keyName=None, action=None,
                status=None, message=None, userId=None, requestorIpAddress=None,
                tolerance=10):

        """
        Returns a locator for the first table row that matches the provided attributes.
        
        The utcDateTime value is compared to the expected_timestamp (which can be provided as
        a string in "YYYY-MM-DD HH:MM:SS" format or as a datetime object) within the given tolerance (in seconds).
        """
        # Log the given data at method call.
        logging.info(
            f"find_row called with: expected_timestamp={expected_timestamp}, keyName={keyName}, "
            f"action={action}, status={status}, message={message}, userId={userId}, "
            f"requestorIpAddress={requestorIpAddress}, tolerance={tolerance}"
        )

        # Convert expected_timestamp to a datetime object if it's provided as a string.
        if isinstance(expected_timestamp, str):
            expected_timestamp = datetime.strptime(expected_timestamp, "%Y-%m-%d %H:%M:%S")

        # I made this flexible for when the UI changes...
        self.AUTIT_LOG_ROW_SELECTOR  = "tr.mdc-data-table__row"

        rows = self.page.locator(self.AUTIT_LOG_ROW_SELECTOR)
        row_count = rows.count()
        for i in range(row_count):
            row = rows.nth(i)

            # Verify timestamp with tolerance.
            ts_text = row.locator(self.AUTIT_LOG_CELL_LOCATORS["utcDateTime"]).inner_text().strip()
            try:
                row_ts = datetime.strptime(ts_text, "%Y-%m-%d %H:%M:%S")
            except ValueError:
                continue  # Skip rows with unparsable timestamps.
            if abs((row_ts - expected_timestamp).total_seconds()) > tolerance:
                continue

            # Check optional attributes.
            if keyName is not None:
                cell = row.locator(self.AUTIT_LOG_CELL_LOCATORS["keyName"]).inner_text().strip()
                if keyName not in cell:
                    continue
            if action is not None:
                cell = row.locator(self.AUTIT_LOG_CELL_LOCATORS["action"]).inner_text().strip()
                if action not in cell:
                    continue
            if status is not None:
                cell = row.locator(self.AUTIT_LOG_CELL_LOCATORS["status"]).inner_text().strip()
                if status not in cell:
                    continue
            if message is not None:
                cell = row.locator(self.AUTIT_LOG_CELL_LOCATORS["message"]).inner_text().strip()
                if message not in cell:
                    continue
            if userId is not None:
                cell = row.locator(self.AUTIT_LOG_CELL_LOCATORS["userId"]).inner_text().strip()
                if userId not in cell:
                    continue
            if requestorIpAddress is not None:
                cell = row.locator(self.AUTIT_LOG_CELL_LOCATORS["requestorIpAddress"]).inner_text().strip()
                if requestorIpAddress not in cell:
                    continue

            # Row found. Log the contents of the row.
            row_contents = {field: row.locator(selector).inner_text().strip() 
                            for field, selector in self.AUTIT_LOG_CELL_LOCATORS.items()}
            logging.info(f"ROW FOUND: {row_contents}")
            
            return row  # Found a matching row.

        raise AssertionError("No matching row found with the specified attributes.")



    def verify_row_contents(self, row, expected_contents: dict):
        """
        Verifies that the given row contains the expected contents.
        expected_contents is a dict with keys matching the column identifiers
        (e.g., "utcDateTime", "keyName", "action", "status", "message", "userId", "requestorIpAddress")
        and values being the expected substrings in those columns.
        """
        logging.info(f"Verifying row contents. Expected contents: {expected_contents}")
        
        for col, expected_text in expected_contents.items():
            cell_locator = f"td.cdk-column-{col}"
            cell_text = row.locator(cell_locator).inner_text().strip()
            logging.debug(f"Column '{col}': expecting substring '{expected_text}', found '{cell_text}'")
            
            if expected_text not in cell_text:
                logging.error(
                    f"Verification failed for column '{col}': Expected '{expected_text}' in '{cell_text}'."
                )
            assert expected_text in cell_text, (
                f"Expected '{expected_text}' in column '{col}', but got '{cell_text}'."
            )
        
        logging.info("Row verified successfully.")



    def get_row_contents_by_timestamp(self, timestamp_str):
        """
        Given a timestamp string in "YYYY-MM-DD HH:MM:SS" format, find the row corresponding to that timestamp
        and return the row contents as a dictionary.
        """
        logging.info(f"get_row_contents_by_timestamp called with timestamp_str: {timestamp_str}")

        # Convert the timestamp string to a datetime object.
        specific_time = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
        logging.info(f"Converted timestamp_str to datetime: {specific_time}")
        
        # Use find_row with just the timestamp.
        row = self.find_row(expected_timestamp=specific_time)
        logging.info(f"Row found for timestamp {specific_time}")
        
        # Option 1: Get a list of all cell texts in the row.
        all_cells = row.locator("td").all_inner_texts()
        logging.debug(f"All cell texts: {all_cells}")
        
        contents = {key: row.locator(selector).inner_text().strip()
                    for key, selector in self.AUTIT_LOG_CELL_LOCATORS.items()}
        logging.info(f"Row contents for timestamp {specific_time}: {contents}")
        
        return contents
    

    def table_click_button_in_row(self, row_index: int, button_text: str) -> None:
        cell_testid = f"kmaas.row.id{row_index}.cell.action"
        button = self.page.locator(f'[data-testid="{cell_testid}"] button:has-text("{button_text}")')
        button.click()


    def performCheck(self,check,keyname):
        logging.info(f"performChecks: {check}")
        
        row = self.get_keyrow_by_keyname(keyname)
        logging.debug(f"keyname:{keyname}")
        logging.debug(f"row:{row}")
        
        # self.pause()
    
        if check == "KeyNotCreated": 
            self.cuil.text_hidden(keyname)
            
        if check == "No Key Name Status Not Uploaded Enabled No No Key Vault": 
            time.sleep(2)
            data = self.extract_row_data(row)
            
            assert data['name'] == "-", (
                f"Expected '-' in column 'name', but got '{data['name']}'."
            )
            kn = f"azure_{keyname}(ESKM)"
            assert data['keySource'] == kn, (
                f"Expected '{kn}' in column 'keySource', but got '{data['keySource']}'."
            )
            assert data['status'] == 'Not Uploaded', (
                f"Expected 'Not Uploaded' in column 'status', but got '{data['status']}'."
            )        
            assert data['enabled'] == 'No', (
                f"Expected 'No' in column 'enabled', but got '{data['enabled']}'."
            )    
            assert data['keyVault'] == False, (
                f"Expected 'FALSE' in column 'keyVault', but got '{data['keyVault']}'."
            )
                   
        if check == "Sonderlocke 2 Tags": 
            logging.info(f"Sonderlocke")

            logging.debug(f"row:{row}")
            
            self.page.locator(f'[data-testid="kmaas.row.{row}.cell.action"]').locator('#manageCloudKeysAction').select_option("Edit")

            time.sleep(2)

            tag_pairs2 = self.collect_tag_pairs()
            for pair in tag_pairs2:
                print(f"{pair['name']} → {pair['value']}")

            logging.debug(f"set1: {self.replaced_pairs}")
            logging.debug(f"set2: {tag_pairs2}")
            
            self.assert_tag_pairs_equal(self.replaced_pairs,tag_pairs2)
            self.click_cancel_button()
            
        if check == "Sonderlocke 2 KeyVaults": 
            # self.pause()
            pass

        if check == "Activation > Expired" or check == "Activation == Expired": 
            self.cuil.check_toaster_content(self.page, "error", "Error", "Activation date must come before expiration date.")
            self.cuil.close_all_toasters(self.page)
            # self.pause()




















    def find_with_timeout(
        self,
        selector: str,
        timeout_ms: int = 3_000,
        poll_interval_ms: int = 100
    ) -> Optional[ElementHandle]:
        
        # Polls page.query_selector for up to `timeout_ms` milliseconds
        # looking for `selector`. Returns the ElementHandle if found,
        # or None if the timeout elapses.
        
        # Example:
        #     cell = self.find_with_timeout("td:has-text('My Key')")

        deadline = time.time() + timeout_ms / 1_000
        while time.time() < deadline:
            handle = self.page.query_selector(selector)
            if handle:
                return handle
            time.sleep(poll_interval_ms / 1_000)
        return None
    
            
    def get_keyrow_by_keyname(self, keyname, type="azure"):
        
        cell = self.find_with_timeout(f"td:has-text('{keyname}')")
        if cell == None:
            if type == "azure":
                cell = self.find_with_timeout(f"td:has-text('azure_{keyname}(ESKM)')")
            # if type == "aws":
            # if type == "google":
            
        try:
            # Get the data-testid attribute
            data_testid = cell.get_attribute('data-testid')
            logging.info(f"data_testid: {data_testid}:")

            # Extract the part between dots (e.g., '.id1.')
            match = re.search(r'\.(id\d+)\.', data_testid)
            
            if match:
                row_id = match.group(1)
                return row_id
        except:
            logging.debug("no keyrow found!")
            return False

            
    def extract_row_data(self, row_id: str):
        # Define the row locator
        row_locator = self.page.locator(f"tr[data-testid='kmaas.row.{row_id}']")
        
        logging.debug(f"row-id: {row_id}")
        
        # Find all <td> elements in the row
        cells = row_locator.locator("td")
        
        # Dictionary to store results
        data = {}

        # Loop through each cell and extract data
        cell_count = cells.count()
        for i in range(cell_count):
            cell = cells.nth(i)
            
            # Extract the data-testid
            data_testid = cell.get_attribute('data-testid')
            if not data_testid:
                continue

            # Extract the suffix (e.g., banana, name, status, etc.)
            match = re.search(r'\.cell\.(\w+)$', data_testid)
            if not match:
                continue
            field_name = match.group(1)

            # Try to locate a <span>
            span_locator = cell.locator('span')
            if span_locator.count() == 0:
                # No <span> at all
                data[field_name] = False
            else:
                # There is a <span>, grab its text
                raw = span_locator.text_content()
                text = raw.strip() if raw else ""
                if not text:
                    # Empty or whitespace-only
                    data[field_name] = False
                else:
                    data[field_name] = text

        return data
    
    def multiple_text_occurrences(self, text: str, expected_count: int) -> None:
        locator = self.page.locator(f'text="{text}"')
        actual_count = locator.count()

        if actual_count != expected_count:
            msg = (
                f"Expected {expected_count!r} occurrence(s) of text {text!r}, "
                f"but found {actual_count!r}."
            )
            # log the error
            logging.error(msg)
            pytest.fail(msg)

    def get_select_value_by_label(self, label_text: str) -> str:
        # Find the container DIV whose label has our text
        label_container = self.page.locator(
            f"div.col-lg-12:has(label:has-text('{label_text}'))"
        )
        # From there, grab the very next sibling div and descend into the <select>
        select = label_container.locator("xpath=following-sibling::div//select")
        # Evaluate JS to pull out the .value of that <select>
        val = select.evaluate("el => el.value")
        logging.info(val)
        return val

    def collect_tag_pairs(self):
        # get a Locator for all the textboxes under your tags container
        textbox_locator = (
            self.page
                .locator("#reuploadKeyFieldstags")
                .get_by_role("textbox")
        )

        # find out how many textboxes there are
        total_boxes = textbox_locator.count()
        if total_boxes % 2 != 0:
            raise RuntimeError(f"Expected an even number of textboxes, got {total_boxes!r}")

        pairs = []
        # step through in pairs: (0,1), (2,3), …
        for i in range(0, total_boxes, 2):
            name_box  = textbox_locator.nth(i)
            value_box = textbox_locator.nth(i + 1)
            name_text  = name_box.get_attribute("ng-reflect-model")
            value_text = value_box.get_attribute("ng-reflect-model")
            pairs.append({"name": name_text, "value": value_text})

        return pairs

    def assert_tag_pairs_equal(self, actual, expected):
        # Assert that two lists of {"name":…, "value":…} dicts contain the same pairs,
        # regardless of order.

        # same length
        assert len(actual) == len(expected), (
            f"Expected {len(expected)} tag-pairs, got {len(actual)}:\n"
            f"  actual  = {actual!r}\n"
            f"  expected= {expected!r}"
        )

        # compare as multisets of (name, value) tuples
        actual_set   = {(p['name'], p['value']) for p in actual}
        expected_set = {(p['name'], p['value']) for p in expected}
        only_actual   = actual_set - expected_set
        only_expected = expected_set - actual_set

        assert not only_actual and not only_expected, (
            "Tag-pairs differ:\n"
            f"  only in actual  = {only_actual}\n"
            f"  only in expected= {only_expected}"
        )

    def expectedres(self, response):
        
        if response == "Missing 2 Items":
            self.multiple_text_occurrences("Field Required",2)
            return
        if response == "Missing 1 Item":
            self.multiple_text_occurrences("Field Required",1)
            return

        expect(self.page.get_by_text(response)).to_be_visible(timeout=30000)



    def get_azure_key_values(self, keyname):
        # select "edit" in key row
        # #reuploadEskmKey.value -> Key Name
        # #reuploadKeyName.value -> *Cloud Key Name (ohne azure_)
        # #input_enabled -> checked
        # Vault Value ->                 
            # pom.get_select_value_by_label("Key Vault")
            # pom.pause()
        # Activation Date
        # Expiration Date
        
        
        
        pass
    
    
    
    def wait_for_spinner_to_go_away(self, timeout: int = 100_000) -> bool:
        
        logging.debug("Waiting for spinner to dissappear")

        # <lib-taas-modal-loading-spinner _ngcontent-ng-c1469472594="" uniqueid="manageCloudKeysLoadingSpinner" width="300px" height="300px" _nghost-ng-c4277456297="" ng-reflect-unique-id="manageCloudKeysLoadingSpinner" ng-reflect-width="300px" ng-reflect-height="300px" ng-reflect-spinner-visible="true"><div _ngcontent-ng-c4277456297="" class="modal-overlay ng-star-inserted" data-testid="manageCloudKeysLoadingSpinner.modal.overlay"><div _ngcontent-ng-c4277456297="" class="modal" data-testid="manageCloudKeysLoadingSpinner.modal" style="width: 300px; height: 300px;"><div _ngcontent-ng-c4277456297="" class="spinner-container" data-testid="manageCloudKeysLoadingSpinner.modal.spinner"><div _ngcontent-ng-c4277456297="" class="spinner"></div></div><div _ngcontent-ng-c4277456297="" class="content" data-testid="manageCloudKeysLoadingSpinner.text.content">Loading...</div></div></div><!--container--></lib-taas-modal-loading-spinner>


        # selector = "div.mdc-circular-progress__circle-clipper"
        
        selector = "[data-testid='manageCloudKeysLoadingSpinner.modal.spinner']"
        
        
        try:
            
            # this WORKED ...
            # -> rewrite after bootstrap remove ...
            
            # state="detached" waits until the element is no longer in the DOM
            self.page.wait_for_selector(selector, state="detached", timeout=timeout)
            logging.debug("Spinner detached")
            return True
        
            
        
        except TimeoutError:
            return False