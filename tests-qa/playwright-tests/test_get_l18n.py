import logging, pytest, time
from subclasses.localization import XLFConverter

def test_localization ():

    testname = "Fetch localization vars"
    testDescription = f"Test: '{testname}'"

    logging.info(testDescription)

    converter = XLFConverter()

    src  = '/home/<USER>/Projects/taas-kmaas-frontend/src/locales'
    dest = '/home/<USER>/Projects/taas-kmaas-frontend/tests-qa/playwright-tests/i18n.py'

    try:
        converter.convert_folder(src, dest)

        from i18n import i18n

        # teststring =  i18n["en"]["Services & Products: headline subscribed services"]
        # logging.info(teststring)
        # teststring =  i18n["de"]["Services & Products: headline subscribed services"]
        # logging.info(teststring)
        # teststring =  i18n["es"]["Services & Products: headline subscribed services"]
        # logging.info(teststring)
        # teststring =  i18n["sv"]["Services & Products: headline subscribed services"]
        # logging.info(teststring)

    except:
        msg = "Localization could not be generated!"
        logging.error(msg)
        pytest.fail(msg)

