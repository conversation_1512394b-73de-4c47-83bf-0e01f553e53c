#!/usr/bin/env bash
# set -e  # Exit immediately if a command exits with a non-zero status

# curl https://iad1-devekmaas-haproxy01.us.utimaco.cloud/api/v1/cloud/instances
# curl https://ekmaas-dev.iad1.us.utimaco.cloud/api/v1/cloud/instances
curl https://ekmaas-api.dev.services.utimaco.com/actuator/health

pytest \
    ./playwright-tests/test_versioncheck.py \
    --alluredir ./allure-results \
    -W ignore::DeprecationWarning \
    -s


 pytest \
    ./playwright-tests/test_KMAAS_42.py \
    --alluredir ./allure-results \
    -W ignore::DeprecationWarning \
    -s --slowmo 1000


    #  ./playwright-tests/test_smoketest.py \
# pytest \
#      ./playwright-tests/test_versioncheck.py \
#      ./playwright-tests/test_KMAAS_96.py \
#      ./playwright-tests/test_KMAAS_98.py \
#      ./playwright-tests/test_KMAAS_99.py \
#     --alluredir ./allure-results \
#     -W ignore::DeprecationWarning \
#     -s


# blocked:
# ./playwright-tests/test_KMAAS_100.py \
# ./playwright-tests/test_KMAAS_131.py \

