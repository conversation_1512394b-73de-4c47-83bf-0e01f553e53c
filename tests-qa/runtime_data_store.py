import os
import json

class JsonDataStore:
    def __init__(self, filename="temp-data.json", init=False):
        # Get the path to the current script's directory and build the full path
        script_dir = os.path.dirname(os.path.abspath(__file__))
        self.file_path = os.path.join(script_dir, filename)
        
        # If the init flag is set, clear (or create) the file with an empty JSON object
        if init:
            with open(self.file_path, "w", encoding="utf-8") as f:
                json.dump({}, f, ensure_ascii=False, indent=4)
    
    def push_var(self, key, value):
        """Push a new variable to the JSON file. Overwrites the value if the key exists."""
        data = {}
        # Load existing data if the file exists, otherwise continue with an empty dictionary
        if os.path.exists(self.file_path):
            try:
                with open(self.file_path, "r", encoding="utf-8") as f:
                    data = json.load(f)
            except json.JSONDecodeError:
                # File exists but is empty or corrupted: reset to empty dict
                data = {}
        
        # Update the dictionary with the new key/value
        data[key] = value
        
        # Write the updated dictionary back to the file using pretty-printing
        with open(self.file_path, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
    
    def get_var(self, key):
        """Retrieve a variable from the JSON file. Returns None if the key does not exist."""
        if os.path.exists(self.file_path):
            try:
                with open(self.file_path, "r", encoding="utf-8") as f:
                    data = json.load(f)
                return data.get(key, None)
            except json.JSONDecodeError:
                return None
        else:
            return None
