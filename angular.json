{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"taas-km-frontend-angular": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "i18n": {"sourceLocale": "en", "locales": {"de": {"translation": "src/locales/messages.de.xlf"}, "se": {"translation": "src/locales/messages.se.xlf"}, "es": {"translation": "src/locales/messages.es.xlf"}}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"loader": {".svg": "text"}, "outputPath": "dist/taas-km-frontend-angular", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["node_modules/utimaco-common-ui-angular/assets/global_style_variables.scss", "src/styles.scss"], "scripts": ["./node_modules/jquery/dist/jquery.js"], "localize": true}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "3mb"}, {"type": "anyComponentStyle", "maximumWarning": "3kb", "maximumError": "4kb"}], "outputHashing": "all"}, "development": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}], "optimization": false, "extractLicenses": false, "sourceMap": true}, "preprod": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.preprod.ts"}], "optimization": false, "extractLicenses": false, "sourceMap": true}, "poc": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.poc.ts"}], "optimization": true, "extractLicenses": false, "sourceMap": true}, "de": {"localize": ["de"]}, "en": {"localize": ["en"]}, "se": {"localize": ["se"]}, "es": {"localize": ["es"]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "taas-km-frontend-angular:build:production"}, "preprod": {"buildTarget": "taas-km-frontend-angular:build:preprod"}, "development": {"buildTarget": "taas-km-frontend-angular:build:development"}, "poc": {"buildTarget": "taas-km-frontend-angular:build:poc"}, "en": {"buildTarget": "taas-km-frontend-angular:build:development,en"}, "de": {"buildTarget": "taas-km-frontend-angular:build:development,de"}, "se": {"buildTarget": "taas-km-frontend-angular:build:development,se"}, "es": {"buildTarget": "taas-km-frontend-angular:build:development,es"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "ng-extract-i18n-merge:ng-extract-i18n-merge", "options": {"buildTarget": "taas-km-frontend-angular:build", "format": "xlf2", "outputPath": "src/locales", "targetFiles": ["messages.de.xlf", "messages.se.xlf", "messages.es.xlf"]}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"karmaConfig": "./karma.conf.js", "polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"schematicCollections": ["@angular-eslint/schematics"]}}