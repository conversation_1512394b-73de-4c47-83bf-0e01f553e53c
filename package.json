{"name": "taas-km-frontend-angular", "version": "0.3.11", "scripts": {"ng": "ng", "lint": "ng lint", "test": "ng test", "start": "ng serve", "start-dev": "ng serve --configuration development", "start-prod": "ng serve --configuration production", "start-preprod": "ng serve --configuration preprod", "start-poc": "ng serve --configuration poc", "start-de": "ng serve --configuration=de", "start-en": "ng serve --configuration=en", "start-se": "ng serve --configuration=se", "translate": "ng extract-i18n", "build": "ng build", "build:stats": "ng build --stats-json && esbuild-visualizer --metadata ./dist/taas-km-frontend-angular/stats.json --filename ./dist/taas-km-frontend-angular/stats.html && open-cli ./dist/taas-km-frontend-angular/stats.html", "build-prod": "ng build --configuration production", "build-preprod": "ng build --configuration preprod", "build-poc": "ng build --configuration poc", "build-dev": "ng build --configuration development", "watch": "ng build --watch --configuration development"}, "private": true, "dependencies": {"@angular/animations": "^17.3.5", "@angular/cdk": "^17.3.5", "@angular/common": "^17.3.5", "@angular/compiler": "^17.3.5", "@angular/core": "^17.3.5", "@angular/forms": "^17.3.5", "@angular/material": "^17.3.5", "@angular/platform-browser": "^17.3.5", "@angular/platform-browser-dynamic": "^17.3.5", "@angular/router": "^17.3.5", "@material-icons/svg": "1.0.33", "@popperjs/core": "^2.11.8", "chart.js": "^4.4.1", "jquery": "^3.7.1", "jwt-decode": "^4.0.0", "keycloak-angular": "^15.2.1", "keycloak-js": "^24.0.3", "ng2-charts": "^5.0.4", "rxjs": "~7.8.0", "taas-km-frontend-angular": "file:", "tslib": "^2.3.0", "utimaco-common-ui-angular": "https://repo.fra1.us.utimaco.cloud/repository/npm-hosted/utimaco-common-ui-angular/-/utimaco-common-ui-angular-1.5.1.tgz", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^17.3.5", "@angular-eslint/builder": "^17.3.5", "@angular-eslint/eslint-plugin": "^17.3.5", "@angular-eslint/eslint-plugin-template": "^17.3.5", "@angular-eslint/schematics": "^17.3.4", "@angular-eslint/template-parser": "^17.3.5", "@angular/cli": "^17.3.5", "@angular/compiler-cli": "^17.3.5", "@angular/localize": "^17.3.5", "@types/jasmine": "~5.1.0", "@typescript-eslint/eslint-plugin": "7.10.0", "@typescript-eslint/parser": "7.10.0", "esbuild-visualizer": "^0.7.0", "eslint": "^8.57.0", "jasmine-core": "~5.1.2", "karma": "~6.4.3", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.1", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "ng-extract-i18n-merge": "^2.12.0", "open-cli": "^8.0.0", "typescript": "~5.4.5"}}