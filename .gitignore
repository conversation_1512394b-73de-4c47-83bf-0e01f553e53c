# See http://help.github.com/ignore-files/ for more about ignoring files.

# Compiled output
/dist
/tmp
/out-tsc
/bazel-out

# Node
/node_modules
npm-debug.log
yarn-error.log

# IDEs and editors
.idea/
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# Visual Studio Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.history/*

# Miscellaneous
/.angular/cache
.sass-cache/
/connect.lock
/coverage
/libpeerconnection.log
testem.log
/typings

# System files
.DS_Store
Thumbs.db

.nx/cache
.nx/workspace-data


# QA
tests-qa/conftest.py
tests-qa/subclasses
tests-qa/playwright-tests/test_2fa.py
__pycache__
tests-qa/.env
screenshots
tests-qa/playwright-tests/crawler
.vscode/settings.json
tests-qa/playwright-tests/_____*
version.txt
