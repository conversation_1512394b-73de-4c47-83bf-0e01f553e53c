// This file defines the main navigation configuration
// The config is used in the common ui angular library as input for rendering the TaaS standard main navigation on the left side

import { TaasMenuItem } from "utimaco-common-ui-angular";
//import chartIcon from '@material-icons/svg/svg/insert_chart/baseline.svg';
// see https://netbasal.com/simplifying-file-imports-in-angular-with-new-loader-options-4e94a0eb18af for importing svgs in angular ^17.1
import homeIcon from '@material-icons/svg/svg/home/<USER>';
import auditLog from '../assets/icons/fluent-mdl2--compliance-audit.svg';
import gcpIcon from '../assets/icons/gcp-icon.svg';
import azureIcon from '../assets/icons/azure-icon.svg';
import awsIcon from '../assets/icons/aws-icon.svg';
//import cloud from '../assets/icons/cloud_sync.svg';
import user from '../assets/icons/person_add.svg';
import group from '../assets/icons/group_add.svg';
import configurationIcon from '../assets/icons/configuration.svg';
import groupIcon from '@material-icons/svg/svg/group/baseline.svg';
import tdeIcon from '../assets/icons/tde-icon.svg';
import kmipIcon from '../assets/icons/kmip-icon.svg';
import restIcon from '../assets/icons/rest.svg';
import enterpriseClientIcon from '../assets/icons/enterprise_client-icon.svg';
import cloudClientIcon from '../assets/icons/cloud_client-icon.svg';

export const TAAS_MENU_ITEM_LIST_TS: TaasMenuItem[] = [
    //{ id: 0, type: "link", active: true, name: $localize`:main-menu home link:Home`, link: "/", iconSvg: homeIcon, children: [] },
    { id: 0, type: "link", active: true, name: $localize`:main-menu home link:Dashboard`, link: "/dashboard", iconSvg: homeIcon, children: [] },
    //{ id: 1, type: "link", active: false, name: $localize`Cloud Instances`, link: "/cloud-instances", iconSvg: chartIcon, children:[]},
    {
        id: 1, type: "section", active: false, name: $localize`Cloud Clients`, link: "cloud-clients/azure", iconSvg: cloudClientIcon, children: [
            { id: 6, type: "link", active: false, name: $localize`Azure`, link: "/cloud-clients/azure", iconSvg: azureIcon, children: [] },
            { id: 7, type: "link", active: false, name: $localize`AWS`, link: "/cloud-clients/aws", iconSvg: awsIcon, children: [] },
            { id: 8, type: "link", active: false, name: $localize`Google`, link: "/cloud-clients/gcp", iconSvg: gcpIcon, children: [] },
        ]
    },
    {
        id: 2, type: "section", active: false, name: $localize`Enterprise Clients`, link: "/enterprise-clients", iconSvg: enterpriseClientIcon, children: [
            { id: 9, type: "link", active: false, name: $localize`KMIP`, link: "/enterprise-clients/kmip", iconSvg: kmipIcon, children: [] },
            { id: 10, type: "link", active: false, name: $localize`TDE`, link: "/enterprise-clients/tde", iconSvg: tdeIcon,  children: [] },
            { id: 11, type: "link", active: false, name: $localize`REST`, link: "/enterprise-clients/rest", iconSvg: restIcon, children: [] }
            ]
    },
    {
        id: 3, type: "section", active: false, name: $localize`Access Management`, link: "/access-management", iconSvg: groupIcon, children: [
            { id: 12, type: "link", active: false, name: $localize`Users`, link: "/access-management/users", iconSvg: user, children: [] },
            { id: 13, type: "link", active: false, name: $localize`Groups`, link: "/access-management/groups", iconSvg: group, children: [] },
          // { id: 6, type: "link", active: false, name: $localize`ViewGroups`, link: "/access-management/view-groups", iconSvg: groupIcon, children: [] },
           //{ id: 10, type: "link", active: false, name: $localize`ViewGroups`, link: "/access-management/view-groups-test", iconSvg: groupIcon, children: [] }
        ]
    },

    { id: 4, type: "link", active: false, name: $localize`Audit Log`, link: "/audit-logs", iconSvg: auditLog, children:[]},
    { id: 5, type: "link", active: false, name: $localize`Setting`, link: "/settings", iconSvg: configurationIcon, children:[]}
    /* usage example for sections with child elements
        {
            id: 1, type: "section", active: false, name: $localize`:main-menu reports link:Reports`, link: "/reports", iconSvg: chartIcon, children: [
                { id: 4, type: "link", active: false, name: $localize`:main-menu transactions link:Transactions`, link: "/reports/transactions", iconSvg: transactionsIcon, children: [] },
            ]
        },
        */
];
