import { CommonModule, DatePipe } from '@angular/common';
import {
  Component,
  inject,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import {
  ClientSidePaginatedDataSource,
  PaginatedDataSource,
  TaasModalDialogueComponent,
  ToastService,
} from 'utimaco-common-ui-angular';
import { UserRoleService } from '../../../core/services/role/user-role.service';
import { Role } from '../../../core/models/roles.enum';
import { TaasUserService } from '../../../core/services/taas-user-service.service';
import { ModalDialogueTranslations } from 'utimaco-common-ui-angular/lib/taas-modal-dialogue/models/modal-dialogue-translations';
import { TaasButtonComponent } from 'utimaco-common-ui-angular';
import { TaasKmipClientService } from '../../../core/services/kmip/taas-kmip-client.service';
import { TableData } from 'utimaco-common-ui-angular';
import { TableColumn } from 'utimaco-common-ui-angular';
import {
  SortableTableComponent,
  TaasModalLoadingSpinnerComponent,
} from 'utimaco-common-ui-angular';
import { take } from 'rxjs';
import { RouterLink } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { KmipClient } from '../../../core/models/kmip-client';
import { downloadClientCert, handleCertificatePaste } from '../../../shared/components/taas-kmip-util';

interface KmipClientFormData {
  clientId: string;
  password: string;
  confirmPassword: string;
  certificate: string;
}

interface KmipClientResponse {
  success: boolean;
  message: string;
  data?: any;
  status?: number;
  body?: {
    data: KmipClient[];
  };
  error?: {
    message: string;
    code: number;
  };
}

interface KmipTableData extends TableData {
  id: number;
  clientId: string;
  userId: string;
  createdBy: string;
  creationDate: string;
  certificateExpirationDate: string;
  lastModified: string;
  certificate: string;
  csr: string;
}

@Component({
  selector: 'app-taas-kmip',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TaasModalDialogueComponent,
    TaasButtonComponent,
    SortableTableComponent,
    TaasModalLoadingSpinnerComponent,
    RouterLink,
    MatIconModule
  ],
  providers: [DatePipe],
  templateUrl: './taas-kmip.component.html',
  styleUrl: './taas-kmip.component.scss',
})
export class TaasKmipComponent implements OnInit {
  @ViewChild(TaasModalDialogueComponent) modalRef!: TaasModalDialogueComponent;
  @ViewChild('tableOptions', { static: true }) tableOptions!: TemplateRef<any>;
  @ViewChild('clientIdLinkTemplate', { static: true }) clientIdLinkTemplate!: TemplateRef<any>;
  private datePipe = inject(DatePipe);
  private _toast = inject(ToastService);
  private userRoleService = inject(UserRoleService);
  
  // Role-based permission flags
  public canCreateKmipClient: boolean = false;
  public canDownloadCertificate: boolean = false;
  public canDeleteKmipClient: boolean = false;
  public canViewKmipClientDetails: boolean = false;
  public canEditInstance: boolean = false;
  public hasAnyInstancePermissions: boolean = false;

  // Current user info for ownership checks
  private currentUserId: string | null = null;
  private currentUserEmail: string | null = null;
  
  isDialogOpen = false;
  clientForm: FormGroup;

  public tableData: KmipTableData[] = [];
  public tableColumns: TableColumn[] = [];
  public isKmipClientsLoading: boolean = false;
  formSubmitted = false;
  clientErrorMsg!: string;
  public dataSource!: PaginatedDataSource;

  // Custom translations to change the Confirm button text to "Save"
  customModalTranslations: ModalDialogueTranslations = {
    confirmLabelText: $localize`:AddKmipClient\:button label for confirm:Save`,
    cancelLabelText: 'Cancel',
  };
  isKmipError: boolean = false;

  // Service injections
  private fb = inject(FormBuilder);
  private kmipCientService = inject(TaasKmipClientService);
  private taasUserService = inject(TaasUserService);

  constructor() {
    this.clientForm = this.fb.group(
      {
        clientId: ['', Validators.required],
        password: ['', [Validators.required, Validators.minLength(12)]],
        confirmPassword: ['', Validators.required],

        certificate: ['', Validators.required],
      },
      {
        validators: [this.passwordMatchValidator.bind(this)],
      }
    );
  }

  passwordMatchValidator(form: AbstractControl): ValidationErrors | null {
    const password = form.get('password');
    const confirmPassword = form.get('confirmPassword');

    if (password?.value !== confirmPassword?.value) {
      confirmPassword?.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    } else {
      confirmPassword?.setErrors(null);
      return null;
    }
  }



  /**
   * Initialize role-based permissions for KMIP client operations
   * According to the EKMaaS role matrix:
   * - ORG_ADMIN and KMIP_INSTANCE_CREATOR can create KMIP clients
   * - Users with at least INSTANCE_VIEWER can view client details
   * - Users with INSTANCE_EDITOR or higher can download certificates
   * - Only INSTANCE_ADMIN and ORG_ADMIN can delete KMIP clients
   * - Users can only see KMIP instances they have permission for
   */
  private initializePermissions(): void {
    // Check if user has any instance-level permissions
    this.hasAnyInstancePermissions = this.userRoleService.hasAnyRole([
      Role.INSTANCE_VIEWER,
      Role.INSTANCE_EDITOR,
      Role.INSTANCE_ADMIN,
      Role.ORG_ADMIN
    ]);

    // User can create KMIP clients if they are org admin or KMIP instance creator
    this.canCreateKmipClient = this.userRoleService.hasAnyRole([
      Role.ORG_ADMIN,
      Role.KMIP_INSTANCE_CREATOR
    ]);

    // User can view KMIP client details if they have at least viewer permissions
    this.canViewKmipClientDetails = this.hasAnyInstancePermissions;

    // User can download certificates if they have any instance permissions
    // All users with any level of access should be able to download certificates
    this.canDownloadCertificate = this.userRoleService.hasAnyRole([
      Role.INSTANCE_VIEWER,
      Role.INSTANCE_EDITOR,
      Role.INSTANCE_ADMIN,
      Role.ORG_ADMIN
    ]);

    // User can edit instances if they have editor or higher permissions
    this.canEditInstance = this.userRoleService.hasAnyRole([
      Role.INSTANCE_EDITOR,
      Role.INSTANCE_ADMIN,
      Role.ORG_ADMIN
    ]);

    // Only admins can delete KMIP clients
    this.canDeleteKmipClient = this.userRoleService.hasAnyRole([
      Role.INSTANCE_ADMIN,
      Role.ORG_ADMIN
    ]);

    // Log permission status for debugging
    console.log('KMIP Permissions:', {
      hasAnyInstancePermissions: this.hasAnyInstancePermissions,
      canCreateKmipClient: this.canCreateKmipClient,
      canViewKmipClientDetails: this.canViewKmipClientDetails,
      canDownloadCertificate: this.canDownloadCertificate,
      canEditInstance: this.canEditInstance,
      canDeleteKmipClient: this.canDeleteKmipClient
    });
  }

  /**
   * Load current user information for ownership checks
   * Uses the existing TaasUserService which already handles JWT parsing
   */
  private async loadCurrentUserInfo(): Promise<void> {
    try {
      // Use the existing TaasUserService instead of duplicating JWT logic
      const userDetails = await this.taasUserService.getCurrentUserDetails();

      this.currentUserId = userDetails.email; // Use email as primary identifier
      this.currentUserEmail = userDetails.email;

      console.log('Current user info loaded:', {
        userId: this.currentUserId,
        email: this.currentUserEmail,
        name: `${userDetails.givenName} ${userDetails.familyName}`
      });

    } catch (error) {
      console.error('Failed to load current user info:', error);
      this.currentUserId = null;
      this.currentUserEmail = null;
    }
  }

  async ngOnInit(): Promise<void> {
    // Initialize permissions
    this.initializePermissions();

    // Load current user info for ownership checks
    await this.loadCurrentUserInfo();

    // Subscribe to role changes
    this.userRoleService.userRoles$.subscribe(() => {
      this.initializePermissions();
    });

    this.generateTableColumns();
    this.getKmipClient();
  }

  generateTableColumns(): void {
    this.tableColumns = [
      {
        header: $localize`Client ID`,
        valuePropertyName: 'clientId',
        sortable: true,
        template: this.clientIdLinkTemplate
      },
      {
        header: $localize`Creation By`,
        valuePropertyName: 'createdBy',
        sortable: true
      },
      {
        header: $localize`Creation Date`,
        valuePropertyName: 'creationDate',
        sortable: true,
        initialSortDirection: 'desc',
      },
      {
        header: $localize`Certificate Expiration Date`,
        valuePropertyName: 'certificateExpirationDate',
        sortable: true
      },

      {
        header: $localize`Last modified`,
        valuePropertyName: 'lastModified',
        sortable: true
      },
      {
        header: $localize`Action`,
        valuePropertyName: 'action',
        template: this.tableOptions
      },
    ];
  }

  openRegistrationDialog(): void {
    // Check if user has permission to create KMIP clients
    if (!this.canCreateKmipClient) {
      this._toast.error('Permission Denied', 'You do not have permission to create KMIP clients');
      return;
    }
    
    this.formSubmitted = false;
    this.clientForm.reset();
    this.modalRef.openModal();
  }

  onPasteCertificate(event: ClipboardEvent): void {
    handleCertificatePaste(event, this.clientForm.get('certificate'));
  }



  saveForm(): void {
    this.formSubmitted = true;

    if (this.clientForm.valid) {
      console.log('Form data:', this.clientForm.value as KmipClientFormData);
      this.sendClientRegistrationToBackend(this.clientForm.value);
    } else {
      Object.keys(this.clientForm.controls).forEach((key) => {
        const control = this.clientForm.get(key);
        control?.markAsTouched();
      });

      setTimeout(() => {
        if (!this.modalRef.modalVisible) {
          this.modalRef.openModal();
        }
      }, 0);
    }
  }

  private sendClientRegistrationToBackend(
    clientData: KmipClientFormData
  ): void {
    console.log('send', clientData);
    this.kmipCientService.registerClient(clientData)
      .subscribe({
        next: (res: KmipClientResponse) => {
          console.log('Success11:', res);
          if (res.success && res.data) {
            this._toast.success('Success ', res.message);
            this.getKmipClient();
            this.modalRef.closeModal();
          } else {
            this.handleKMIPError(res, 'Error creating KMIP Client');
            this.getKmipClient();
          }
        },
        error: (error: KmipClientResponse) => {
          console.error('Error:', error);
          this.handleKMIPError(error, 'Error creating KMIP Client');
        },
      });
  }

  getKmipClient(): void {
    this.isKmipClientsLoading = true;
    this.kmipCientService
      .getkmipClientsData()
      .pipe(take(1))
      .subscribe((res: KmipClientResponse) => {
        if ((res.status === 200 || res.status === 201) && res.body) {
          this.isKmipClientsLoading = false;

          // The backend now filters data based on user permissions
          // No need for frontend filtering

          // Map KmipClient to KmipTableData
          this.tableData = res.body.data.map(
            (tableData: KmipClient): KmipTableData => ({
              ...tableData,
              clientId: tableData.clientId,
              createdBy: tableData.userId,
              creationDate:
                this.datePipe.transform(
                  tableData.creationDate,
                  'yyyy-MM-dd HH:mm:ss'
                ) || '',
              certificateExpirationDate:
                this.datePipe.transform(
                  tableData.certExpirationDate,
                  'yyyy-MM-dd HH:mm:ss'
                ) || '',
              csr: tableData.certificate,
              lastModified: this.datePipe.transform(
                tableData.lastModified,
                'yyyy-MM-dd HH:mm:ss'
              ) || ''
            })
          );
          
          console.log('Received KMIP clients from backend:', this.tableData.length);
          this.dataSource = new ClientSidePaginatedDataSource(this.tableData);
          return;
        }
        this.handleKMIPError(res, 'Error while trying to fetch KMIP Clients');
      });
  }

  // Frontend filtering methods removed as this is now handled by the backend
  // which receives user context (ID and roles) with each request


  handleKMIPError(res: KmipClientResponse, errorTitle: string): void {
    this.isKmipClientsLoading = false;

    if (res.error && res.error.message) {
      this.tableData = [];
      console.log('create kmip client error', res);
      this._toast.error(errorTitle, res.error.message + " Please provide valid input.");
    } else if (res.status === 503) {
      this.tableData = [];
      this._toast.error(
        errorTitle,
        'Service unavailable, Please try again later.'
      );
    } else if (res.status === 500) {
      this.tableData = [];
      console.log("500-Internal Server error.")
      this._toast.error(errorTitle, 'Something went wrong on our end. Please try again later.');
    } else {
      this.tableData = [];
      this._toast.error(errorTitle, 'We are experiencing technical difficulties. Please try again later.');
    }
  }

  downloadClientCert(kmipClient: KmipClient): void {
    // Check if user has permission to download certificates
    if (!this.canDownloadCertificate) {
      this._toast.error('Permission Denied', 'You do not have permission to download client certificates');
      return;
    }
    
    downloadClientCert(kmipClient.csr, kmipClient.clientId);
  }

  showError(controlName: string, formGroup?: FormGroup): boolean {
    // Get the control with null check
    const control: AbstractControl | null = formGroup
      ? formGroup.get(controlName)
      : this.clientForm.get(controlName);

    // If control is null, return false
    if (!control) return false;

    // Check invalid state with explicit boolean conversion
    return control.invalid &&
      (control.dirty ||
        control.touched ||
        this.formSubmitted);
  }


  toggleViewKmipClientModal(_t6: KmipClient, arg1: boolean) {
    // Check if user has permission to view KMIP client details
    if (!this.canViewKmipClientDetails) {
      this._toast.error('Permission Denied', 'You do not have permission to view KMIP client details');
      return;
    }
    
    console.log(_t6, arg1);
    throw new Error('Method not implemented.');
  }
  
  toggleDeleteKmipClientModal(_t6: KmipClient, arg1: boolean) {
    // Check if user has permission to delete KMIP clients
    if (!this.canDeleteKmipClient) {
      this._toast.error('Permission Denied', 'You do not have permission to delete KMIP clients');
      return;
    }
    
    console.log(_t6, arg1);
    throw new Error('Method not implemented.');
  }
}
