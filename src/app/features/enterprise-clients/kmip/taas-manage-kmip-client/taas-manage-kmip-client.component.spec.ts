import { ComponentFixture, TestBed } from '@angular/core/testing';

import { TaasManageKmipClientComponent } from './taas-manage-kmip-client.component';

describe('TaasManageKmipClientComponent', () => {
  let component: TaasManageKmipClientComponent;
  let fixture: ComponentFixture<TaasManageKmipClientComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TaasManageKmipClientComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(TaasManageKmipClientComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
