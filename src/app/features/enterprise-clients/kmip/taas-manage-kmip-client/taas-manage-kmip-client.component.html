<div class="kmip-client-view-container">
  <!-- <mat-card class="certificate-details">
    <mat-card-content> -->
  <form [formGroup]="clientForm" class="details-grid">
    <div class="left-section">
      <mat-form-field>
        <mat-label i18n="ViewkmipClient: label field for Client Id">Client ID</mat-label>
        <input matInput formControlName="clientId" readonly>
      </mat-form-field>
      <mat-form-field>
        <mat-label i18n="ViewkmipClient: label field for Created By">Created By</mat-label>
        <input matInput formControlName="createdBy" readonly>
      </mat-form-field>
      <mat-form-field>
        <mat-label i18n="ViewkmipClient: label field for Creation Date">Creation Date</mat-label>
        <input matInput formControlName="creationDate" readonly>
      </mat-form-field>
      <mat-form-field>
        <mat-label i18n="ViewkmipClient: label field for Last Modified">Last Modified</mat-label>
        <input matInput formControlName="lastModified" readonly>
      </mat-form-field>
    </div>
    <div class="right-section">
      <mat-form-field>
        <mat-label i18n="ViewkmipClient: label field for Certificate Start Date">Certificate Start Date</mat-label>
        <input matInput formControlName="certificateStartDate" readonly>
      </mat-form-field>
      <mat-form-field>
        <mat-label i18n="ViewkmipClient: label field for Certificate Expiration Date">Certificate Expiration Date</mat-label>
        <input matInput formControlName="certificateExpirationDate" readonly>
      </mat-form-field>

      <div class="action-buttons">
        <lib-taas-button [buttonText]="'Download Client Cert'" [addClass]="''" [buttonType]="'primary'"
          (buttonPressedEvent)="downloadClientCert()"
          i18n="AddKmipClient: Button text for Download Kmip Client Cert"></lib-taas-button>
        <lib-taas-button [buttonText]="'Change Client Cert'" [addClass]="''" [buttonType]="'primary'"
          (buttonPressedEvent)="changeClientCert()"
          i18n="AddKmipClient: Button text for Change Client Cert"></lib-taas-button>
        <lib-taas-button [buttonText]="'Change Password'" [addClass]="''" [buttonType]="'primary'"
          (buttonPressedEvent)="changePassword()"
          i18n="AddKmipClient: Button text for Change Client Password"></lib-taas-button>

        <!-- <lib-taas-button [buttonText]="'Download Client Cert'" [addClass]="''" [buttonType]="'primary'"
            (buttonPressedEvent)="deleteClientCert()"
            i18n="AddKmipClient: Button text for Download Kmip Client Cert"></lib-taas-button> -->

      </div>
    </div>
  </form>

<ng-template #changeCertificateTemplate>
  <form [formGroup]="changeCertificateForm" class="change-certificate-form">
    <mat-form-field>
      <mat-label i18n="UpdatekmipClient: label field for New Certificate Entry">New Certificate</mat-label>
      <textarea
        matInput
        formControlName="newCertificate"
        placeholder="Paste your new certificate here"
        rows="5"
        (paste)="onPasteCertificate($event)"
      ></textarea>
      @if(hasError('newCertificate', 'required', changeCertificateForm)) {
        <mat-error i18n="UpdatekmipClient: Error message for Certificate Validation">
          Client Certificate is required
        </mat-error>
      }
    </mat-form-field>
    <div class="cert-note" i18n="UpdatekmipClient: Info field for Client Certificate Note">
      Note: Client Id and Common Name in the Client Certificate should match
    </div>
  </form>
</ng-template>



<ng-template #changePasswordTemplate>
  <form [formGroup]="changePasswordForm" class="change-password-form">
    <mat-form-field>
      <mat-label i18n="UpdatekmipClient: label field for New Password Entry">New Password</mat-label>
      <input
        matInput
        type="password"
        formControlName="newPassword"
        placeholder="Enter new password"
      >
      @if(hasError('newPassword', 'required', changePasswordForm)) {
        <mat-error i18n="UpdatekmipClient: Error message for Password Validation">
          Password is required
        </mat-error>
      }
      @if(hasError('newPassword', 'minlength', changePasswordForm)) {
        <mat-error i18n="UpdatekmipClient: Error message for Password Validation">
          Password must be at least 8 characters long
        </mat-error>
      }
    </mat-form-field>

    <mat-form-field>
      <mat-label i18n="UpdatekmipClient: label field for New Confirm Password Entry">Confirm Password</mat-label>
      <input
        matInput
        type="password"
        formControlName="confirmPassword"
        placeholder="Confirm new password"
      >
      @if(hasError('confirmPassword', 'required', changePasswordForm)) {
        <mat-error i18n="UpdatekmipClient: Error message for Confirm Password Validation">
          Confirm Password is required
        </mat-error>
      }
      @if(changePasswordForm.hasError('passwordMismatch') &&
          (changePasswordForm.get('confirmPassword')?.dirty ||
           changePasswordForm.get('confirmPassword')?.touched ||
           formSubmitted)) {
        <mat-error i18n="UpdatekmipClient: Error message for Password mismatch">
          Passwords do not match
        </mat-error>
      }
    </mat-form-field>
  </form>
</ng-template>

<!-- <lib-taas-modal-loading-spinner uniqueId="kmip-client-spinner" [spinnerVisible]="isClientDetailsLoading"
content="KMIP Client Data loading..">
</lib-taas-modal-loading-spinner> -->

<lib-taas-modal-loading-spinner
    uniqueId="kmip-client-spinner"
    [spinnerVisible]="spinnerVisible"
    [content]="spinnerContent">
  </lib-taas-modal-loading-spinner>

  <div class="kmip-objects-table">
    <lib-sortable-table uniqueId="kmip-object" [dataSource]="dataSource" [columns]="tableColumns">
    </lib-sortable-table>
  </div>
  <!-- Modal Dialogue Component -->
  <!-- <lib-taas-modal-dialogue
  [title]="currentModalTitle"
  uniqueId="KmipClientManagement"
  [width]="'500px'"
  [height]="'auto'"
  [showCloseButton]="true"
  [modalVisible]="modalVisible"
  [customTranslations]="customModalTranslations"
  (confirm)="currentModalContent === changeCertificateTemplate ? saveNewPassword():saveNewCertificate()">
  <ng-container *ngIf="currentModalContent">
    <ng-container *ngTemplateOutlet="currentModalContent"></ng-container>
  </ng-container>
</lib-taas-modal-dialogue> -->

<lib-taas-modal-dialogue
[title]="currentModalTitle"
uniqueId="KmipClientManagement"
[width]="'500px'"
[height]="'auto'"
[showCloseButton]="true"
[modalVisible]="modalVisible"
[customTranslations]="customModalTranslations"
(confirm)="templateComparison()">
<ng-container *ngIf="currentModalContent">
  <ng-container *ngTemplateOutlet="currentModalContent"></ng-container>
</ng-container>
</lib-taas-modal-dialogue>

</div>
