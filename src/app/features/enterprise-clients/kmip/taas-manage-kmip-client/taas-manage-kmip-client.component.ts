import {
  Component,
  inject,
  Input,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatTableModule } from '@angular/material/table';
import {
  ClientSidePaginatedDataSource,
  PaginatedDataSource,
  TaasButtonComponent,
  TaasModalDialogueComponent,
  ToastService,
} from 'utimaco-common-ui-angular';
import { TableColumn } from 'utimaco-common-ui-angular';
import { TableData } from 'utimaco-common-ui-angular';
import { SortableTableComponent } from 'utimaco-common-ui-angular';
import {
  catchError,
  finalize,
  map,
  Observable,
  of,
  Subscription,
  throwError,
} from 'rxjs';
import {
  ClientDetails,
  KmipClientResponse,
  TaasKmipClientService,
  UpdateResponse,
} from '../../../../core/services/kmip/taas-kmip-client.service';
import { ActivatedRoute } from '@angular/router';
import { HttpErrorResponse } from '@angular/common/http';
import { ModalDialogueTranslations } from 'utimaco-common-ui-angular/lib/taas-modal-dialogue/models/modal-dialogue-translations';
import {
  downloadClientCert,
  handleCertificatePaste,
} from '../../../../shared/components/taas-kmip-util';
import {TaasModalLoadingSpinnerComponent} from 'utimaco-common-ui-angular';


interface KmipObjectsTableData extends TableData {
  id: number;
  objectName: string;
  createdBy: string;
  creationDate: string;
  Algorithm: string;
  UUID: string;
}

interface ErrorResponse {
  status?: number;
  statusText?: string;
  error?: {
    message?: string;
    code?: number | string;
  };
  message?: string;
}

@Component({
  selector: 'app-taas-manage-kmip-client',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatCardModule,
    MatGridListModule,
    MatTableModule,
    TaasButtonComponent,
    SortableTableComponent,
    TaasModalDialogueComponent,
    TaasModalLoadingSpinnerComponent
  ],
  providers: [DatePipe],
  templateUrl: './taas-manage-kmip-client.component.html',
  styleUrl: './taas-manage-kmip-client.component.scss',
})
export class TaasManageKmipClientComponent implements OnInit, OnDestroy {
  @ViewChild(TaasModalDialogueComponent) modalRef!: TaasModalDialogueComponent;
  @ViewChild('tableOptions', { static: true })
  tableOptions!: TemplateRef<unknown>;

  @ViewChild('changeCertificateTemplate', { static: true })
  changeCertificateTemplate!: TemplateRef<any>;

  @ViewChild('changePasswordTemplate', { static: true })
  changePasswordTemplate!: TemplateRef<any>;

  @Input() clientId!: string;
  public dataSource!: PaginatedDataSource;

  public tableData: KmipObjectsTableData[] = [];
  public tableColumns: TableColumn[] = [];
  public isClientDetailsLoading: boolean = false;
  public errorMessage: string = '';

  private route = inject(ActivatedRoute);
  private subscriptions: Subscription = new Subscription();
  private _toast = inject(ToastService);
  certificate!: string;

  //public isDialogOpen = false;
  public modalVisible = false;
  public currentModalContent: TemplateRef<any> | null = null;
  public currentModalTitle: string = '';
  private isUpdating = false;
  clientCertChange = false;
  clientPasswordChange = false;
  // Forms for different modals
  clientForm: FormGroup;
  changeCertificateForm: FormGroup;
  changePasswordForm: FormGroup;

  customModalTranslations: ModalDialogueTranslations = {
    confirmLabelText: $localize`:AddKmipClient\:button label for confirm:Save`,
    cancelLabelText: 'Cancel',
  };
  formSubmitted = false;
  private datePipe = inject(DatePipe);
  constructor(
    private fb: FormBuilder,
    private kmipClientService: TaasKmipClientService
  ) {
    this.clientForm = this.fb.group({
      clientId: [''],
      createdBy: [''],
      creationDate: [''],
      lastModified: [''],
      certificateStartDate: [''],
      certificateExpirationDate: [''],
    });
    // Form for changing certificate
    this.changeCertificateForm = this.fb.group({
      newCertificate: ['', [Validators.required]],
    });

    // Form for changing password
    this.changePasswordForm = this.fb.group(
      {
        newPassword: ['', [Validators.required, Validators.minLength(12)]],
        confirmPassword: ['', [Validators.required]],
      },
      { validators: [this.passwordMatchValidator.bind(this)] }
    );
  }

  passwordMatchValidator(form: AbstractControl): ValidationErrors | null {
    const password = form.get('newPassword');
    const confirmPassword = form.get('confirmPassword');

    if (password?.value !== confirmPassword?.value) {
      confirmPassword?.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    } else {
      confirmPassword?.setErrors(null);
      return null;
    }
  }

  ngOnInit() {
    this.clientId = String(this.route.snapshot.paramMap.get('id'));
    console.log('this.selectedKmipClientId', this.clientId);
    if (this.clientId) {
      this.fetchClientDetails(this.clientId);
    }
    this.generateTableColumns();
    this.dataSource = new ClientSidePaginatedDataSource(this.tableData);
  }

  fetchClientDetails(clientId: string): void {
    this.isClientDetailsLoading = true;

    const detailsSubscription = this.kmipClientService
      .getClientDetails(clientId)
      .pipe(
        map((response: KmipClientResponse): ClientDetails | null => {
          if (response?.body?.data) {
            console.log(response?.body?.data);
            this.certificate = response.body.data.certificate;
            return {
              clientId: response.body.data.clientId,
              userId: response.body.data.userId,
              creationDate: response.body.data.creationDate,
              lastModified: response.body.data.lastModified,
              certStartDate: response.body.data.certStartDate,
              certExpirationDate: response.body.data.certExpirationDate,
              certificate: response.body.data.certificate,
            };
          }
          return null;
        }),
        catchError((error: ErrorResponse): Observable<null> => {
          this.handleError(error);
          return of(null);
        }),
        finalize(() => {
          this.isClientDetailsLoading = false;
        })
      )
      .subscribe({
        next: (details: ClientDetails | null) => {
          if (details) {
            console.log(details);
            this.clientForm.patchValue({
              clientId: details.clientId || '',
              createdBy: details.userId || '',
              creationDate: this.datePipe.transform(
                details.creationDate,
                'yyyy-MM-dd HH:mm:ss'
              ) || '',
              //lastModified: details.lastModified || '',
              lastModified: this.datePipe.transform(
                details.lastModified,
                'yyyy-MM-dd HH:mm:ss'
              ) || '',
              certificateStartDate: this.datePipe.transform(
                details.certStartDate,
                'yyyy-MM-dd HH:mm:ss'
              ) || '',
              certificateExpirationDate: this.datePipe.transform(
                details.certExpirationDate,
                'yyyy-MM-dd HH:mm:ss'
              ) || '',
            });
          }
        },
      });

    this.subscriptions.add(detailsSubscription);
  }

  private handleError(error: ErrorResponse): void {
    const errorConfig: { [key: number]: string } = {
      0: 'We are experiencing technical difficulties. Please try again later.',
      404: 'The requested resource could not be found.',
      500: 'Internal Server error. Please try again later.',
    };
    const errorMessage =
      error?.error?.message ||
      error?.message ||
      (error.status && errorConfig[error.status]) ||
      'An unexpected error occurred.';
    this._toast.error('Error', errorMessage);
    console.error('Full error details:', error);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  get spinnerVisible(): boolean {
    if(this.isUpdating){
      this.isClientDetailsLoading=false;
    }
    return this.isClientDetailsLoading || this.isUpdating;
  }

  get spinnerContent(): string {
    if (this.isClientDetailsLoading) {
      console.log("isClientDetailsLoading");
      return 'KMIP Client Data loading...';
    }
    if (this.isUpdating) {
      console.log("isUpdating");
      return 'Updating KMIP Client Data...';
    }
    return '';
  }

  generateTableColumns(): void {
    this.tableColumns = [
      {
        header: $localize`Object Name`,
        valuePropertyName: 'objectName',
        sortable: true,
      },
      {
        header: $localize`Creation By`,
        valuePropertyName: 'createdBy',
        sortable: true,
      },
      {
        header: $localize`Creation Date`,
        valuePropertyName: 'creationDate',
        sortable: true,
        initialSortDirection: 'desc',
      },
      {
        header: $localize`Algorithm`,
        valuePropertyName: 'algorithm',
        sortable: true,
      },
      {
        header: $localize`UUID`,
        valuePropertyName: 'uuid',
        sortable: true,
      },
    ];
  }

  downloadClientCert() {
    downloadClientCert(this.certificate, this.clientId);
    console.log('Download Client Cert', this.certificate);
  }

  changeClientCert() {
    console.log('Change Client Cert');
    this.changeCertificateForm.reset();
    this.currentModalContent = this.changeCertificateTemplate;
    console.log('this.currentModalContent', this.currentModalContent);
    console.log(
      'this.changeCertificateTemplate',
      this.changeCertificateTemplate
    );
    this.currentModalTitle = $localize`Change Client Certificate`;
    this.modalVisible = true;
    if (this.modalRef) {
      this.modalRef.modalVisible = true;
      this.modalRef.openModal();
    }
  }

  changePassword() {
    console.log('Change Password');
    this.changePasswordForm.reset();
    this.currentModalContent = this.changePasswordTemplate;
    console.log('this.currentModalContent', this.currentModalContent);
    this.currentModalTitle = $localize`Change Password`;
    this.modalVisible = true;

    if (this.modalRef) {
      this.modalRef.modalVisible = true;
      this.modalRef.openModal();
    }
  }

  saveNewCertificate() {
    if (this.isUpdating) return;
    this.formSubmitted = true;
    console.log('saveNewCertificate');
    console.log('this.currentModalContent', this.currentModalContent);
    Object.keys(this.changeCertificateForm.controls).forEach((key) => {
      const control = this.changeCertificateForm.get(key);
      control?.markAsTouched();
    });
    if (this.changeCertificateForm.valid) {
      const newCertificate =
        this.changeCertificateForm.get('newCertificate')?.value;

      this.isUpdating = true;
      this.clientCertChange = true;
      this.updateKmipClient('newCertificate', newCertificate);
    } else {
      this._toast.error(
        'Validation Error',
        'Please provide a valid certificate.'
      );
      setTimeout(() => {
        if (!this.modalRef.modalVisible) {
          this.modalRef.openModal();
        }
      }, 0);
    }
  }

  saveNewPassword() {
    if (this.isUpdating) return;

    this.formSubmitted = true;
    console.log('saveNewPassword');
    console.log('this.currentModalContent', this.currentModalContent);

    Object.keys(this.changePasswordForm.controls).forEach((key) => {
      const control = this.changePasswordForm.get(key);
      control?.markAsTouched();
    });
    if (this.changePasswordForm.valid) {
      const newPassword = this.changePasswordForm.get('newPassword')?.value;
      this.isUpdating = true;
      this.clientPasswordChange = true;
      this.updateKmipClient('newPassword', newPassword);
    } else {
      this._toast.error(
        'Validation Error',
        'Please ensure passwords match and meet requirements.'
      );
      setTimeout(() => {
        if (!this.modalRef.modalVisible) {
          this.modalRef.openModal();
        }
      }, 0);
    }
  }

  hasError(
    controlName: string,
    errorType: string,
    formGroup: FormGroup
  ): boolean {
    const control = formGroup.get(controlName);
    return !!(
      control &&
      control.hasError(errorType) &&
      (control.dirty || control.touched || this.formSubmitted)
    );
  }

  onPasteCertificate(event: ClipboardEvent): void {
    handleCertificatePaste(event, this.changeCertificateForm.get('newCertificate'));
  }

  deleteClientCert() {
    console.log('Delete Client Cert');
  }
  // Close modal method
  closeModal() {
    this.modalVisible = false;
    if (this.modalRef) {
      this.modalRef.modalVisible = false;
      this.modalRef.closeModal();
    }
  }
  templateComparison() {
    if (this.currentModalContent === null) {
      console.error('Modal content is null');
      return;
    }

    // console.log('Current Modal Content:', this.currentModalContent);
    // console.log('Change Certificate Template:', this.changeCertificateTemplate);
    console.log(
      'Strict Equality:',
      this.currentModalContent === this.changeCertificateTemplate
    );

    // console.log('Current Modal Content Reference:', this.currentModalContent.elementRef);
    // console.log('Change Certificate Template Reference:', this.changeCertificateTemplate.elementRef);

    return this.currentModalContent === this.changeCertificateTemplate
      ? this.saveNewCertificate()
      : this.saveNewPassword();
  }

  updateKmipClient(
    updateType: 'newPassword' | 'newCertificate',
    updateValue: string
  ): void {
    const updatePayload: any = {
      clientId: this.clientId,
    };
    updatePayload[updateType] = updateValue;

    this.kmipClientService
      .updateKmipClient(updatePayload)
      .pipe(
        catchError((error: HttpErrorResponse) => {
          let errorMessage = `An unexpected error occurred while updating the ${
            updateType === 'newPassword' ? 'password' : 'certificate'
          }.`;
          if(error.status === 0){
            console.log(error.status);
            errorMessage="We are experiencing technical difficulties. Please try again later.";
          }
          else if (error.error && typeof error.error === 'object') {
            const backendError = error.error as ErrorResponse;
            if (backendError.message) {
              errorMessage = backendError.message;
            }
          }
          console.error('Update Error:', error);

          //this._toast.error('Update Failed', errorMessage);
          return throwError(() => new Error(errorMessage));
        }),
        finalize(() => {
          this.isUpdating = false;
        })
      )
      .subscribe({
        next: (response: UpdateResponse) => {
          this.closeModal();

          const successMessage = response.message;
          console.log(successMessage);

          const successToast =
            updateType === 'newPassword'
              ? 'Password Updated'
              : 'Certificate Updated';

          this._toast.success(
            successToast,
            successMessage || 'Updated Successfully'
          );

          this.fetchClientDetails(this.clientId);
        },
        error: (err) => {
          this._toast.error('Update Failed', err.message);
          this.isUpdating = false;
        },
      });
  }
}
