<div class="kmip-container">
  <!-- <button (click)="openModal()" class="btn btn-primary">Open Client Form</button> -->

  @if (canCreateKmipClient) {
    <lib-taas-button buttonType="highlight" icon="add" (buttonPressedEvent)="openRegistrationDialog()">
      <span i18n="AddKmipClient: Button text for Add Kmip Client">Add KMIP Client</span>
    </lib-taas-button>
  }

  <ng-template #tableOptions let-row="row">
    <div class="action-button-list">
      <!-- <button type="button" routerLink="/enterprise-clients/kmip/{{row.id}}" class="table-button manage-button"
        i18n="CloudListing: Button text for Manage Keys">
        Manage Keys
      </button> -->
      @if (canDownloadCertificate) {
        <lib-taas-button buttonType="primary" (buttonPressedEvent)="downloadClientCert(row)">
          <span i18n="KmipClientListing: Button text for Download Kmip Client Certificate">Download Client Cert</span>
        </lib-taas-button>
      }

      <!-- <lib-taas-button [buttonText]="'Refresh Client Cert'" [addClass]="'refresh-button'" [buttonType]="'primary'"
        (buttonPressedEvent)="refreshClientCert(row, true)"
        i18n="KmipClientListing: Button text for Refresh Kmip Client Cert"></lib-taas-button> -->

      <!-- <lib-taas-button [buttonText]="'View'" [addClass]="'view-button'" [buttonType]="'primary'"
        (buttonPressedEvent)="toggleViewKmipClientModal(row, true)"
        i18n="KmipClientListing: Button text for View Kmip Client"></lib-taas-button> -->

      @if (canDeleteKmipClient) {
        <lib-taas-button buttonType="danger" (buttonPressedEvent)="toggleDeleteKmipClientModal(row, true)">
          <span i18n="KmipClientListing: Button text for Delete Kmip Client">Delete</span>
        </lib-taas-button>
      }
    </div>
  </ng-template>

  <ng-template #clientIdLinkTemplate let-row="row">
    <a [routerLink]="['/enterprise-clients/kmip/', row.clientId]" class="client-id-link">{{ row.clientId }}</a>
  </ng-template>

  <lib-taas-modal-loading-spinner uniqueId="kmip-spinner" [spinnerVisible]="isKmipClientsLoading" content="KMIP Clients loading..">
  </lib-taas-modal-loading-spinner>

  <!-- Content display based on loading state and permissions -->
  @if (isKmipClientsLoading) {
    <!-- Loading state is handled by the spinner above -->
  } @else if (!hasAnyInstancePermissions) {
    <!-- No permissions message -->
    <div class="no-permissions-message" data-testid="kmip.container.nopermissions">
      <div class="message-content">
        <mat-icon>info</mat-icon>
        <h3 i18n="KmipListing: No permissions title">No Permissions</h3>
        <p i18n="KmipListing: No permissions message">
          You do not have permission to view any KMIP instances.
          Please contact your administrator to request access.
        </p>
      </div>
    </div>
  } @else if (hasAnyInstancePermissions && tableData.length === 0) {
    <!-- No data message when user has permissions but no KMIP clients -->
    <div class="no-data-message" data-testid="kmip.container.nodata">
      <div class="message-content">
        <mat-icon>folder_open</mat-icon>
        <h3 i18n="KmipListing: No data title">No KMIP Clients</h3>
        <p i18n="KmipListing: No data message">
          No KMIP clients found for the instances you have access to.
        </p>
      </div>
    </div>
  } @else {
    <!-- Table - only show when user has permissions and data exists -->
    <lib-sortable-table uniqueId="kmip-clients" [dataSource]="dataSource" [columns]="tableColumns">
    </lib-sortable-table>
  }

  <!--
  @if(!isKmipClientsLoading && tableData.length>=0){
    <div>
      <lib-sortable-table uniqueId="kmip-clients" [data]="tableData" [columns]="tableColumns">
      </lib-sortable-table>
    </div>
  }  -->

  <!-- 
    <lib-taas-modal-loading-spinner uniqueId="kmipClientLoadingSpinner" width="300px" height="300px" [spinnerVisible]="isKmipClientsLoading && tableData.length===0">
    </lib-taas-modal-loading-spinner>
  -->

  <!-- TODO add i18n support for all texts including title of modal e.g. by translating in the component class and adding as variable for now -->

  <lib-taas-modal-dialogue [title]="'Add New KMIP Client'" uniqueId="AddKmipClient" width="500px" height="auto"
    [modalVisible]="false" uniqueId="client-form-modal" [customTranslations]="customModalTranslations"
    (confirm)="saveForm()">

    <!-- Form Content -->
    <form [formGroup]="clientForm">
      <div>
        <label for="clientId" class="form-label" i18n="AddkmipClient: label field for Client Id">Client ID</label>
        <input type="text" class="form-control" id="clientId" formControlName="clientId">
        @if(showError('clientId')){
        <div class="text-danger mt-1" i18n="AddkmipClient: label field for Client Id Validation Error">
          Client ID is required
        </div>
        }
        <!-- <div *ngIf="clientForm.get('clientId')?.invalid &&
                 (clientForm.get('clientId')?.dirty || clientForm.get('clientId')?.touched || formSubmitted)"
          class="text-danger mt-1" i18n="AddkmipClient: label field for Client Id Validation Error">
          Client ID is required
        </div> -->
        <!-- @if(isKmipError){
          <div>
            {{clientErrorMsg}}
          </div>
        } -->
      </div>

      <div class="mb-3">
        <label for="password" class="form-label" i18n="AddkmipClient: label field for Password">Password</label>
        <input type="password" class="form-control" id="password" formControlName="password">
        @if(showError('password')){
        <div class="text-danger mt-1" i18n="AddkmipClient: label field for Password Validation Error">
          Password is required and must be at least 12 characters
        </div>
        }
        <!-- <div *ngIf="clientForm.get('password')?.invalid &&
                 (clientForm.get('password')?.dirty || clientForm.get('password')?.touched || formSubmitted)"
          class="text-danger mt-1" i18n="AddkmipClient: label field for Password Validation Error">
          Password is required and must be at least 12 characters
        </div> -->
      </div>

      <div class="mb-3">
        <label for="confirmPassword" class="form-label" i18n="AddkmipClient: label field for Confirm Password">Confirm
          Password</label>
        <input type="password" class="form-control" id="confirmPassword" formControlName="confirmPassword">
        @if(showError('confirmPassword')){
        <div class="text-danger mt-1" i18n="AddkmipClient: label field for Confirm Password Validation Error">
          Please confirm your password
        </div>
        }
        <!-- <div
          *ngIf="clientForm.get('confirmPassword')?.invalid &&
                 (clientForm.get('confirmPassword')?.dirty || clientForm.get('confirmPassword')?.touched || formSubmitted)"
          class="text-danger mt-1" i18n="AddkmipClient: label field for Confirm Password Validation Error">
          Please confirm your password
        </div> -->
        @if(clientForm.hasError('passwordMismatch') &&
        (clientForm.get('confirmPassword')?.dirty || clientForm.get('confirmPassword')?.touched || formSubmitted)){
        <div class="text-danger mt-1" i18n="AddkmipClient: label field for Password Mismatch Validation Error">
          Passwords do not match
        </div>
        }

      </div>

      <!--TODO: to be used in future-->
      <!-- <div class="mb-3">
        <label for="certificatePeriod" class="form-label"
          i18n="AddkmipClient: label field for Certificate Period">Certificate Period</label>
        <select class="form-select" id="certificatePeriod" formControlName="certificatePeriod">
          <option value="">Select Period</option>
          <option value="1">1 Year</option>
          <option value="2">2 Years</option>
          <option value="3">3 Years</option>
          <option value="4">4 Years</option>
          <option value="5">5 Years</option>
        </select>
        <div
          *ngIf="clientForm.get('certificatePeriod')?.invalid &&
                 (clientForm.get('certificatePeriod')?.dirty || clientForm.get('certificatePeriod')?.touched || formSubmitted)"
          class="text-danger mt-1" i18n="AddkmipClient: label field for Client Certificate Period Validation Error">
          Certificate Period is required
        </div>
      </div> -->

      <div class="mb-3">
        <label for="certificate" class="form-label" i18n="AddkmipClient: label field for Client Certificate">Client Certificate</label>
        <textarea class="form-control" id="certificate" formControlName="certificate" rows="5"
          (paste)="onPasteCertificate($event)"></textarea>
        @if(showError('certificate')){
        <div class="text-danger mt-1" i18n="AddkmipClient: label field for Client Certificate Validation Error">
          Client Certificate is required
        </div>
        }
        <!-- <div *ngIf="clientForm.get('certificate')?.invalid &&
                   (clientForm.get('certificate')?.dirty || clientForm.get('certificate')?.touched || formSubmitted)"
          class="text-danger mt-1" i18n="AddkmipClient: label field for Client Certificate Validation Error">
          Client Certificate is required
        </div> -->
      </div>
      <div class="cert-note" i18n="AddkmipClient: label field for Client Certificate Note">
        Note: Client Id and Common Name in the Client Certificate should match
      </div>

    </form>
  </lib-taas-modal-dialogue>
</div>