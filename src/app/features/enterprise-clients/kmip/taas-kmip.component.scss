// :host {
//   display: block;
// }

.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  width: 120px;
  min-width: 120px;
  display: inline-block;
}

.form-control,
.form-select {
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid #ced4da;
}

.text-danger {
  color: rgba(244, 67, 54, 0.8);
  //font-size: 0.875rem;
  font-size: 0.8rem;
  margin-top: 5px;
}

textarea {
  resize: vertical;
  min-height: 100px;
}

:host ::ng-deep {
  .modal {
    position: relative;
    margin: auto;
    z-index: 1051;
  }
}

.kmip-container {
  padding-top: 30px;
}

.cert-note {
  font-size: 10px;
  color: grey;
}


.client-id-link {
  color: #0068b4; // Deep blue color
  font-weight: bold;
  text-decoration: none;

  &:hover {
    text-decoration: none;
    color: #004080; // Slightly darker blue on hover
  }
}


.mat-icon {
  vertical-align: middle;
  margin: auto;
  line-height: 0.95;
}

.cloudBtn {
  color: #fff;
  background-color: #0068b4;
  border-color: black;
  float: right;
  margin-bottom: 50px;
}

.btn {
  border-radius: 5px;
  border: none;
  padding: 10px 12px 10px 10px;
}

.action-button-list {
  display: flex;
  gap:10px;
  flex-direction: column;
  ::ng-deep lib-taas-button button.taas-simple-button {
    padding: 5;
    height: 25px;
  }
}

// No permissions and no data message styles
.no-permissions-message,
.no-data-message {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  padding: 2rem;

  .message-content {
    text-align: center;
    max-width: 400px;

    mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #666;
      margin-bottom: 1rem;
    }

    h3 {
      color: #333;
      margin-bottom: 0.5rem;
      font-weight: 500;
    }

    p {
      color: #666;
      line-height: 1.5;
      margin: 0;
    }
  }
}

.no-permissions-message {
  .message-content {
    mat-icon {
      color: #ff9800; // Orange for warning
    }
  }
}

.no-data-message {
  .message-content {
    mat-icon {
      color: #9e9e9e; // Gray for empty state
    }
  }
}
