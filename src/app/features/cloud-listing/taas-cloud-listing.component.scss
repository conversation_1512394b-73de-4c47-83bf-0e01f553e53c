.divContainer {
  //padding: 10px 5px 10px 5px;
  padding: 30px 15px 10px 5px;
  // margin-left:170px;
  // margin-top:50px;
}

.noConfig {
  width: 100%;
  text-align: center;
  padding-top: 50px;
  font-size: 12px;
  color: #666666;
}

.btn {
  border-radius: 5px;
  border: none;
  padding: 5px 10px 5px 10px;
}

.row-padding {
  padding: 5px 0px 10px 0px;

  &.row-last {
    margin-top: 12px
  }
}

.row-icon {
  width: 15px;
  height: 15px;
}

.rowIconBig {
  width: 17px;
  height: 18px;
}

.alignCenter {
  //text-align: center!important;
  padding: 0.5em;
  font-size: small;
}

.mat-icon {
  vertical-align: middle;
  margin: auto;
  line-height: 0.95;
}

.cloudBtn {
  color: #fff;
  background-color: #0068b4;
  border-color: black;
  float: right;
}

a {
  //text-decoration: underline;
  font-weight: 700;
}

a:hover {
  color: #0068b4;
}

.table-button {
  color: #fff;
  background-color: #0068b4;
  border-color: #0068b4;
  font-size: small;
  text-align: center;
  white-space: normal;
  min-width: 135px;
}

.table-button-list {
  margin: auto;
  white-space: normal;
  display: flex;
  flex-direction: column;
  gap: 5px;
  align-items: flex-start;
}

.rowIconBiger {
  width: 20px;
  height: 20px;
  margin-right: 0px;
  //float: right;
}

.iconFont {
  font-size: 20px;
  cursor: pointer;
}

div.disabled {
  pointer-events: none;
  cursor: default;
}

.custom-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: block;
}
