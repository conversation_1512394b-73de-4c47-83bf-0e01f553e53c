<div class="divContainer">
    <!-- add cloud instance btn -->
    @if (canCreateInstance) {
    <lib-taas-button btnId="addCloudInstanceOpen" (click)="toggleAddCloudInstanceModal(true)" buttonType="highlight" icon="add">
      {{ addCloudInstanceLocalized }}
    </lib-taas-button>
    }
    <!-- <div *ngIf="isLoggedIn && !isReadOnly" class="btn pl-1 float-right" data-toggle="modal" title="Settings"
          data-target="#cloudSettingsModal" id="cloudSettingsOpen" (click)="toggleCloudSettingsModal(true)">
          <mat-icon>settings</mat-icon>
    </div> -->
  <div class="row row-padding row-last">
    <div>
      <!-- Template for cloudType column -->
      <ng-template #cloudType let-row="row">
        <img src="assets/icons/{{ row.cloudType }}.svg" title="{{ row.cloudType }}" class="row-icon" alt="type" />
        {{ row.cloudType | appCloudTypeName }}
      </ng-template>

      <!-- Template for Action column -->
      <ng-template #tableOptions let-row="row">
        <div class="table-button-list">
          @if (canManageKeys) {
          <button type="button" routerLink="/managekeys/{{ row.id }}" class="table-button manage-button"
            i18n="CloudListing: Button text for Manage Keys">
            Manage Keys
          </button>
          }
          @if (canEditInstance) {
          <button type="button" class="table-button edit-button" data-toggle="modal" data-target="#deleteCloudInstanceModal"
            (click)="toggleEditCloudInstanceModal(row, true)" (keydown.space)="toggleEditCloudInstanceModal(row, true)"
            (keydown.enter)="toggleEditCloudInstanceModal(row, true)" i18n="CloudListing: Button text for Edit Instance">
            Edit Instance
          </button>
          }
          @if (canDeleteInstance) {
          <button type="button" class="table-button" data-toggle="modal" data-target="#deleteCloudInstanceModal"
            (click)="toggleDeleteCloudInstanceModal(row, true)" (keydown.space)="toggleDeleteCloudInstanceModal(row, true)"
            (keydown.enter)="toggleDeleteCloudInstanceModal(row, true)" i18n="CloudListing: Button text for Delete Instance">
            Delete Instance
          </button>
          }
        </div>
      </ng-template>

      <!-- Cloud Table -->
      @if(tableData.length > 0){
      <div>
        <lib-sortable-table uniqueId="kmaas" [dataSource]="dataSource" [columns]="tableColumns">
        </lib-sortable-table>
      </div>
      }
      <!-- Handle loading of cloud instance data -->
      <lib-taas-modal-loading-spinner uniqueId="cloudListingLoadingSpinner" width="300px" height="300px"
        [spinnerVisible]="isCloudDashboardLoading && tableData.length === 0">
      </lib-taas-modal-loading-spinner>
      @if (!isCloudDashboardLoading && tableData.length === 0) {
      <!-- Handle no cloud instances -->
      <div class="noConfig" i18n="CloudListing: Text for No Cloud Instances">
        No Cloud Instances Available
      </div>
      }

      <!-- handle not logged in -->
      <!-- <div class="noConfig" *ngIf="!isLoggedIn && cloudList.length === 0">Please login to view configured cloud instances</div> -->
    </div>
  </div>
</div>

<!-- cloud settings modal component -->
<!-- @if (isCloudSettingsVisible) {
    <app-cloud-settings *ngIf="isCloudSettingsVisible" (onCancel)="isCloudSettingsVisible=false"></app-cloud-settings>
} -->

<lib-taas-modal-dialogue uniqueId="addCloudInstance" [title]="addCloudInstanceLocalized" height="500px"
  [modalVisible]="isAddCloudInstanceModalVisible" (cancel)="toggleAddCloudInstanceModal(false)" [showFooter]="false"
  #addCloudInstanceModalRef>
  @if(isAddCloudInstanceModalVisible) {
  <app-taas-add-cloud-instance [modalReference]="addCloudInstanceModalRef"></app-taas-add-cloud-instance>
  }
</lib-taas-modal-dialogue>

<lib-taas-modal-dialogue uniqueId="editCloudInstance" [title]="editCloudInstanceLocalized" width="600px" height="500px"
  [modalVisible]="isEditCloudInstanceModalVisible" (cancel)="isEditCloudInstanceModalVisible = false" [showFooter]="false"
  variant="info"
  #editCloudInstanceModalRef>
  @if(isEditCloudInstanceModalVisible) {
  <app-taas-edit-cloud-instance [modalReference]="editCloudInstanceModalRef" [selectedCloud]="selectedCloud"></app-taas-edit-cloud-instance>
  }
</lib-taas-modal-dialogue>

<lib-taas-modal-dialogue uniqueId="deleteCloudInstanceModal" [title]="deleteCloudInstanceLocalized" [content]="getDeleteMessage()"
  variant="danger"
  width="600px" height="200px" [modalVisible]="isDeleteCloudInstanceModalVisible" (cancel)="isDeleteCloudInstanceModalVisible = false"
  (confirm)="onDeleteCloudInstance()">
</lib-taas-modal-dialogue>