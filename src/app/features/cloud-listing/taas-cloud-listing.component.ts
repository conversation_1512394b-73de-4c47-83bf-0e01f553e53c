import {
  <PERSON>mpo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Template<PERSON>ef,
  ViewChild,
  inject,
} from '@angular/core';
import { TaasCloudInstanceService } from '../../core/services/cloud/taas-cloud-instance.service';
import { TaasUserService } from '../../core/services/taas-user-service.service';
import { Title } from '@angular/platform-browser';
import { CommonModule, DatePipe } from '@angular/common';
import { FormatCloudTypeName } from '../../core/pipes/formatCloudTypeName.pipe';
import { RouterLink } from '@angular/router';
import { TaasAddCloudInstanceComponent } from '../add-cloud-instance/taas-add-cloud-instance.component';
import { TaasEditCloudInstanceComponent } from '../edit-cloud-instance/taas-edit-cloud-instance.component';
import { UserRoleService } from '../../core/services/role/user-role.service';
import { Role } from '../../core/models/roles.enum';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import {
  ToastService,
  SortableTableComponent,
  ClientSidePaginatedDataSource,
  PaginatedDataSource,
  TaasModalDialogueComponent,
  TaasButtonComponent,
  TaasModalLoadingSpinnerComponent,
} from 'utimaco-common-ui-angular';
import { Subject, switchMap, take, takeUntil, from, map } from 'rxjs';
import { TableColumn } from 'utimaco-common-ui-angular';
import { HttpResponse } from '@angular/common/http';
import { CloudInstance } from './models/cloud-instance';
import { TableDataItem } from './models/table-data-item';
import { CloudApiResponse } from './models/cloud-api-response';


@Component({
  selector: 'app-taas-cloud-listing',
  standalone: true,
  imports: [
    CommonModule,
    TaasAddCloudInstanceComponent,
    FormatCloudTypeName,
    RouterLink,
    TaasEditCloudInstanceComponent,
    MatInputModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    SortableTableComponent,
    TaasModalDialogueComponent,
    TaasButtonComponent,
    TaasModalLoadingSpinnerComponent
  ],
  providers: [DatePipe],
  templateUrl: './taas-cloud-listing.component.html',
  styleUrl: './taas-cloud-listing.component.scss',
})
export class TaasCloudListingComponent implements OnInit, OnDestroy {
  @ViewChild('tableOptions', { static: true }) tableOptions!: TemplateRef<any>;
  @ViewChild('cloudType', { static: true }) cloudType!: TemplateRef<any>;

  public tableData: TableDataItem[] = []; // Cloud instances list
  public tableColumns: TableColumn[] = [];
  public isCloudDashboardLoading: boolean = false;
  public isReadOnly: boolean = true; // Readonly user or admin
  public dataSource!: PaginatedDataSource;
  
  // Role-based permission flags
  public canCreateInstance: boolean = false;
  public canEditInstance: boolean = false;
  public canDeleteInstance: boolean = false;
  public canManageKeys: boolean = false;
  
  // Selected cloud instance object
  public selectedCloud: CloudInstance = {
    id: -1,
    instanceName: '',
    cloudType: '',
    eskmKeyUser: null,
    configFields: '',
    createdDate: '',
    lastUpdated: '',
  };

  public isAddCloudInstanceModalVisible: boolean = false; // add instance modal flag
  public isEditCloudInstanceModalVisible: boolean = false; // edit instance modal flag
  public isDeleteCloudInstanceModalVisible: boolean = false; // delete confirmation modal flag
  public isCloudSettingsVisible: boolean = false; // cloud settings modal flag
  addCloudInstanceLocalized: string = $localize`:AddCloudInstance\:title: Add Cloud Instance`;
  editCloudInstanceLocalized: string = $localize`:EditCloudInstance\:title: Edit Cloud Instance`;
  deleteCloudInstanceLocalized: string = $localize`:DeleteCloudInstance\:title: Delete Cloud Instance`;

  private ngUnsubscribe = new Subject<void>();

  loggedUser: string = '';
  isLoggedIn: boolean = true;

  private taasCloudInstanceService = inject(TaasCloudInstanceService);
  private _toast = inject(ToastService);
  private title = inject(Title);
  private datePipe = inject(DatePipe);
  private userRoleService = inject(UserRoleService);
  private taasUserService = inject(TaasUserService);

  /**
   * Initialize role-based permissions for this component
   * According to the matrix:
   * - Org admins can view all cloud instances.
   * - Other users can only view instances they have permission for (e.g., created by them).
   * - Org admin and specific instance creators can create instances
   * - Instance admin and org admin can edit instances
   * - Only instance admin and org admin can delete instances
   * - All users with at least viewer permissions can manage keys
   */
  private initializePermissions(): void {
    // Use centralized permission methods from UserRoleService
    this.canCreateInstance = this.userRoleService.canCreateInstanceType('AZURE');
    this.canEditInstance = this.userRoleService.canEditInstances();
    this.canDeleteInstance = this.userRoleService.canDeleteInstances();

    // Use centralized method for key management permissions
    this.canManageKeys = this.userRoleService.canManageKeys();

    // The Add button should be disabled if the user is not an org-admin or azure-instance-creator
    // This is handled by the `canCreateInstance` flag in the template.
  }

  ngOnInit(): void {
    this.title.setTitle($localize`Utimaco TaaS: Enterprise Key Manager`);
    
    // Initialize permissions
    this.initializePermissions();
    
    // Subscribe to role changes
    this.userRoleService.userRoles$.pipe(
      takeUntil(this.ngUnsubscribe)
    ).subscribe(() => {
      this.initializePermissions();
    });
    
    this.generateTableColumns();
    this.getCloudList();
    this.listenForGridUpdate();
  }

  generateTableColumns() {
    this.tableColumns = [
      {
        header: $localize`Instance Name`,
        valuePropertyName: 'instanceName',
        sortable: true,
        initialSortDirection: 'asc',
      },
      {
        header: $localize`Cloud Type`,
        valuePropertyName: 'cloudType',
        sortable: true,
        initialSortDirection: 'asc',
        template: this.cloudType,
      },
      {
        header: $localize`Creation Date`,
        valuePropertyName: 'createdDate',
        sortable: true,
        initialSortDirection: 'asc',
      },
      {
        header: $localize`Last Updated`,
        valuePropertyName: 'lastUpdated',
        sortable: true,
        initialSortDirection: 'asc',
      },
      {
        header: $localize`Action`,
        valuePropertyName: 'action',
        template: this.tableOptions,
      },
    ];
  }

  getCloudList() {
    this.isCloudDashboardLoading = true;
    
    from(this.taasUserService.getCurrentUserDetails()).pipe(
      switchMap(currentUser => 
        this.taasCloudInstanceService.getCloudDashboardData().pipe(
          map(res => ({ res, currentUser }))
        )
      ),
      take(1)
    ).subscribe(({ res, currentUser }) => {
      if ((res.status === 200 || res.status === 201) && res.body) {
        this.isCloudDashboardLoading = false;
        
        // Filter instances based on user role
        if (this.userRoleService.hasRole(Role.ORG_ADMIN)) {
          // Org admins see all instances
          this.tableData = res.body.data;
        } else {
          // Other users see only instances they have created
          this.tableData = res.body.data.filter((instance: TableDataItem) => instance['eskmKeyUser'] === currentUser.email);
        }

        this.tableData.map((tableData: TableDataItem) => {
          tableData.configFields = JSON.parse(tableData.configFields);
          tableData.createdDate =
            this.datePipe.transform(tableData.createdDate, 'YYYY-MM-dd') ??
            '';
          tableData.lastUpdated =
            this.datePipe.transform(tableData.lastUpdated, 'YYYY-MM-dd') ??
            '';
          this.dataSource = new ClientSidePaginatedDataSource(this.tableData);
          return tableData;
        });
        return;
      }
      this.handleCloudListError(res);
    });
    localStorage.removeItem('caList');
  }

  handleCloudListError(res: CloudApiResponse | HttpResponse<unknown>) {
    this.isCloudDashboardLoading = false;
    const errorTitle = $localize`Error while trying to fetch cloud instances`;

    if ('error' in res && res.error?.message) {
      this.tableData = [];
      this._toast.error(errorTitle, res.error.message);
    } else if ('status' in res && res.status === 503) {
      this.tableData = [];
      this._toast.error(
        errorTitle,
        'Service unavailable, Please try again after some time.'
      );
    }

    this.tableData = [];
    this._toast.error(errorTitle, 'Internal Server error');
  }

  listenForGridUpdate() {
    this.taasCloudInstanceService.cloudInstanceListObservable
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(() => {
        this.getCloudList(); // Reload cloud grid in case instances are changed
      });
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }

  toggleCloudSettingsModal(value: boolean) {
    //show/hide cloudSettingsModal
    this.isCloudSettingsVisible = value;
  }

  toggleAddCloudInstanceModal(value: boolean) {
    // Only allow opening the modal if user has proper permissions
    if (value && !this.canCreateInstance) {
      this._toast.warning(
        'Permission Denied',
        'You need proper permissions to add cloud instances'
      );
      return;
    }
    
    //show/hide addCloudInstanceModal
    this.isAddCloudInstanceModalVisible = value;
  }

  toggleEditCloudInstanceModal(cloudInstance: CloudInstance, value: boolean) {
    // Only allow opening the modal if user has proper permissions
    if (value && !this.canEditInstance) {
      this._toast.warning(
        'Permission Denied',
        'You need Admin permission to edit cloud instances'
      );
      return;
    }
    
    //show/hide editCloudInstanceModal
    this.selectedCloud = cloudInstance;
    this.isEditCloudInstanceModalVisible = value;
  }

  toggleDeleteCloudInstanceModal(cloudInstance: CloudInstance, value: boolean) {
    // Only allow opening the modal if user has proper permissions
    if (value && !this.canDeleteInstance) {
      this._toast.warning(
        'Permission Denied',
        'You need Admin permission to delete cloud instances'
      );
      return;
    }
    
    //show/hide warning
    this.selectedCloud = cloudInstance;
    this.isDeleteCloudInstanceModalVisible = value;
  }

  getDeleteMessage(): string {
    const deleteMessage: string = $localize`:DeleteCloudInstance\:delete message: Are you sure you want to delete instance `;
    return deleteMessage + this.selectedCloud.instanceName + '?';
  }

  onDeleteCloudInstance() {
    // Double-check permission before executing delete operation
    if (!this.canDeleteInstance) {
      this._toast.warning(
        'Permission Denied',
        'You need Admin permission to delete cloud instances'
      );
      return;
    }
    
    //call to delete cloud instance
    const errorTitle = $localize`Error while trying to delete Cloud Instance`;

    // TODO: Define proper type for the delete response
    this.taasCloudInstanceService
      .deleteCloudInstance(this.selectedCloud.id)
      .subscribe((res: CloudApiResponse) => {
        if (res.status == 200 || res.status == 201) {
          this._toast.success('Success', res.body?.message ?? '');
          this.taasCloudInstanceService.cloudInstanceChanged();
        } else if (res.error?.message) {
          this._toast.error(errorTitle, res.error.message);
          this.taasCloudInstanceService.cloudInstanceChanged();
        } else if (res.status == 503) {
          this._toast.error(
            errorTitle,
            'Service unavailable, Please try again after some time.'
          );
        } else {
          this._toast.error(errorTitle, 'Internal Server error');
        }
        this.isDeleteCloudInstanceModalVisible = false;
      });
  }
}
