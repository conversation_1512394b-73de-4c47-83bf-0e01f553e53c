<lib-taas-modal-loading-spinner uniqueId="editCloudInstancesLoadingSpinner" width="300px" height="300px" [spinnerVisible]="!isLoaded">
</lib-taas-modal-loading-spinner>

@if (isLoaded) {
<!-- Instance Name -->
<div>
  <div>
    <label
      for="cloudInstanceName"
      i18n="Edit Cloud Instance: label field for Instance Name"
      >Instance Name</label
    >
  </div>
</div>
<div>
  <div>
    <input
      class="largeInputDisabled"
      [(ngModel)]="selectedCloudInstance.instanceName"
      id="editCloudInstanceName"
      [disabled]="true"
    />
  </div>
</div>

<!-- Cloud Type -->
<div>
  <div>
    <label
      for="cloudType"
      i18n="Edit Cloud Instance: label field for Cloud Type"
      >Cloud Type</label
    >
  </div>
</div>
<div class="row">
  <div>
    <input
      class="largeInputDisabled"
      [value]="selectedCloudInstance.cloudType | appCloudTypeName"
      id="editCloudType"
      [disabled]="true"
    />
  </div>
</div>

@if (keyUri &&
selectedCloudInstance.cloudType.toLowerCase().includes('cache-only-key')) {
<div class="row disabled">
  <div>
    <label for="keyUri" i18n="Edit Cloud Instance: label field for Key URI"
      >URL</label
    >
  </div>
  <div>
    <textarea
      class="largeInput sublabel"
      rows="2"
      style="height: 100%"
      readonly
      id="keyUri"
      [ngModel]="keyUri"
    ></textarea>
  </div>
</div>
}

<!-- Dynamic Fields -->
@if (selectedCloudType.configFields && selectedCloudType.configFields.length >
0) { @for (field of selectedCloudType.configFields; track field.jsonField) {
<div>
  @if (field.type !== 'value-array' && field.type !== 'multi-select' &&
  field.type !== 'yes-no') {
  <div>
    <label for="field.jsonField">
      @if (field.required) {<span>*</span>}
      {{ getLocalizedLabel(field) }}
    </label>
  </div>
  @if (!field.noReviewOnEdit) {
  <div [ngClass]="{ disabled: instanceVerified }">
    <app-taas-shared-input
      [inputField]="field"
      [disabled]="field.jsonField === 'accessKeyID' ? true : instanceVerified"
      [value]="selectedCloudInstance.configFields[field.jsonField]"
      id="{{ 'editCloudInsConf' + field.jsonField }}"
      (update)="onSharedFieldUpdate($event, field)"
    >
    </app-taas-shared-input>
  </div>
  } } @if (field.type === 'yes-no') {
  <div [ngClass]="{ disabled: instanceVerified }">
    <div>
      <div>
        <label
          class="togglePadding mr-2"
          for="{{ 'addCloudInsConf' + field.jsonField }}"
        >
          {{ getLocalizedLabel(field) }}
        </label>
      </div>
      <div id="{{ 'addCloudInsConf' + field.jsonField }}">
        <app-taas-shared-input
          [inputField]="field"
          [disabled]="
            field.jsonField === 'accessKeyID' ? true : instanceVerified
          "
          [value]="selectedCloudInstance.configFields[field.jsonField]"
          id="{{ 'editCloudInsConf' + field.jsonField }}"
          (update)="onSharedFieldUpdate($event, field)"
        >
        </app-taas-shared-input>
      </div>
    </div>
  </div>
  } @if (field.type === 'password-field-noshow') {
  <div [ngClass]="{ disabled: instanceVerified }">
    <app-taas-shared-input
      [inputField]="field"
      [disabled]="field.jsonField === 'accessKeyID' ? true : instanceVerified"
      [value]="selectedCloudInstance.configFields[field.jsonField]"
      id="{{ 'editCloudInsConf' + field.jsonField }}"
      (update)="onSharedFieldUpdate($event, field)"
    >
    </app-taas-shared-input>
  </div>
  } @if (field.type === 'value-array') {
  <div>
    <app-taas-shared-input
      [inputField]="field"
      [value]="selectedCloudInstance.configFields[field.jsonField]"
      id="{{ 'editCloudInsConf' + field.jsonField }}"
      [inputLabel]="field.label"
      (update)="onSharedFieldUpdate($event, field)"
      [disabled]="true"
    >
    </app-taas-shared-input>
  </div>
  } @if (field.type === 'multi-select') {
  <div>
    <app-taas-shared-input
      [inputField]="field"
      [value]="selectedCloudInstance.configFields[field.jsonField]"
      id="{{ 'editCloudInsConf' + field.jsonField }}"
      [inputLabel]="field.label"
      [maxLength]="field.maxSelect"
      (update)="onSharedFieldUpdate($event, field)"
    >
    </app-taas-shared-input>
  </div>
  } @if (validateCloudInstance[field.jsonField].msg.length > 0) {
  <div>
    <mat-error>{{ validateCloudInstance[field.jsonField].msg }}</mat-error>
  </div>
  }
</div>
} } @else {
<div
  class="nonEditableNote"
  i18n="Edit Cloud Instance: text for not editable Instance"
>
  Note: This instance is not editable.
</div>
}

<!-- Error list on submission -->
@if (!editInstanceError.value && editInstanceError.msg.length > 0) {
<div>
  <div>
    @for (error of editInstanceError.msg; track error) {
    <mat-error class="{{ editInstanceError.class }}">{{
      error.trim()
    }}</mat-error>
    }
  </div>
</div>
} }

<!-- Edit and cancel btn -->
@if (isLoaded) {
<div class="modal-footer">
  <!-- <a [href]="downloadUrl" download="{{downloadFilename}}.json">Download json</a> -->
  @if (selectedCloudType.configFields && selectedCloudType.configFields.length >
  0) {

  <lib-taas-button
    btnId="editButtonEditCloudInstance"
    buttonType="highlight"
    (click)="onEditCloudInstance()"
    [disabled]="!editCloudBtn"
    >{{ editBtn.text }}
  </lib-taas-button>
  }
  <lib-taas-button
    #editInstanceCancel
    btnId="cancelButtonAddCloudInstance"
    buttonType="primary"
    (click)="triggerCloseModal()"
    i18n="AddCloudInstance: Cancel"
    >{{ cancelLocalized }}
  </lib-taas-button>
</div>
}
