import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  ViewChild,
  ElementRef,
  inject,
} from '@angular/core';
import { TaasCloudInstanceService } from '../../core/services/cloud/taas-cloud-instance.service';
import { MatDialog } from '@angular/material/dialog';
import { DomSanitizer } from '@angular/platform-browser';
import { TaasSharedInputComponent } from '../../shared/components/taas-shared-input/taas-shared-input.component';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule } from '@angular/forms';
import { FormatCloudTypeName } from '../../core/pipes/formatCloudTypeName.pipe';
import { ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import {
  TaasButtonComponent,
  TaasModalDialogueComponent,
  TaasModalLoadingSpinnerComponent,
  ToastService,
} from 'utimaco-common-ui-angular';
import {
  LABEL_TRANSLATIONS,
  VALIDATION_MESSAGES,
} from '../../shared/components/taas-shared-localization';
import { LabelFields } from '../../shared/components/models/label-fields';

@Component({
  selector: 'app-taas-edit-cloud-instance',
  standalone: true,
  imports: [
    TaasSharedInputComponent,
    FormatCloudTypeName,
    CommonModule,
    MatFormFieldModule,
    FormsModule,
    MatInputModule,
    ReactiveFormsModule,
    TaasButtonComponent,
    TaasModalLoadingSpinnerComponent
  ],
  templateUrl: './taas-edit-cloud-instance.component.html',
  styleUrl: './taas-edit-cloud-instance.component.scss',
})
export class TaasEditCloudInstanceComponent implements OnInit {
  @ViewChild('editInstanceCancel') editInstanceCancel!: ElementRef;
  @Input() modalReference!: TaasModalDialogueComponent;

  selectedCloudInstanceId = -1;
  isLoaded = false;
  //basic initialization for selected cloud instance
  selectedCloudInstance: any = {
    id: -1,
    instanceName: '',
    cloudType: '',
    eskmKeyUser: '',
    'eskm-user-password': '',
    configFields: null,
    createdDate: '',
    lastUpdated: '',
    keyFields: [],
  };
  //validation object for fields
  validateCloudInstance: any = {};
  modifiedValueArray: any = {};
  //general error object
  editInstanceError: any = { value: false, msg: [], class: '' };
  //enable/disable submit
  editCloudInstanceEnabled: any = false;
  //selected cloud type object with dynamic fields
  selectedCloudType: any = {
    name: '',
    keyFields: [],
    configFields: [],
    uploadKeyEnabled: true,
    verifyCloudConfigEnabled: true,
  };
  supportedCloudProvidersList: any = [];
  //edit/verify btn text and style
  editBtn = {
    class: 'verifyBtn',
    text:
      $localize`:EditCloudInstance-button text@@editCloudInstanceButton.text:Verify` ||
      'Verify',
  };
  cancelLocalized: string = $localize`:EditCloudInstance\:Cancel: Cancel`;

  //enable disable add btn
  instanceVerified = false;
  //enable disable verify btn
  downloadFilename = '';
  downloadUrl: any;
  editCloudBtn = true;
  keyUri: any;

  @Output() cancel: EventEmitter<any> = new EventEmitter();
  @Input() selectedCloud: any;

  constructor(
    private taasCloudInstanceService: TaasCloudInstanceService,
    //private _toast:ToastService,
    public dialog: MatDialog,
    private sanitizer: DomSanitizer
  ) {}

  private _toast = inject(ToastService);
  getLocalizedLabel(field: LabelFields): string {
    //console.log('field',field);
    return LABEL_TRANSLATIONS[field.jsonField] || field.label;
  }

  ngOnInit(): void {
    //get list of supported cloud types
    this.getCloudInstance(this.selectedCloud);
  }

  onEditCloudInstance() {
    //send edit req on submit
    if (!this.validateEditInstance()) return;
    this.editCloudBtn = false;
    const selectedCloudInstance = JSON.parse(
      JSON.stringify(this.selectedCloudInstance)
    );
    //console.log('edit-selectedCloudInstance',selectedCloudInstance);
    if (this.selectedCloudType.configFields)
      this.selectedCloudType.configFields.map((field: any) => {
        this.multiSelectHandler(field, selectedCloudInstance);
      });
    //Delete instance check
    if (
      !this.editInstanceError.value &&
      this.editInstanceError.msg.length &&
      this.editInstanceError.msg[0].includes(
        $localize`:EditCloudInstance-delete instance warn@@delete_instance_warn:Warning: Instance will be deleted`
      )
    ) {
      this.taasCloudInstanceService
        .deleteCloudInstance(this.selectedCloudInstanceId)
        .subscribe((res) => {
          this.editCloudBtn = true;
          this.deleteInstanceHandler(res); //handle deletion of an instance
        });
    } else {
      //send update instance api request
      this.taasCloudInstanceService
        .updateCloudInstance(
          selectedCloudInstance,
          this.selectedCloudInstanceId,
          this.editBtn.class == 'verifyBtn'
        )
        .subscribe((res) => {
          //console.log("after update url hit",res);
          this.editCloudBtn = true;
          this.editInstanceError.msg = [];
          this.updateInstanceHandler(res);
        });
    }
  }

  multiSelectHandler(field: any, selectedCloudInstance: any) {
    //handle multi-select input merge
    //console.log('multiSelectHandler',selectedCloudInstance);
    if (field.type == 'value-array' || field.type == 'multi-select') {
      //handle value array type
      const modifiedValue = this.modifiedValueArray[field.jsonField].join(',');
      if (this.modifiedValueArray[field.jsonField].length)
        selectedCloudInstance.configFields[field.jsonField] =
          selectedCloudInstance.configFields[field.jsonField].length
            ? selectedCloudInstance.configFields[field.jsonField] +
              ',' +
              modifiedValue
            : modifiedValue;
    }
  }

  deleteInstanceHandler(res: any) {
    //handle deletion of an instance
    //console.log('deleteInstanceHandler',res);
    if (res.status == 200 || res.status == 201) {
      //success delete
      this.editInstanceError.value = true;
      this.editInstanceError.msg = [];
      this.editInstanceError.class = '';
      this.editInstanceCancel.nativeElement.click();
      this._toast.success('Success', res.body.message);
      //fire observable to update cloud instances
      this.taasCloudInstanceService.cloudInstanceChanged();
    } else if (res.error && res.error.message) {
      //failure
      this._toast.error('Error', res.error.message);
      this.taasCloudInstanceService.cloudInstanceChanged();
    } else if (res.status == 503) {
      this._toast.error(
        'Error',
        'Service unavailable, Please try again after some time.'
      );
    } else {
      this._toast.error('Error', 'Internal Server error');
    }
  }

  updateInstanceHandler(res: any) {
    //handle update instance response
    //console.log('updateInstanceHandler',res);
    if (res.status == 200 || res.status == 201) {
      //success update
      switch (this.editBtn.class) {
        case 'verifyBtn':
          this.editBtn.class = 'cloudBtn';
          this.editBtn.text =
            $localize`:EditCloudInstance-button text for update@@updateCloudInstanceButton.text:Update` ||
            'Update';
          this.editInstanceError.value = false;
          this.instanceVerified = true;
          if (res.body && res.body.message) {
            this.editInstanceError.msg = [res.body.message];
            this.editInstanceError.class = 'text-success';
          }
          break;
        default: //failure
          this.editInstanceError.value = true;
          this.editInstanceError.msg = [];
          this.editInstanceError.class = '';
          this._toast.success('Success', res.body.message);
          //fire observable to update cloud instances
          this.taasCloudInstanceService.cloudInstanceChanged();
          this.triggerCloseModal();
          break;
      }
    } else if (res.error && res.error.message) {
      if (res.error.message.includes('updated partially'))
        this.getCloudInstance(this.selectedCloud);
      this.editInstanceError.value = false;
      this.editInstanceError.msg.push(res.error.message);
      //this.editInstanceError.msg=res.error.message.split(',');
      this.editInstanceError.class = '';
      //this._toast.error('Error',res.error.message);
    } else {
      this.editInstanceError.value = false;
      if (res.error.error) this.editInstanceError.msg.push(res.error.error);
      //this.editInstanceError.msg=res.error.error.split(',');
      else if (res.status == 503)
        this.editInstanceError.msg = ['Internal Server error'];
      else this.editInstanceError.msg = ['Internal Server error'];
      this.editInstanceError.class = '';
    }
  }

  onSharedFieldUpdate(event: any, field: any) {
    //on dynamic field update
    //console.log('onSharedFieldUpdate',field);
    if (
      (field.type == 'value-array' || field.type == 'multi-select') &&
      Array.isArray(event.event.target.value) &&
      event.event.target.value.length == 2
    ) {
      this.multiSelectUpdateHandler(field, event);
    } else
      this.selectedCloudInstance.configFields[field.jsonField] =
        event.event.target.value;
  }

  //handle updating of value array and multi select inputs
  multiSelectUpdateHandler(field: any, event: any) {
    //console.log('multiSelectUpdateHandler',field);
    const confValues1: any = [],
      confValues2: any = [];
    event.event.target.value[0].forEach((elm: any) => {
      if (elm) confValues1.push(elm.trim());
    });
    event.event.target.value[1].forEach((elm: any) => {
      if (elm) confValues2.push(elm.trim());
    });
    //add validation and styling for serviceAccountName
    if (
      field.jsonField == 'serviceAccountName' &&
      !confValues1.length &&
      !confValues2.length
    ) {
      this.editInstanceError.value = false;
      this.editInstanceError.msg = [
        $localize`:EditCloudInstance-warning on update inputs@@warn_update_value_array:Warning: Instance will be deleted if no service account are present`,
      ];
      this.editInstanceError.class = 'font-italic';
    } else {
      this.editInstanceError.value = true;
      this.editInstanceError.msg = [];
      this.editInstanceError.class = '';
    }
    this.selectedCloudInstance.configFields[field.jsonField] =
      confValues1.join(',');
    this.modifiedValueArray[field.jsonField] = confValues2;
  }

  // openHelpDialog() {//help page director
  //   if (this.selectedCloudType.uploadKeyEnabled)
  //     this.dialog.open(HelpComponent, {
  //       width: '95%',
  //       data: { nodeName: 'Edit Cloud Instance' },
  //       panelClass: 'help-dialog'
  //     });
  //   else
  //     this.dialog.open(HelpComponent, {
  //       width: '95%',
  //       data: { nodeName: 'Edit External Cloud Instance' },
  //       panelClass: 'help-dialog'
  //     });
  // }

  validateEditInstance() {
    //validate edit instance on submit
    let status = true;
    //console.log('validateEditInstance');
    if (this.selectedCloudType.configFields)
      this.selectedCloudType.configFields.forEach((field: any) => {
        this.validateStep(
          this.selectedCloudInstance.configFields[field.jsonField],
          field,
          { required: field.required, maxLength: field.maxLength }
        );
      });
    status = this.editCloudInstanceEnabled;
    return status;
  }

  validateStep(value: any, field: any, validator: any) {
    //validate single change
    let result = true;
    //console.log('validateStep',validator);
    const maxLength = validator.maxLength > 0 ? validator.maxLength : 50;
    if (field.type !== 'yes-no') {
      if (
        (field.type == 'value-array' || field.type == 'multi-select') &&
        validator.required
      ) {
        const configFields =
          this.selectedCloudInstance.configFields[field.jsonField];
        this.modifiedValueArray[field.jsonField] = this.modifiedValueArray[
          field.jsonField
        ]
          ? this.modifiedValueArray[field.jsonField].filter(
              (data: any) => data != ''
            )
          : [];
        const newConfigFields = this.mergeNewConfigFields(
          configFields,
          this.modifiedValueArray[field.jsonField]
        );
        this.validateMultiSelectField(
          newConfigFields,
          field,
          maxLength,
          configFields
        );
      } else if (
        (validator.required && field.type != 'value-array' && !value) ||
        value.trim() == ''
      ) {
        this.validateCloudInstance[field.jsonField].value = false;
        this.validateCloudInstance[field.jsonField].msg = [
          VALIDATION_MESSAGES.FIELD_REQUIRED,
        ];
      } else if (value && value.length > maxLength) {
        this.validateCloudInstance[field.jsonField].value = false;
        this.validateCloudInstance[field.jsonField].msg = [
          VALIDATION_MESSAGES.VALIDATION_CHECK_MSG +
            maxLength +
            VALIDATION_MESSAGES.CHARACTER,
        ];
      } else {
        this.validateCloudInstance[field.jsonField].value = true;
        this.validateCloudInstance[field.jsonField].msg = [];
      }
    }
    Object.keys(this.validateCloudInstance).forEach((key) => {
      //check validations
      if (this.validateCloudInstance[key].value == false) {
        result = false;
      }
    });

    this.editCloudInstanceEnabled = result;
  }

  //check and set valudation msgs for valuearray and multiselect field
  validateMultiSelectField(
    newConfigFields: any,
    field: any,
    maxLength: any,
    configFields: any
  ) {
    //console.log('validateMultiSelectField',newConfigFields,configFields);
    if (!newConfigFields || newConfigFields == '') {
      this.validateCloudInstance[field.jsonField].value = false;
      this.validateCloudInstance[field.jsonField].msg = [
        VALIDATION_MESSAGES.FIELD_REQUIRED,
      ];
    } else if (
      this.hasDuplicates(this.modifiedValueArray[field.jsonField], configFields)
    ) {
      this.validateCloudInstance[field.jsonField].value = false;
      this.validateCloudInstance[field.jsonField].msg = [
        VALIDATION_MESSAGES.UNIQUE_CHECK,
      ];
    } else if (
      this.modifiedValueArray[field.jsonField].filter(
        (d: any) => d.length > maxLength
      ).length
    ) {
      this.validateCloudInstance[field.jsonField].value = false;
      this.validateCloudInstance[field.jsonField].msg =
        VALIDATION_MESSAGES.VALIDATION_CHECK_MSG +
        maxLength +
        VALIDATION_MESSAGES.CHARACTER;
    } else {
      this.validateCloudInstance[field.jsonField].value = true;
      this.validateCloudInstance[field.jsonField].msg = '';
    }
  }

  mergeNewConfigFields(value: any, newValueArr: any) {
    //merge the new config fields with old
    //console.log('mergeNewConfigFields',newValueArr,value);
    const newValue = newValueArr.join(',');
    if (!value) return newValue;
    if (!newValue) return value;
    if (value && newValue) return value + ',' + newValue;
  }

  hasDuplicates(list: any, oldList: any) {
    //check for value array duplicates
    let hasDuplicates = new Set(list).size !== list.length;
    if (hasDuplicates) return hasDuplicates;
    else {
      list.forEach((d: any) => {
        if (oldList.includes(d)) hasDuplicates = true;
      });
      return hasDuplicates;
    }
  }

  getCloudInstance(selectedCloud: any) {
    //fetch cloud instance based on id
    //console.log('getCloudInstance',selectedCloud);
    if (selectedCloud) {
      this.isLoaded = false;
      this.selectedCloudInstanceId = selectedCloud.id;
      this.editInstanceError.msg = [];
      this.taasCloudInstanceService
        .getCloudInstance(selectedCloud.id)
        .subscribe((res) => {
          this.getCloudInstanceResponseHandler(res);
        });
    }
  }

  //handle get cloud instance response
  getCloudInstanceResponseHandler(res: any) {
    //console.log('getCloudInstanceResponseHandler',res);
    if ((res.status == 200 || res.status == 201) && res.body) {
      this.selectedCloudInstance = res.body.data;
      this.getSupportedCloud(); //fetch cloud providers
      this.selectedCloudInstance.configFields = JSON.parse(
        this.selectedCloudInstance.configFields
      );
      if (
        this.selectedCloudInstance.cloudType
          .toLowerCase()
          .includes('cache-only-key')
      ) {
        this.keyUri =
          window.location.protocol +
          '//' +
          window.location.hostname +
          ':' +
          window.location.port +
          '/byok/api/v1/cloud/instance/' +
          this.selectedCloudInstance.instanceName +
          '/HYOK/';
      }
    } else if (res.error && res.error.message) {
      this.isLoaded = true;
      this.editInstanceError.value = false;
      this.editInstanceError.msg.push(res.error.message);
      //this.editInstanceError.msg=res.error.message.split(',');
      this.editInstanceError.class = '';
    } else if (res.status == 503) {
      this.isLoaded = true;
      this.editInstanceError.value = false;
      this.editInstanceError.msg = [
        'Service unavailable, Please try again after some time.',
      ];
      this.editInstanceError.class = '';
    } else {
      this.isLoaded = true;
      this.editInstanceError.value = false;
      this.editInstanceError.msg = ['Internal Server error'];
      this.editInstanceError.class = '';
    }
  }

  getSupportedCloud() {
    //get supported cloud providers list
    //console.log('getSupportedCloud');
    this.taasCloudInstanceService
      .getSupportedCloudProviders()
      .subscribe((res) => {
        this.isLoaded = true;
        if (
          (res.status == 200 || res.status == 201) &&
          res.body &&
          res.body.data.cloudProviders
        ) {
          this.setCloudProvidersFields(res);
          // this.initDownloadInstance();
        } else if (res.error && res.error.message) {
          this.editInstanceError.value = false;
          this.editInstanceError.msg.push(res.error.message);
          //this.editInstanceError.msg=res.error.message.split(',');
          this.editInstanceError.class = '';
        } else if (res.status == 503) {
          this.editInstanceError.value = false;
          this.editInstanceError.msg = [
            'Service unavailable, Please try again after some time.',
          ];
          this.editInstanceError.class = '';
        } else {
          this.editInstanceError.value = false;
          this.editInstanceError.msg = ['Internal Server error'];
          this.editInstanceError.class = '';
        }
      });
  }

  //set fields after getSupportedCloudProviders
  setCloudProvidersFields(res: any) {
    //console.log('setCloudProvidersFields',res);
    this.supportedCloudProvidersList = res.body.data.cloudProviders;
    this.supportedCloudProvidersList.forEach(
      (data: { name: any; configFields: any[] }) => {
        if (data.name == this.selectedCloudInstance.cloudType) {
          if (data.configFields)
            data.configFields.forEach((field) => {
              this.validateCloudInstance[field.jsonField] = {
                value: true,
                msg: '',
              };
              if (field.type == 'password-field' && field.noReviewOnEdit) {
                data.configFields.find(
                  (item) => item.jsonField == field.jsonField
                ).type = 'password-field-noshow';
              }
            });
          this.editCloudInstanceEnabled = false;
          this.selectedCloudType = data;
          //console.log('edit-this.selectedCloudType',this.selectedCloudType);
          if (!this.selectedCloudType.verifyCloudConfigEnabled) {
            this.editBtn.class = 'cloudBtn';
            this.editBtn.text =
              $localize`:EditCloudInstance-button text for update@@onVerified.text:Update` ||
              'Update';
          }
        }
      }
    );
  }

  initDownloadInstance() {
    //set instance fields for json download
    //console.log('initDownloadInstance');
    const jsonObject: any = {
      'Instance Id': this.selectedCloudInstance.id,
      'Instance Name': this.selectedCloudInstance.instanceName,
    };
    const selectedCloudInstance = JSON.parse(
      JSON.stringify(this.selectedCloudInstance)
    );
    //console.log('initDownloadInstance-selectedCloudInstance',selectedCloudInstance);
    if (this.selectedCloudType.configFields)
      this.selectedCloudType.configFields.map((field: any) => {
        //     this.multiSelectHandler(field,selectedCloudInstance);
        jsonObject[field.label] =
          selectedCloudInstance.configFields[field.jsonField];
      });
    this.downloadFilename =
      this.selectedCloudInstance.instanceName +
      '_' +
      this.selectedCloudInstance.id.toLocaleString();
    const url = this.sanitizer.bypassSecurityTrustResourceUrl(
      'data:text/json;charset=UTF-8,' +
        encodeURIComponent(JSON.stringify(jsonObject, null, '\t'))
    );
    this.downloadUrl = url;
  }

  public triggerCloseModal() {
    this.modalReference?.closeModal();
  }
}
