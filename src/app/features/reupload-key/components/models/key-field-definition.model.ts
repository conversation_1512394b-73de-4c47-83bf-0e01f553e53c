// src/app/features/reupload-key/components/models/key-field-definition.model.ts
export interface KeyFieldValue {
  id: string | number;
  value: string;
}

export interface KeyFieldDefinition {
  type: 'text-field' | 'date-picker' | 'date-time-picker' | 'name-value-array' | 'name-value_editable-array' | 'select' | 'yes-no' | 'multi-select' | 'grid-view' | 'value-array'; // Added 'value-array' based on usage
  jsonField: string;
  label: string; // Added based on usage in getLocalizedKeyFieldsLabel and dropdownOptionList
  required: boolean;
  maxLength?: number | null; // Added based on validateFields
  values?: KeyFieldValue[]; // For select types
  // Added optional properties based on template usage
  editable?: boolean;
  isImportField?: boolean;
  maxSelect?: number;
  commonLabel?: string;
}