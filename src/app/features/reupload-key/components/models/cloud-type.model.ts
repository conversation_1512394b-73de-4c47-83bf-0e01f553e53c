// src/app/features/reupload-key/components/models/cloud-type.model.ts
import { KeyFieldDefinition } from './key-field-definition.model';

export interface CloudType {
  name: string; // Added based on usage in getDropdownData
  keyFields: KeyFieldDefinition[];
  eskmKeyFields: KeyFieldDefinition[];
  fetchCloudKeyDetails: boolean; // Added based on fetchCloudKeyDetails usage
  fetchESKMKeyDetails: boolean; // Added based on fetchCloudKeyDetails usage
  // Add other potential properties if known
}