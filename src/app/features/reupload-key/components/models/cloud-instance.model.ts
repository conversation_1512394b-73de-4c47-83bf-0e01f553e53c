// src/app/features/reupload-key/components/models/cloud-instance.model.ts
import { KeyFieldDefinition } from './key-field-definition.model';

// TODO: Define ConfigField interface if the structure is known or can be determined.
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type ConfigField = any;

export interface CloudInstance {
  cloudID: number;
  cloudType: string;
  id: number;
  eskmKeyUser: string;
  instanceName: string;
  keyFields: KeyFieldDefinition[];
  eskmKeyFields: KeyFieldDefinition[];
  configFields: ConfigField[];
}