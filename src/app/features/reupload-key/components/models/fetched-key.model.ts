// src/app/features/reupload-key/components/models/fetched-key.model.ts

// TODO: Define the structure more accurately if possible.
// This interface represents the key data received as input.
export interface FetchedKey {
  status?: boolean; // Used in cloudInstance setter
  keySource: string;
  eskmKeyName?: string; // Derived from keySource
  keyAlgoSize?: string; // Set from search association or default
  eskmKeyOwner?: string; // Set from search association
  keyId?: string; // Assumed based on SelectedKey
  keyName?: string; // Assumed based on SelectedKey
  // Add other potential properties based on the 'value' input structure
  [key: string]: any; // Allow other properties until fully defined
}