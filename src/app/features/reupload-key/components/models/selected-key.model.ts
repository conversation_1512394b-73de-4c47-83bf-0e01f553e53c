// src/app/features/reupload-key/components/models/selected-key.model.ts

// TODO: Define the structure more accurately if possible, especially for keyFields and eskmKeyAttributes.
// Using Record<string, any> for now as the exact structure depends heavily on the KeyFieldDefinition.
export interface SelectedKey {
  name: string;
  keyName: string;
  keyOwner: string;
  algorithm: string;
  keyLength: number | string; // Changed to allow string based on line 154
  cloudKeyName: string;
  keyFields: Record<string, any>; // Represents dynamic key fields based on CloudType
  eskmKeyAttributes: Record<string, any>; // Represents dynamic ESKM attributes based on CloudType
  keySource: string;
  keyId: string;
}