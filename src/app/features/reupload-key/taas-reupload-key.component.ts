import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  ViewChild,
  HostListener,
} from '@angular/core';

import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { TaasCloudInstanceService } from '../../core/services/cloud/taas-cloud-instance.service';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { MatRadioModule } from '@angular/material/radio';
import { MatStepperModule } from '@angular/material/stepper';

import { ToText } from '../../core/pipes/toText';
import { TaasSharedInputComponent } from '../../shared/components/taas-shared-input/taas-shared-input.component';

import {
  TaasButtonComponent,
  TaasModalDialogueComponent,
  TaasModalLoadingSpinnerComponent,
  ToastService,
} from 'utimaco-common-ui-angular';
import { FIELD_KEY_TRANSLATIONS } from '../../shared/components/taas-shared-localization';
import { KeyFields } from '../../shared/components/models/key-fields';

@Component({
  selector: 'app-taas-reupload-key',
  templateUrl: './taas-reupload-key.component.html',
  styleUrls: ['./taas-reupload-key.component.scss'],
  imports: [
    TaasSharedInputComponent,
    MatIcon,
    MatIconModule,
    MatFormFieldModule,
    FormsModule,
    CommonModule,
    MatDialogModule,
    ToText,
    MatStepperModule,
    MatRadioModule,
    TaasButtonComponent,
    TaasModalLoadingSpinnerComponent
  ],
  standalone: true,
})
export class TaasReuploadKeyComponent implements OnInit {
  constructor(
    private cloudInstanceService: TaasCloudInstanceService,
    public dialog: MatDialog,
    private _toast: ToastService
  ) {}
  @ViewChild('confirmationCancel') confirmationCancel: any; //confirmation cancel toggle btn element
  @Input() modalReference!: TaasModalDialogueComponent;
  @Input() set cloudInstance(value: any) {
    //set inputs on cloud instance change
    this.selectedCloudInstance = value;
    if (this.selectedCloudInstance.id) {
      this.getDropdownData(); //fetch data for required dropdown
      if (this.fetchedKey.status) {
        //fetch search key association
        this.cloudInstanceService
          .getSearchSelectAssociation(
            this.selectedCloudInstance.id,
            this.fetchedKey.eskmKeyName
          )
          .subscribe((res) => {
            this.searchAssociationResponseHandler(res);
          });
      }
    }
  }
  @Input() set cloudType(value: any) {
    //set input and initialize on cloudType change
    this.selectedCloudType = value;
    if (value) if (this.selectedCloudType.keyFields) this.resetKeyFields();
    if (this.selectedCloudType.eskmKeyFields) this.resetEskmKeyFields();
  }
  @Input() set key(value: any) {
    //set fetched key object
    value['keyAlgoSize'] = '';
    this.eskmKeyFieldsLoaded = false;
    this.fetchedKey = JSON.parse(JSON.stringify(value));
    this.fetchedKey['eskmKeyName'] =
      value.keySource.split('(ESKM').length > 1
        ? value.keySource.split('(ESKM')[0]
        : '';
  }
  @Input() selectedAction: any;
  @Input() keyList: any;
  oldKeyAttributes: any;
  selectedCloudType: any;
  fetchedKey: any;
  modifiedValueArray: any = {};
  viewName = '';
  selectedKey: any = {
    name: '',
    keyName: '',
    keyOwner: '',
    algorithm: '',
    keyLength: 0,
    cloudKeyName: '',
    keyFields: {},
    eskmKeyAttributes: {},
    keySource: '',
    keyId: '',
  };
  existingUploadKeySubmitEnabled = false;
  eskmKeyAttributes: any = [];
  selectedCloudInstance: any = {
    cloudID: -1,
    cloudType: '',
    id: -1,
    eskmKeyUser: '',
    instanceName: '',
    keyFields: [],
    eskmKeyFields: [],
    configFields: [],
  };
  keyVersionListObservable: any;
  reUploadBtn = true;
  editUploadBtn = true;
  eskmKeyFieldsLoaded = false;
  dropdownOptionList: any = {}; //options list for needed dropdown inputs
  //confirmationbox modal data object
  confirmation = {
    //confirmation popup details
    header: '',
    msg: '',
    visible: false,
  };
  @Output() cancel: EventEmitter<any> = new EventEmitter();
  @Output() createNewVersion: EventEmitter<any> = new EventEmitter();

  // fieldKeyFieldTranslations:any = {
  //   enabled: $localize`:ManageCloudKeys@@field.enabled:Enabled`,
  //   keyVault: $localize`:ManageCloudKeys@@field.keyVault:Key Vault`,
  //   activationDate: $localize`:ManageCloudKeys@@field.activationDate:Activation Date`,
  //   expirationDate: $localize`:ManageCloudKeys@@field.expirationDate:Expiration Date`,
  //   tags: $localize`:ManageCloudKeys@@field.tags:Tags`,
  // };
  //field validation status object
  validateSelectedKey: any = {
    cloudKeyName: { value: true, msg: '' },
  };

  sfCasListReady = true;
  sfUploadConfirmation = {
    //sfUpload confirmation popup data object
    header: $localize`:ReUpload Key-Header for confirmation box@@alert_header:Alert!`,
    msgHead: null,
    msgBody: null,
    msgTail: null,
    selectedKey: null,
  };

  getLocalizedKeyFieldsLabel(field: KeyFields): string {
    //console.log('reupload-field',field);
    return FIELD_KEY_TRANSLATIONS[field.jsonField] || field.label;
  }

  ngOnInit(): void {
    this.keyVersionListObservable =
      this.cloudInstanceService.keyVersionListObservable.subscribe((field) => {
        this.eskmKeyFieldsLoaded = false;
        this.cloudInstanceService
          .getGridListValues(this.selectedCloudInstance.id, this.selectedKey)
          .subscribe((res) => {
            this.gridListResponseHandler(res, field);
          });
      });

    if (this.selectedAction.header && this.selectedAction.header !== '') {
      let tempTitle = this.selectedAction.header;
      tempTitle += `<img src="assets/icons/${this.selectedCloudInstance.cloudType}.svg" class="rowIcon btnPadding" alt="type"/>`;
      tempTitle += ' ' + this.selectedCloudInstance.cloudType;
      this.modalReference.title = tempTitle;
    }
  }

  @HostListener('unloaded')
  OnDestroy() {
    //reset component on close
    this.selectedKey = {
      name: '',
      keyName: '',
      keyOwner: '',
      algorithm: '',
      keyLength: '',
      cloudKeyName: '',
      keyFields: {},
      eskmKeyAttributes: {},
      keySource: '',
      keyId: '',
    };
    this.selectedCloudInstance = {
      cloudID: -1,
      cloudType: '',
      id: -1,
      eskmKeyUser: '',
      instanceName: '',
      keyFields: [],
      eskmKeyFields: [],
      configFields: null,
    };
    this.dropdownOptionList = {};
    this.confirmation = { header: '', msg: '', visible: false };
    this.onCancelModal();
    this.keyVersionListObservable.unsubscribe();
  }

  //handle search association response
  searchAssociationResponseHandler(res: any) {
    if (res.status == 200 && res.body.data) {
      if (res.body.data.length) {
        this.fetchedKey.keyAlgoSize = res.body.data[0].keyAlgoSize;
        this.fetchedKey.eskmKeyOwner = res.body.data[0].eskmKeyOwner;
      }
      this.setHsmKey(this.fetchedKey); //set key object
      if (
        this.selectedAction.action == 'Edit' ||
        this.selectedAction.keySource == 'External'
      )
        this.getUploadedCloudKeyDetails();
    } else if (res.error && res.error.errorMessage)
      this._toast.error('Error', res.error.errorMessage);
    else if (res.status == 503)
      this._toast.error(
        'Error',
        'Service unavailable, Please try again after some time.'
      );
    else this._toast.error('Error', 'Internal Server error');
  }

  //default set keyFields
  resetKeyFields() {
    if (this.selectedCloudType.keyFields) {
      this.selectedCloudType.keyFields.forEach(
        (data: { type: string; jsonField: string | number; required: any }) => {
          //create fields and set default values for dynamic fields
          if (data.type == 'yes-no') {
            this.validateSelectedKey[data.jsonField] = { value: true, msg: '' };
            this.selectedKey.keyFields[data.jsonField] = false;
          } else if (
            data.type == 'value-array' ||
            data.type == 'multi-select'
          ) {
            this.selectedKey.keyFields[data.jsonField] = '';
            this.modifiedValueArray[data.jsonField] = [];
            this.validateSelectedKey[data.jsonField] = {
              value: !data.required,
              msg: '',
            };
          } else if (data.type == 'name-value_editable-array') {
            this.selectedKey.keyFields[data.jsonField] = [];
            this.modifiedValueArray[data.jsonField] = [];
            this.validateSelectedKey[data.jsonField] = {
              value: !data.required,
              msg: '',
            };
          } else if (data.type == 'select') {
            this.selectedKey.keyFields[data.jsonField] = '';
            this.validateSelectedKey[data.jsonField] = {
              value: !data.required,
              msg: '',
            };
          } else {
            this.validateSelectedKey[data.jsonField] = {
              value: !data.required,
              msg: '',
            };
            this.selectedKey.keyFields[data.jsonField] = null;
          }
        }
      );
    }
  }

  //default set eskmKeyFields
  resetEskmKeyFields() {
    this.selectedCloudType.eskmKeyFields.forEach(
      (data: { type: string; jsonField: string | number }) => {
        //create fields and set default values for dynamic fields
        if (data.type == 'yes-no') {
          this.selectedKey.eskmKeyAttributes[data.jsonField] = false;
        } else {
          this.selectedKey.eskmKeyAttributes[data.jsonField] = null;
        }
      }
    );
  }

  gridListResponseHandler(res: any, field: any) {
    if (res.status == 200 && res.body) {
      this.selectedKey.eskmKeyAttributes[field] = res.body.Versions;
      this.eskmKeyFieldsLoaded = true;
    } else if (res.error && res.error.errorMessage) {
      this._toast.error('Error', res.error.errorMessage);
      this.eskmKeyFieldsLoaded = true;
    } else if (res.status == 503) {
      this._toast.error(
        'Error',
        'Service unavailable, Please try again after some time.'
      );
      this.eskmKeyFieldsLoaded = true;
    } else {
      this._toast.error('Error', 'Internal Server error');
      this.eskmKeyFieldsLoaded = true;
    }
  }

  //emit event on cancel
  onCancelModal() {
    this.cancel.emit();
  }

  //create key object and fire req on edit submit
  onEditKey() {
    const action: any = {};
    if (!this.validateUploadKey()) return;
    this.editUploadBtn = false;
    action['action'] = 'update';
    action['keyId'] = this.selectedKey.keyId;
    //set edit key object
    if (this.selectedCloudType.keyFields) {
      const selectedKey = JSON.parse(JSON.stringify(this.selectedKey));
      this.setKeyFields(selectedKey);
      action['keyAttributes'] = JSON.stringify(selectedKey.keyFields);
      action['oldKeyAttributes'] = JSON.stringify(this.oldKeyAttributes);
    }
    if (this.selectedCloudType.eskmKeyFields) {
      const eskmKeyAttributes = {},
        attr = JSON.parse(JSON.stringify(this.selectedKey.eskmKeyAttributes));
      this.setEskmKeyFields(eskmKeyAttributes, attr);
      action['eskmKeyOwner'] =
        this.selectedKey.eskmKeyAttributes['eskmKeyOwner'];
      action['eskmKeyAttributes'] = JSON.stringify(eskmKeyAttributes);
    }

    //execute action api request
    this.cloudInstanceService
      .performKeyAction(this.selectedCloudInstance.id, this.selectedKey, action)
      .subscribe((data) => {
        this.editUploadBtn = true;
        this.keyActionResponseHandler(data);
      });
  }

  //set keyfields
  setKeyFields(selectedKey: { keyFields: { [x: string]: any } }) {
    this.selectedCloudType.keyFields.forEach((field: any) => {
      if (field.type == 'name-value_editable-array') {
        const modifiedValue = this.modifiedValueArray[field.jsonField];
        if (this.modifiedValueArray[field.jsonField].length)
          selectedKey.keyFields[field.jsonField] = selectedKey.keyFields[
            field.jsonField
          ].length
            ? selectedKey.keyFields[field.jsonField].concat(modifiedValue)
            : modifiedValue;
      } else if (field.type == 'value-array' || field.type == 'multi-select') {
        const modifiedValue =
          this.modifiedValueArray[field.jsonField].join(',');
        if (this.modifiedValueArray[field.jsonField].length)
          selectedKey.keyFields[field.jsonField] = selectedKey.keyFields[
            field.jsonField
          ].length
            ? selectedKey.keyFields[field.jsonField] + ',' + modifiedValue
            : modifiedValue;
      }
    });
  }

  //set eskmKeyFields
  setEskmKeyFields(eskmKeyAttributes: any, attr: any) {
    this.selectedCloudType.eskmKeyFields.forEach((field: any) => {
      if (field.jsonField != 'eskmKeyOwner' && field.type != 'grid-view') {
        eskmKeyAttributes[field.label] = attr[field.jsonField];
      }
    });
  }

  //handle keyAction response
  keyActionResponseHandler(data: any) {
    if (data.status == 200 && data.body) {
      this.cloudInstanceService.manageKeysChanged();
      this._toast.success('Success', data.body.message);
      this.confirmationClose();
    } else if (data.error && data.error.message) {
      this._toast.error('Error', data.error.errorMessage);
    } else if (data.status == 503) {
      this._toast.error(
        'Error',
        'Service unavailable, Please try again after some time.'
      );
    } else {
      this._toast.error('Error', 'Internal Server error');
    }
  }

  //create key object and fire req on upload submit
  onUploadKey() {
    if (!this.validateUploadKey()) return;

    this.reUploadBtn = false;
    const selectedKey = JSON.parse(JSON.stringify(this.selectedKey));
    this.setKeyFields(selectedKey);

    // if (this.selectedCloudType.name.toLowerCase().includes("salesforce-byok")) {
    //   const keyType = this.selectedKey.keyFields['sfKeyType'];
    //   this.sfByokuploadKey(keyType, selectedKey);
    // } else {
    //upload key api call
    this.uploadKeyApiCall(selectedKey);
    // }
  }

  private uploadKeyApiCall(selectedKey: any) {
    this.cloudInstanceService
      .uploadKey(selectedKey, this.selectedCloudInstance)
      .subscribe((res) => {
        this.reUploadBtn = true;
        this.updateKeyResponseHandler(res);
      });
  }

  // sfByokuploadKey(keytype, selectedKey) {
  //   let sfKeyList = JSON.parse(localStorage.getItem("sfKeyList"));
  //   let valKeyType = this.dropdownOptionList["sfKeyType"].values.find(val => val.id == keytype)
  //   let activeKeyExist = sfKeyList.find(k => k.keyAttributes['type'] == valKeyType.value && k.keyAttributes['keyState'] == "ACTIVE");
  //   if (activeKeyExist) {
  //     this.sfUploadConfirmation.msgHead = "An active key already exists. Uploading a new key will archive the existing key.";
  //     this.sfUploadConfirmation.msgBody = "An archived key can’t encrypt new data, but it can be used to decrypt data previously encrypted  when it was active.";
  //     this.sfUploadConfirmation.msgTail = "You can have up to 50 active and archived tenant secrets of each type.";
  //     this.sfUploadConfirmation.selectedKey = selectedKey;
  //     this.sfUploadConfirmationToggle.nativeElement.click();
  //   } else {
  //     this.uploadKeyApiCall(selectedKey);
  //   }

  // }

  // onSfUpload(selectedKey) {
  //   this.uploadKeyApiCall(selectedKey);
  // }

  //updateKey response handler
  updateKeyResponseHandler(res: any) {
    if ((res.status == 200 || res.status == 201) && res.body) {
      //success
      this.cloudInstanceService.manageKeysChanged();
      //this.confirmation.header = "Success!";
      this.confirmation.header =
        $localize`:ReUpload Key-Confirmation Header@@confirmation_success_msg:Success!` ||
        'Success!';
      this.confirmation.msg = res.body.message;
      this._toast.success(this.confirmation.msg, "");
    } else if (res.error && res.error.message) {
      this._toast.error('Error', res.error.errorMessage);
    } else if (res.status == 503) {
      this._toast.error(
        'Error',
        'Service unavailable, Please try again after some time.'
      );
    } else {
      //failure
      this._toast.error('Error', 'Internal Server error');
    }
  }

  //set values on adding new element for new-version
  onGridItemAdd() {
    const action = {
      action: 'new-version',
      keyId: this.selectedKey.keyId,
      eskmKeyOwner: this.selectedKey.eskmKeyAttributes['eskmKeyOwner'],
      keySource: this.selectedKey.keySource,
      status: 'Active',
    };
    const versionKeyConfirmation = {
      //version confirmation popup data object
      header: $localize`:ReUpload Key-Header for confirmation box@@version_confirmation:Alert!`,
      msg:
        $localize`:ReUpload Key-Confirmation box content@@confirmation_msg:Are you sure you want to create a new version for ` +
        this.selectedKey.keyName +
        '?',
      showDialog: true,
      selectedKey: this.selectedKey,
      action: action,
      field: 'keyVersions',
    };
    this.createNewVersion.emit(versionKeyConfirmation);
  }

  //set dynamic field on update
  onSharedFieldUpdate(event: any, field: any) {
    if (
      (field.type == 'value-array' || field.type == 'multi-select') &&
      Array.isArray(event.event.target.value) &&
      event.event.target.value.length == 2
    ) {
      this.setValueArrayField(event, field);
    } else if (
      field.type == 'name-value_editable-array' &&
      Array.isArray(event.event.target.value) &&
      event.event.target.value.length == 2
    ) {
      this.setNameValueArrayField(event, field);
    } else {
      this.selectedKey.keyFields[field.jsonField] = event.event.target.value;
    }
  }

  //set value of value-array and multi-select type inputs
  setValueArrayField(event: any, field: any) {
    const confValues1: any = [],
      confValues2: any = [];
    event.event.target.value[0].forEach((elm: any) => {
      if (elm) confValues1.push(elm.trim());
    });
    event.event.target.value[1].forEach((elm: any) => {
      if (elm) confValues2.push(elm.trim());
    });
    //add validation and styling for serviceAccountName
    if (
      field.jsonField == 'serviceAccountName' &&
      !confValues1.length &&
      !confValues2.length
    ) {
      this.validateSelectedKey[field.jsonField].value = false;
      this.validateSelectedKey[field.jsonField].msg = [
        'Warning: Instance will be deleted if no service account are present',
      ];
    } else {
      this.validateSelectedKey[field.jsonField].value = true;
      this.validateSelectedKey[field.jsonField].msg = [];
    }
    this.selectedKey.keyFields[field.jsonField] = confValues1.join(',');
    this.modifiedValueArray[field.jsonField] = confValues2;
  }

  //set value of name-value_editable-array type input
  setNameValueArrayField(event: any, field: any) {
    this.validateSelectedKey[field.jsonField].value = true;
    this.validateSelectedKey[field.jsonField].msg = [];
    this.selectedKey.keyFields[field.jsonField] = event.event.target.value[0];
    this.modifiedValueArray[field.jsonField] = event.event.target.value[1];
  }

  //set values on updating eskmKeyAttributes field
  onEskmKeyAttributesUpdate(event: any, field: any) {
    if (field.type == 'yes-no') {
      this.selectedKey.eskmKeyAttributes[field.jsonField] =
        event.event.target.value == true ? 'yes' : 'no';
    } else
      this.selectedKey.eskmKeyAttributes[field.jsonField] =
        event.event.target.value;
  }

  //merge fields together
  mergeNewConfigFields(value: any, newValueArr: any) {
    const newValue = newValueArr.join(',');
    if (!value) return newValue;
    if (!newValue) return value;
    if (value && newValue) return value + ',' + newValue;
  }

  //check list for duplicates
  hasDuplicates(list: any) {
    return new Set(list).size !== list.length;
  }

  //fetch options for dropdown lists
  getDropdownData() {
    if (
      this.selectedCloudType.name.toLowerCase().includes('salesforce') ||
      this.selectedCloudType.name.toLowerCase().includes('gcp-ekm')
    ) {
      if (this.selectedCloudType.keyFields)
        this.selectedCloudType.keyFields.forEach((data: any) => {
          //set dropdowns in keyFields
          this.setKeyFieldsDropdown(data);
        });
      if (this.selectedCloudType.eskmKeyFields)
        this.selectedCloudType.eskmKeyFields.forEach((data: any) => {
          //set dropdowns in eskmKeyFields
          this.setConfigFieldsDropdown(data);
        });
    } else {
      if (this.selectedCloudInstance.keyFields)
        this.selectedCloudInstance.keyFields.forEach((data: any) => {
          //set dropdowns in keyFields
          this.setKeyFieldsDropdown(data);
        });
      if (this.selectedCloudInstance.eskmKeyFields)
        this.selectedCloudInstance.eskmKeyFields.forEach((data: any) => {
          //set dropdowns in eskmKeyFields
          this.setConfigFieldsDropdown(data);
        });
    }
  }

  //set dropdown lists in keyFields
  setKeyFieldsDropdown(data: any) {
    // if (data.jsonField == 'salesForceCAs' && data.type == 'select') {
    //   let caList = JSON.parse(localStorage.getItem("caList"))
    //   this.sfCasListReady = caList ? true : false;
    //   this.dropdownOptionList[data.jsonField] = {
    //     "label": data.label,
    //     "jsonField": data.jsonField,
    //     "type": "select",
    //     "values": caList ? caList.map(d => { return { id: d.developerName + '-' + d.id, value: d.developerName } }) : {}
    //   }
    // } else if (data.jsonField == 'sfKeyType' && data.type == 'select') {
    //   this.dropdownOptionList[data.jsonField] = {
    //     "label": data.label,
    //     "jsonField": data.jsonField,
    //     "type": "select",
    //     "values": data.values.map(d => { return { id: d.id, value: d.value } })
    //   }
    // } else {
    this.selectedCloudType.keyFields.forEach((d: any) => {
      if (d.jsonField == data.jsonField) {
        this.dropdownOptionList[data.jsonField] = { ...d, values: data.values };
      }
    });
    //}
  }

  //set dropdown lists in configFields
  setConfigFieldsDropdown(data: any) {
    if (data.jsonField == 'eskmKeyOwner' && data.type == 'select') {
      this.selectedCloudInstance.configFields = JSON.parse(
        this.selectedCloudInstance.configFields
      );

      this.dropdownOptionList[data.jsonField] = {
        label: 'ESKM Key Owner',
        jsonField: 'eskmKeyOwner',
        type: 'select',
        values: this.selectedCloudInstance.configFields['serviceAccountName']
          .split(',')
          .map((d: any) => {
            return { id: d, value: d };
          }),
      };
    }
    // if (data.jsonField == 'salesForceCAs' && data.type == 'select') {
    //   let caList = JSON.parse(localStorage.getItem("caList"))
    //   this.sfCasListReady = caList ? true : false;
    //   this.dropdownOptionList[data.jsonField] = {
    //     "label": data.label,
    //     "jsonField": data.jsonField,
    //     "type": "select",
    //     "values": caList ? caList.map(d => { return { id: !this.selectedCloudType.name.toLowerCase().includes("market-byok") ? d.developerName + '-' + d.id : d.id, value: d.developerName } }) : {}
    //   }
    // }
  }

  //create and set hsmKey object
  setHsmKey(key: any) {
    const [algorithm, size] =
      key.keyAlgoSize.split('-').length > 1
        ? key.keyAlgoSize.split('-')
        : ['', ''];
    const keyName = key.eskmKeyName;
    this.selectedKey.keyName = keyName;
    this.selectedKey.keyOwner = key.eskmKeyOwner;
    this.selectedKey.algorithm = algorithm;
    this.selectedKey.keyLength = size;
    this.selectedKey.name = key.name ? key.name : '';
    this.selectedKey.keySource = key.keySource ? key.keySource : '';
    this.selectedKey.keyId = key.keyId ? key.keyId : '';
    this.selectedKey.cloudKeyName =
      this.selectedAction.action == 'Edit' || key.keySource == 'External'
        ? key.name
        : this.generateCloudKeyName(key, keyName);
    if (this.selectedAction.action == 'Edit') {
      this.setEditedDynamicFields(key);
    } else this.eskmKeyFieldsLoaded = true;
  }

  //set edited dynamic fields
  setEditedDynamicFields(key: any) {
    if (key.keyAttributes) {
      this.selectedKey.keyFields = JSON.parse(
        JSON.stringify(key.keyAttributes)
      );
      this.oldKeyAttributes = JSON.parse(JSON.stringify(key.keyAttributes));
    }
    if (key.eskmKeyAttributes) {
      this.selectedKey.eskmKeyAttributes = JSON.parse(
        JSON.stringify(key.eskmKeyAttributes)
      );
      this.fetchCloudKeyDetails(key);
    }
  }

  fetchCloudKeyDetails(key: any) {
    //console.log('re-upload-this.selectedCloudType',this.selectedCloudType);
    if (
      this.selectedCloudType.fetchCloudKeyDetails ||
      this.selectedCloudType.fetchESKMKeyDetails
    ) {
      //fetch custom key attributes
      this.cloudInstanceService
        .getCustomKeyAttributes(
          this.selectedCloudInstance.id,
          key,
          this.selectedCloudType.fetchCloudKeyDetails,
          this.selectedCloudType.fetchESKMKeyDetails
        )
        .subscribe((res) => {
          if ((res.status == 200 || res.status == 201) && res.body) {
            //success
            this.customKeyAttributesResponseHandler(res);
          } else if (res.error && res.error.errorMessage) {
            //failure
            this._toast.error('Error', res.error.errorMessage);
          } else if (res.status == 503) {
            this._toast.error(
              'Error',
              'Service unavailable, Please try again after some time.'
            );
          } else {
            this._toast.error('Error', 'Internal Server error');
          }
        });
    }
  }

  //CustomKeyAttributes response handler
  customKeyAttributesResponseHandler(res: any) {
    const eskmKeyAttributes: any = {};
    let keyAttributes = {},
      attr = {};
    if (res.body.eskmKeyAttributes) {
      attr = JSON.parse(res.body.eskmKeyAttributes);
      this.eskmKeyAttributes = [];
      this.eskmKeyAttributeSetter(eskmKeyAttributes, attr);
      eskmKeyAttributes['eskmKeyOwner'] = res.body.eskmKeyOwner;
      this.selectedKey.eskmKeyAttributes = eskmKeyAttributes;
      this.eskmKeyFieldsLoaded = true;
    }
    if (res.body.keyAttributes) {
      try {
        keyAttributes = JSON.parse(res.body.keyAttributes);
        this.oldKeyAttributes = JSON.parse(res.body.keyAttributes);
        this.selectedKey.keyFields = keyAttributes;
        this.eskmKeyFieldsLoaded = true;
      } catch (e) {
        console.log(e);
      }
    }
  }

  //set eskmKeyAttributes on get customKeyAttributes
  eskmKeyAttributeSetter(eskmKeyAttributes: any, attr: any) {
    const newEskmKeyAttributes: any = [];
    let keyAttrObj: any = {};
    this.selectedCloudType.eskmKeyFields.forEach((field: any) => {
      if (field.commonLabel) {
        //handle field having common label
        const tempField = JSON.parse(JSON.stringify(field));
        newEskmKeyAttributes.push(tempField);
        eskmKeyAttributes[field.jsonField] = attr[field.label];
      }
      if (field.type == 'grid-view') {
        //handle type and fetch gridList values
        this.cloudInstanceService
          .getGridListValues(this.selectedCloudInstance.id, this.selectedKey)
          .subscribe((res) => {
            this.eskmKeyFieldsLoaded = false;
            this.getGridListResponseHandler(res, field);
          });
      } else {
        eskmKeyAttributes[field.jsonField] = attr[field.label];
      }
    });
    keyAttrObj = this.groupBy(newEskmKeyAttributes, (val) => val.commonLabel);
    this.eskmKeyAttributes = Object.keys(keyAttrObj).map((key) => {
      return keyAttrObj[key];
    });
  }

  //getGridList response handler
  getGridListResponseHandler(
    res: {
      status: number;
      body: { data: any; message: string };
      error: { message: string };
    },
    field: { type: string; jsonField: string | number }
  ) {
    if (res.status == 200 && res.body) {
      this.selectedKey.eskmKeyAttributes[field.jsonField] =
        res.body.data.Versions;
      setTimeout(() => {
        this.eskmKeyFieldsLoaded = true;
      }, 100);
    } else if (res.error && res.error.message) {
      this._toast.error('Error', res.error.message);
    } else if (res.status == 503) {
      this._toast.error(
        'Error',
        'Service unavailable, Please try again after some time.'
      );
    } else {
      this._toast.error('Error', 'Internal Server error');
    }
  }

  //new cloud key name if key is deleted from cloud
  generateCloudKeyName(key: any, keyName: string) {
    const modifiedKeyName =
      keyName.split(/_(.*)/s).length > 1 ? keyName.split(/_(.*)/s)[1] : keyName;
    if (
      key.status &&
      (key.status.includes('Error') || key.status.includes('Deleted'))
    ) {
      return this.renameReuploadKey(modifiedKeyName, this.keyList);
    }
    return modifiedKeyName;
  }

  //generate new cloudKeyName for key
  renameReuploadKey(keyName: string, list: any, n = 1): string {
    const newKeyname = keyName + '-' + n;
    if (!(list.filter((data: any) => data.name == newKeyname).length < 1))
      return this.renameReuploadKey(keyName, list, n + 1);
    else return newKeyname;
  }

  //reset on closing confirmation
  confirmationClose() {
    this.onCancelModal();
  }

  //validate a givent key object
  validateUploadKey() {
    this.validateFields(
      this.selectedKey['cloudKeyName'],
      'cloudKeyName',
      { required: true, maxLength: 56 },
      'text-field',
      'Cloud Key Name'
    );
    if (this.selectedCloudType.keyFields) {
      this.selectedCloudType.keyFields.forEach(
        (data: {
          jsonField: string;
          required: any;
          maxLength: any;
          type: any;
          label: string | undefined;
        }) => {
          this.validateFields(
            this.selectedKey.keyFields[data.jsonField],
            data.jsonField,
            { required: data.required, maxLength: data.maxLength },
            data.type,
            data.label
          );
        }
      );
    }

    return this.existingUploadKeySubmitEnabled;
  }

  // validate single key object field
  validateFields(
    value: any,
    field: string,
    validator: { required: any; maxLength: any },
    type: string,
    fieldLabel = ''
  ) {
    let result = true;
    console.log(fieldLabel);
    const maxLength = validator.maxLength > 0 ? validator.maxLength : 50;
    if (
      (type == 'value-array' || type == 'multi-select') &&
      validator.required
    ) {
      this.validateMultiSelectConfigFields(field, maxLength);
    } else if (type == 'name-value_editable-array') {
      this.validateNameValueConfigFields(field, maxLength, validator);
    } else if (validator.required && !value && type !== 'yes-no') {
      this.validateSelectedKey[field].value = false;
      this.validateSelectedKey[field].msg = ['Field required'];
    } else if (value && value.length > maxLength) {
      this.validateSelectedKey[field].value = false;
      this.validateSelectedKey[field].msg = [
        'Value should be less than ' + maxLength + ' characters',
      ];
    } else if (type == 'date-time-picker') {
      const val = this.selectedKey.keyFields[field];
      if (Array.isArray(val) && val.length == 2) {
        if (!this.isDateTimeLocalString(val[0])) {
          this.validateSelectedKey[field].value = false;
          this.validateSelectedKey[field].msg = ['Invalid datetime value'];
        } else {
          this.selectedKey.keyFields[field] = val.join('');
          this.validateSelectedKey[field].value = true;
          this.validateSelectedKey[field].msg = [];
        }
      }
    } else {
      this.validateSelectedKey[field].value = true;
      this.validateSelectedKey[field].msg = [];
    }
    //check valitator object for errors
    Object.keys(this.validateSelectedKey).forEach((key) => {
      if (this.validateSelectedKey[key].value == false) {
        result = false;
      }
    });
    this.existingUploadKeySubmitEnabled = result;
  }

  isDateTimeLocalString(str: string): boolean {
    const date = new Date(str);
    return (
      !isNaN(date.getTime()) &&
      str.length === 19 && // YYYY-MM-DDTHH:mm:ss
      str.charAt(10) === 'T'
    );
  }
  //validate and merge new config fields for value-array and multi-select
  validateMultiSelectConfigFields(field: any, maxLength: any) {
    const configFields = this.selectedKey.keyFields[field];
    this.modifiedValueArray[field] = this.modifiedValueArray[field]
      ? this.modifiedValueArray[field].filter((data: any) => data != '')
      : [];
    const newConfigFields = this.mergeNewConfigFields(
      configFields,
      this.modifiedValueArray[field]
    );
    if (!newConfigFields || newConfigFields == '') {
      this.validateSelectedKey[field].value = false;
      this.validateSelectedKey[field].msg = ['Field required'];
    } else if (this.hasDuplicates(this.modifiedValueArray[field])) {
      this.validateSelectedKey[field].value = false;
      this.validateSelectedKey[field].msg = ['Field values must be unique'];
    } else if (
      this.modifiedValueArray[field].filter(
        (d: string | any[]) => d.length > maxLength
      ).length
    ) {
      this.validateSelectedKey[field].value = false;
      this.validateSelectedKey.msg =
        'Value should be less than ' + maxLength + ' characters';
    } else {
      this.validateSelectedKey[field].value = true;
      this.validateSelectedKey[field].msg = [];
    }
  }

  //validate and merge new config fields for name-value_editable-array
  validateNameValueConfigFields(
    field: string | number,
    maxLength: number,
    validator: { required: any }
  ) {
    const configFields = this.selectedKey.keyFields[field];
    this.modifiedValueArray[field] = this.modifiedValueArray[field]
      ? this.modifiedValueArray[field].filter(
          (data: any) => data.name != '' || data.value != ''
        )
      : [];
    //set config fields
    const newConfigFields = configFields
      ? configFields.concat(this.modifiedValueArray[field])
      : this.modifiedValueArray[field];
    if (validator.required && (!newConfigFields || newConfigFields.length)) {
      this.validateSelectedKey[field].value = false;
      this.validateSelectedKey[field].msg = ['Field required'];
    } else if (this.checkTagsListDuplicates(newConfigFields)) {
      this.validateSelectedKey[field].value = false;
      this.validateSelectedKey[field].msg = ['Field values must be unique'];
    } else if (
      this.modifiedValueArray[field].filter((d: any) => d.length > maxLength)
        .length
    ) {
      this.validateSelectedKey[field].value = false;
      this.validateSelectedKey.msg =
        'Value should be less than ' + maxLength + ' characters';
    } else {
      this.validateSelectedKey[field].value = true;
      this.validateSelectedKey[field].msg = [];
    }
  }

  //validate TagsList for Duplicates
  checkTagsListDuplicates(list: any[]) {
    const idsAlreadySeen: any = [],
      valuesAlreadySeen: any = [];
    list.forEach((data: { name: any; value: any }) => {
      const name = data.name,
        value = data.value;
      if (idsAlreadySeen.indexOf(name) == -1) {
        idsAlreadySeen.push(name);
      }
      if (valuesAlreadySeen.indexOf(value) == -1) {
        valuesAlreadySeen.push(value);
      }
    });
    if (
      list.length == idsAlreadySeen.length &&
      list.length == valuesAlreadySeen.length
    )
      return false;
    else return true;
  }

  //set keyFields
  getUploadedCloudKeyDetails() {
    this.keyList.forEach((data: any) => {
      if (
        data.name &&
        data.name == this.selectedKey.name &&
        data.keySource &&
        this.selectedKey.keySource
      ) {
        this.setFetchedDynamicFields(data);
      }
    });
  }

  //set dynamic fields to fetched values
  setFetchedDynamicFields(data: any) {
    if (data.keyAttributes) {
      this.selectedKey.keyFields = JSON.parse(
        JSON.stringify(data.keyAttributes)
      );
      this.oldKeyAttributes = JSON.parse(JSON.stringify(data.keyAttributes));
    }
    if (data.eskmKeyAttributes)
      this.selectedKey.eskmKeyAttributes = JSON.parse(
        JSON.stringify(data.eskmKeyAttributes)
      );
  }

  //group elements based on common element
  groupBy(arr: any[], fn: (item: any) => any) {
    return arr.reduce((prev, curr) => {
      const groupKey = fn(curr);
      const group = prev[groupKey] || [];
      group.push(curr);
      return { ...prev, [groupKey]: group };
    }, {});
  }
}
