<div class="reupload-key-container">
  <div class="reupload-key-form">
    <!-- body block -->
    @if (selectedKey.keyFields || selectedKey.eskmKeyAttributes) {
    <div class="modal-body form-group">
      <lib-taas-modal-loading-spinner uniqueId="eskmFieldLoadingSpinner" width="300px" height="300px" 
        [spinnerVisible]="!eskmKeyFieldsLoaded && (selectedCloudType.fetchCloudKeyDetails || selectedCloudType.fetchESKMKeyDetails)">
      </lib-taas-modal-loading-spinner>
      @if (eskmKeyFieldsLoaded || (!selectedCloudType.fetchCloudKeyDetails &&
      !selectedCloudType.fetchESKMKeyDetails)) {
      <div>
        <!--ESKM Key-->
        @if (selectedKey.keyName && !this.selectedCloudType.eskmKeyFields) {
        <div class="widthFull">
          <div>
            <label for="reuploadEskmKey" i18n="Reupload Key: label for Key Name"
              >Key Name</label
            >
          </div>
          <div>
            <input
              type="text"
              class="largeInput"
              id="reuploadEskmKey"
              [disabled]="true"
              [(ngModel)]="selectedKey.keyName"
              autocomplete="off"
            />
          </div>
        </div>
        }
        <!-- keyName -->
        <div class="widthFull">
          <div>
            @if (selectedKey.cloudKeyName !== '-'){ @if
            (!selectedCloudType.eskmKeyFields) {
            <label for="keyName" i18n="Reupload Key: label for Cloud Key Name"
              >*Cloud Key Name</label
            >
            } @else {
            <label for="keyName" i18n="Reupload Key: label for EKMaas Key Name"
              >*EKMaaS Key Name</label
            >
            } }
          </div>
          <div>
            <input
              type="text"
              class="largeInput"
              id="reuploadKeyName"
              [disabled]="selectedAction.action === 'Edit'"
              [(ngModel)]="selectedKey.cloudKeyName"
              autocomplete="off"
            />
          </div>
          @if (validateSelectedKey['cloudKeyName'].msg.length > 0) {
          <div class="displayFlex">
            <mat-error>{{ validateSelectedKey.cloudKeyName.msg }}</mat-error>
          </div>
          }
        </div>
        <!-- dynamic keyFields -->
        @for (field of selectedCloudType.keyFields; track field) {
        <div class="widthFull">
          @if(field.isImportField){ @if (field.type!=='multi-select' &&
          ((field.editable || selectedKey.keyFields[field.jsonField]) ||
          selectedAction.action!=='Edit')) {
          <div>
            <label
              ><span *ngIf="field.required">*</span
              >{{ getLocalizedKeyFieldsLabel(field) }}</label
            >
          </div>
          } @if (field.type==='multi-select'){ @if (selectedAction.action
          ==='Edit') {
          <div>
            <app-taas-shared-input
              [inputField]="dropdownOptionList[field.jsonField]"
              [value]="selectedKey.keyFields[field.jsonField]"
              id="{{ 'addCloudInsConf' + field.jsonField }}"
              [inputLabel]="field.label"
              [maxLength]="field.maxSelect"
              [disableAdd]="!field.editable"
              [disableRemove]="!field.editable"
              (update)="onSharedFieldUpdate($event, field)"
            >
            </app-taas-shared-input>
          </div>
          } @else {
          <div>
            <app-taas-shared-input
              [inputField]="dropdownOptionList[field.jsonField]"
              [value]="selectedKey.keyFields[field.jsonField]"
              id="{{ 'addCloudInsConf' + field.jsonField }}"
              [inputLabel]="field.label"
              [maxLength]="field.maxSelect"
              (update)="onSharedFieldUpdate($event, field)"
            >
            </app-taas-shared-input>
          </div>
          } } @if (field.type==='select' && ((field.editable ||
          selectedKey.keyFields[field.jsonField]) ||
          selectedAction.action!=='Edit')) {
          <div
            [ngClass]="{
              disabled: !field.editable && selectedAction.action === 'Edit'
            }"
          >
            <app-taas-shared-input
              [inputField]="dropdownOptionList[field.jsonField]"
              [value]="selectedKey.keyFields[field.jsonField]"
              (update)="onSharedFieldUpdate($event, field)"
              id="{{ 'reuploadKeyFields' + field.jsonField }}"
              [disabled]="!field.editable && selectedAction.action === 'Edit'"
            >
            </app-taas-shared-input>
          </div>
          } @if (field.type!=='yes-no' && field.type!=='select' &&
          field.type!=='multi-select' && ((field.editable ||
          selectedKey.keyFields[field.jsonField]) ||
          selectedAction.action!=='Edit')) {
          <div
            [ngClass]="{
              disabled: !field.editable && selectedAction.action === 'Edit'
            }"
          >
            <app-taas-shared-input
              [inputField]="field"
              [value]="selectedKey.keyFields[field.jsonField]"
              (update)="onSharedFieldUpdate($event, field)"
              id="{{ 'reuploadKeyFields' + field.jsonField }}"
              [disabled]="!field.editable && selectedAction.action === 'Edit'"
            >
            </app-taas-shared-input>
          </div>
          } @if (field.type==='yes-no' && (field.editable ||
          selectedKey.keyFields[field.jsonField])) {
          <div
            class="customMargin textInitial"
            [ngClass]="{
              disabled: !field.editable && selectedAction.action === 'Edit'
            }"
          >
            <app-taas-shared-input
              [inputField]="field"
              [value]="selectedKey.keyFields[field.jsonField]"
              (update)="onSharedFieldUpdate($event, field)"
              id="{{ 'reuploadKeyFields' + field.jsonField }}"
              [disabled]="!field.editable && selectedAction.action === 'Edit'"
            >
            </app-taas-shared-input>
          </div>
          } @if (!validateSelectedKey[field.jsonField].value) {
          <div class="displayFlex">
            <mat-error>{{
              validateSelectedKey[field.jsonField].msg
            }}</mat-error>
          </div>
          } }
        </div>
        }
        <!-- dynamic eskmKeyFields -->
        <div class="widthFull">
          <!-- generate fields without commonLabel -->
          @for (field of selectedCloudType.eskmKeyFields; track field) {
          <div class="width100">
            @if (field.type!=='yes-no' && field.type!=='grid-view' &&
            !field.commonLabel) {
            <div>
              <label for="'field.jsonField"
                ><span *ngIf="field.required">*</span
                >{{ field.label | appToText }}</label
              >
            </div>
            } @if (field.type!=='yes-no' && field.type!=='text-field' &&
            field.type!=='grid-view' && field.type !== 'select' &&
            !field.commonLabel) {
            <div>
              <input
                class="largeInput"
                [disabled]="true"
                id="{{ 'reuploadEskmKeyFields' + field.jsonField }}"
                [ngModel]="selectedKey.eskmKeyAttributes[field.jsonField]"
              />
            </div>
            } @if (field.type==='text-field' && !field.commonLabel) {
            <div>
              <app-taas-shared-input
                [inputField]="field"
                [value]="selectedKey.eskmKeyAttributes[field.jsonField]"
                (update)="onEskmKeyAttributesUpdate($event, field)"
                id="{{ 'reuploadEskmKeyFields' + field.jsonField }}"
              >
              </app-taas-shared-input>
            </div>
            } @if (field.type==='grid-view' && !field.commonLabel) {
            <div>
              <app-taas-shared-input
                [inputField]="field"
                [value]="selectedKey.eskmKeyAttributes[field.jsonField]"
                id="{{ 'reuploadEskmKeyFields' + field.jsonField }}"
                [inputLabel]="field.label"
                [disabled]="false"
                (update)="onGridItemAdd()"
              >
              </app-taas-shared-input>
            </div>
            } @if (field.type==='select' && !field.commonLabel) {
            <div>
              <app-taas-shared-input
                [disabled]="!field.editable"
                [inputField]="dropdownOptionList[field.jsonField]"
                [value]="selectedKey.eskmKeyAttributes[field.jsonField]"
                (update)="onEskmKeyAttributesUpdate($event, field)"
                id="{{ 'reuploadEskmKeyFields' + field.jsonField }}"
              >
              </app-taas-shared-input>
            </div>
            } @if (field.type==='yes-no' && !field.commonLabel) {
            <div>
              <app-taas-shared-input
                [inputField]="field"
                [value]="
                  selectedKey.eskmKeyAttributes[field.jsonField] === 'yes'
                    ? true
                    : false
                "
                (update)="onEskmKeyAttributesUpdate($event, field)"
                id="{{ 'reuploadEskmKeyFields' + field.jsonField }}"
              >
              </app-taas-shared-input>
              <label class="sublabel" for="field.jsonField">{{
                field.label | appToText
              }}</label>
            </div>
            }
          </div>
          }

          <!-- generate fields with commonLabel -->
          @for (fieldList of eskmKeyAttributes; track fieldList) {
          <div class="width100">
            @if (fieldList[0] && fieldList[0].commonLabel) {
            <div>
              <label>{{ fieldList[0].commonLabel }}</label>
            </div>
            } @for (field of fieldList; track field) {
            <div class="width100">
              @if (field.type!=='yes-no') {
              <div>
                <input
                  class="largeInput"
                  [disabled]="true"
                  id="{{ 'reuploadEskmKeyFields' + field.jsonField }}"
                  [ngModel]="selectedKey.eskmKeyAttributes[field.jsonField]"
                />
              </div>
              } @else {
              <div>
                <app-taas-shared-input
                  [inputField]="field"
                  [value]="
                    selectedKey.eskmKeyAttributes[field.jsonField] === 'yes'
                      ? true
                      : false
                  "
                  (update)="onEskmKeyAttributesUpdate($event, field)"
                  id="{{ 'reuploadEskmKeyFields' + field.jsonField }}"
                >
                </app-taas-shared-input>
                <label class="sublabel" for="'field.jsonField'">{{
                  field.label | appToText
                }}</label>
              </div>
              }
            </div>
            }
          </div>
          }
        </div>
      </div>
      }
    </div>
    }
  </div>
  <!-- buttons -->
  <!--TODO invisibe buttons?-->
  <div class="reupload-key-footer">
    @if (selectedAction.action === 'Upload') {
    <lib-taas-button
      btnId="reuploadModalUpload"
      [disabled]="!reUploadBtn || !sfCasListReady"
      buttonText="Upload"
      (click)="onUploadKey()"
      buttonType="highlight"
    ></lib-taas-button>
    } @else if (selectedAction.action === 'Edit') {
    <lib-taas-button
      btnId="reuploadModalSave"
      [disabled]="!editUploadBtn || !sfCasListReady"
      buttonText="Save"
      variant="confirm"
      (click)="onEditKey()"
      buttonType="highlight"
    ></lib-taas-button>
    }

    <lib-taas-button
      #confirmationCancel
      btnId="reuploadModalCancel"
      (click)="onCancelModal()"
      buttonText="Cancel"
    >
    </lib-taas-button>
  </div>
  <!-- Salesforce-Byok upload confirmation modal -->
  <!-- <div id="sfUploadConfirmationModal" class="modal fade" role="dialog" #sfUploadConfirmationModal
                  data-backdrop="">
                  <div class="modal-dialog modal-md">
                      <div class="modal-content">

                          <div class="modal-header d-block">
                              {{sfUploadConfirmation.header}}
                              <mat-icon class="pr-4 pl-2 float-right rowIconBig iconFont"
                                  id="sfUploadConfirmationModalCancel" data-dismiss="modal" (click)="confirmationClose()">
                                  close
                              </mat-icon>
                          </div>

                          <div class="modal-body form-group">
                              <div>
                                  <span class="text-body">
                                      <h4><strong>{{sfUploadConfirmation.msgHead}}</strong></h4>
                                  </span>
                                  <span class="text-body">
                                      <h5>{{sfUploadConfirmation.msgBody}}</h5>
                                  </span>

                                  <div class="pt-3">
                                      <span class="text-warning fontSmall">&#9830;{{sfUploadConfirmation.msgTail}} </span>
                                  </div>
                              </div>
                          </div>

                          <div class="modal-footer">
                              <button type="button" class="btn cloudBtn" id="sfUploadConfirmationYes"
                                  (click)="onSfUpload(sfUploadConfirmation.selectedKey)">Upload</button>
                              <button type="button" class="btn cloudBtn" id="sfUploadConfirmationModalCancel"
                                  data-dismiss="modal" (click)="confirmationClose()">Cancel</button>
                          </div>
                      </div>
                  </div>
              </div> -->
</div>
