::ng-deep{
  .rowIcon{
    width:15px;
    height: 15px;
  }
}
.rowIconBig{
    width:20px;
    height: 20px;
    float: inline-end;
    border: none;
}

.iconFont{
    font-size:20px;
    cursor: pointer;
    color: white!important;
}
// .rowIconBig{
//     width:20px;
//     height: 20px;
// }
.helpFont{
    font-size:22px;
    cursor: pointer;
}
.cloudBtn{
    color: #fff;
    background-color: #0068b4;
    border-color: #0068b4;
}
.widthFull{
    width:95%;
    margin-top:0.5rem
}
.displayFlex{
    display: flex;
}
.largeInput,.largeInput:focus-visible{
    width:100%;
    height: 30px;
    border-top: none;
    border-left: none;
    border-right: none;
    border-bottom: 1px solid #648787;;
    color: #212529;
    font-size: inherit!important;
    cursor: text;
}
.labelInput{
    min-width: 225px;
    height: 24px;
    border: none;
    color: #212529;
    margin-left: 10px;
    margin-bottom: 10px;
    font-size: inherit!important;
}
label{
    color: #666;
    font-size: 13px;
    margin-bottom: 0px;
}
.sublabel{
    font-size: 12px;
}
button{
    margin:10px;
    margin-right: 0px;
}
.fa-check-circle{
    color: limegreen;
    font-size: 25px;
}
.disabled {
    pointer-events: none;
    cursor: default;
}
.width100{
    width: 100%;
    display: contents;
    word-break: break-word;
}
@media (min-width: 992px){
    .width100 .customMargin {
        padding-left: 3rem!important;
    }
}
@media (max-width: 992px){
    .width100 .customMargin {
        padding-left: 2rem!important;
    }
}
.textInitial {
    text-align: initial;
}
// .custom-modal-overlay {
//     position:fixed;
//     top: 0;
//     left: 0;
//     width: 100%;
//     height: 100%;
//     background-color: rgba(0, 0, 0, 0.5);
//     display: block;
//   }
  .aligned-with-icon{
    position: absolute;
    margin-top: 5px;
    margin-left: 5px; /* optional */
}
.confirmationBoxMesage {
    font-weight: 400;
    font-size: 13px;
    color: #212529;
    overflow-wrap: anywhere;
}

.mat-icon {
    border: none;
    padding: none;
    height: 24px;
    width: 24px;
    color: #0068b4;
    font-size: 20px;
}

  .modal-content {
    background-color: white;
    border-radius: 8px;
    //padding: 20px;
    // max-width: 500px;
    // width: 100%;
    max-height: 80vh;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    overflow: hidden;
  }

  .modal-header {
    padding: 10px;
    border-bottom: 1px solid #ddd;
  }

  .modal-body-wrapper {
    overflow-y: auto;
    max-height: 60vh;
    padding-right: 10px;
  }


  .modal-body-wrapper::-webkit-scrollbar {
    width: 8px;
  }

  .modal-body-wrapper::-webkit-scrollbar-thumb {
    border-radius: 4px;
  }

  .modal-body-wrapper::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    border-radius: 4px;
  }

  .reupload-key-container {
    display: flex;
    flex-direction: column;
    height: 100%;

    .reupload-key-form {
      overflow-y: auto;
      overflow-x: hidden;
      max-height: 450px;
      scrollbar-width: thin;
    }

    .reupload-key-footer {
      display: flex;
      flex-direction: row-reverse;
      gap: 10px;
      margin-top: 20px;
    }
  }
