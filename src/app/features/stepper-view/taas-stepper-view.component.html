<div [ngClass]="{ uploadDisabled: !selectedCloudType.uploadKeyEnabled }">
  @if (eventList.length > 0) {
  <mat-horizontal-stepper
    #keyStepper
    [linear]="true"
    (selectionChange)="onStepperChange($event)"
  >
    <mat-step
      label="{{ eventList[0].label ? eventList[0].label : '' }}"
      completed="{{ eventList[0].completed }}"
    >
      <p>{{ eventList[0].text ? eventList[0].text : "" }}</p>
      <div>
        <!-- radio btn options for new and existing keys -->
        <div class="alignCenter">
          @if (selectedCloudType.uploadKeyEnabled) {
          <mat-radio-group name="uploadKeyType" fxLayout="column">
            <mat-radio-button
              color="accent"
              id="stepperUploadNewKeyOption"
              [value]="uploadKeyTypeOptions[0].name"
              (change)="uploadKeyTypChange(uploadKeyTypeOptions[0])"
              checked="true"
              name="uploadKeyType"
              [disabled]="eventList[0].disableChange"
            >
              {{ uploadKeyTypeOptions[0].name }}
            </mat-radio-button>
            <mat-radio-button
              color="accent"
              id="stepperUploadExistingKeyOption"
              [value]="uploadKeyTypeOptions[1].name"
              (change)="uploadKeyTypChange(uploadKeyTypeOptions[1])"
              checked="false"
              name="uploadKeyType"
              [disabled]="eventList[0].disableChange"
            >
              {{ uploadKeyTypeOptions[1].name }}
            </mat-radio-button>
          </mat-radio-group>
          }
        </div>

        <!-- Upload Existing Key popup -->
        @if (uploadKeyTypeOptions[1].checked) {
        <div>
          <div>
            <div class="widthFull">
              <div>
                <label i18n="Stepper-View: label for Select key"
                  >Select Key</label
                >
              </div>
              <div class="displayFlex">
                <!-- search field -->
                <input
                  type="text"
                  placeholder="Search"
                  id="stepper-search-text"
                  aria-describedby="search-text"
                  (keyup)="existingKeySearchCheck($event)"
                  aria-label="Search"
                  [(ngModel)]="keySearchText"
                  autocomplete="off"
                />
                <mat-icon>search</mat-icon>
              </div>
              <!-- warnings and error display -->
              @if (uploadKeyTypeOptions[1].checked && keyFormValidityMsg.status
              && keyFormValidityMsg.class==='text-warning') {
              <div class="textAlignCenter warningText">
                <span [class]="keyFormValidityMsg.class"
                  >&#9830; {{ keyFormValidityMsg.msg }}</span
                >
              </div>
              } @if (uploadKeyTypeOptions[1].checked && searchWarning.status) {
              <div class="textAlignCenter warningText">
                <span class="text-warning"
                  >&#9830; {{ searchWarning.msg }}</span
                >
              </div>
              } @if (keySearchText.length>1 && !existingKeySearchSubmitEnabled)
              {
              <ul class="list-group list-group-flush">
                @for (key of keyList | appSearchKey: keySearchText; track
                key.eskmKeyName) {
                <li
                  class="list-group-item"
                  id="{{ 'stepperUploadNewKey' + key.eskmKeyName }}"
                  appHighlight
                  tabindex="0"
                  (click)="fetchExistingKey(key, eventList[0])"
                  (keydown.space)="fetchExistingKey(key, eventList[0])"
                  (keydown.enter)="fetchExistingKey(key, eventList[0])"
                >
                  {{ key.eskmKeyName }}
                </li>
                }
              </ul>
              }
            </div>
          </div>
          <!-- submit and cancel for Upload Existing Key popup -->
          <div class="footer">
            <lib-taas-button
              btnId="stepperExistingNext"
              buttonType="highlight"
              (click)="fetchExistingKey(null, eventList[0])"
              [disabled]="!existingKeySearchSubmitEnabled"
              >{{ getButtonText("next") }}
            </lib-taas-button>

            <lib-taas-button
              btnId="stepperExistingCancel"
              buttonType="primary"
              (click)="onCancelModal()"
              >{{ getButtonText("cancel") }}
            </lib-taas-button>
          </div>
        </div>
        }

        <!-- Create New Key and Upload popup -->
        <div *ngIf="uploadKeyTypeOptions[0].checked">
          <div>
            <!-- keyOwner -->
            @if (!(selectedCloudType.eskmKeyFields &&
            selectedCloudType.eskmKeyFields.length)) {
            <div class="widthFull">
              <div>
                <label for="keyOwner" i18n="Stepper-View: label for key owner"
                  >Key Owner</label
                >
              </div>
              <div>
                <input
                  type="text"
                  class="largeInput"
                  id="stepperKeyOwner"
                  [(ngModel)]="selectedKey.keyOwner"
                  autocomplete="off"
                  [disabled]="true"
                />
              </div>
            </div>
            }
            <!-- keyName -->
            <div class="widthFull">
              <div>
                <label for="keyName" i18n="Stepper-View: label for key name"
                  >*Key Name</label
                >
              </div>
              <div>
                <input
                  type="text"
                  class="largeInput"
                  id="stepperEskmKey"
                  [(ngModel)]="selectedKey.keyName"
                  autocomplete="off"
                />
              </div>
              @if (validateSelectedKey.keyName.msg.length > 0) {
              <div class="displayFlex">
                <mat-error>
                  {{ validateSelectedKey.keyName.msg }}
                  <!-- <span style="color:red">{{validateSelectedKey.keyName.msg}}</span> -->
                </mat-error>
              </div>
              }
            </div>
            <!-- algorithm -->
            <div class="widthFull">
              <div>
                <label for="algorithm" i18n="Stepper-View: label for algorithm"
                  >*Algorithm</label
                >
              </div>
              <div>
                <select
                  class="largeInput"
                  id="stepperAlgorithm"
                  (change)="onAlgorithmChange($event)"
                >
                  <option value="" selected>Select</option>
                  @for (algo of algorithmList; track algo.id) {
                  <option [value]="algo.id">
                    {{ algo.keyAlgo + "-" + algo.keySize }}
                  </option>
                  }
                </select>
              </div>
              @if (validateSelectedKey.algorithm.msg.length > 0) {
              <div class="displayFlex">
                <mat-error>
                  {{ validateSelectedKey.algorithm.msg }}
                  <!-- <span style="color:red">{{validateSelectedKey.algorithm.msg}}</span> -->
                </mat-error>
              </div>
              }
            </div>

            <!-- dynamic fields -->
            <div class="widthFull">
              <!-- dynamic eskmKeyFields -->
              @for (field of selectedCloudType.eskmKeyFields; track
              field.jsonField) {
              <div class="width100">
                @if (field.type!=='yes-no' && field.type!=='grid-view' &&
                !field.commonLabel) {
                <div>
                  <label for="'field.jsonField">
                    @if (field.required) {
                    <span>*</span>
                    }
                    {{ field.label | appToText }}
                  </label>
                </div>
                } @if (field.type==='select' && !field.commonLabel) {
                <div>
                  <app-taas-shared-input
                    [disabled]="!field.editable"
                    [inputField]="dropdownOptionList[field.jsonField]"
                    [value]="selectedKey.eskmKeyFields[field.jsonField]"
                    (update)="onEskmKeyAttributesUpdate($event, field)"
                    id="{{ 'stepper' + field.jsonField }}"
                  >
                  </app-taas-shared-input>
                </div>
                } @if (field.type!=='select' && field.type!=='yes-no' &&
                field.type!=='grid-view' && !field.commonLabel) {
                <div>
                  <app-taas-shared-input
                    [inputField]="field"
                    [disabled]="!field.editable"
                    [value]="selectedKey.eskmKeyFields[field.jsonField]"
                    (update)="onEskmKeyAttributesUpdate($event, field)"
                    id="{{ 'stepper' + field.jsonField }}"
                  >
                  </app-taas-shared-input>
                  <!-- <label class="ml-2" *ngIf="field.type=='yes-no'" for="'field.jsonField">{{field.label | appToText}}</label> -->
                </div>
                } @if (field.type==='yes-no' && !field.commonLabel) {
                <div>
                  <app-taas-shared-input
                    [inputField]="field"
                    [disabled]="!field.editable"
                    [value]="
                      selectedKey.eskmKeyFields[field.jsonField] === 'yes'
                        ? true
                        : false
                    "
                    (update)="onEskmKeyAttributesUpdate($event, field)"
                    id="{{ 'stepper' + field.jsonField }}"
                  >
                  </app-taas-shared-input>
                  @if (field.type==='yes-no') {
                  <label class="sublabel" for="field.jsonField">{{
                    field.label | appToText
                  }}</label>
                  }
                </div>
                } @if (field.type!=='yes-no' && field.type !== 'grid-view') {
                <div class="displayFlex">
                  @if (validateSelectedKey.eskmKeyFields[field.jsonField] &&
                  validateSelectedKey.eskmKeyFields[field.jsonField].msg.length>0)
                  {
                  <mat-error>
                    {{ validateSelectedKey.eskmKeyFields[field.jsonField].msg }}
                  </mat-error>
                  }
                </div>
                }
              </div>
              }
              <!-- dynamic eskmKeyAttributes -->
              @for (fieldList of eskmKeyAttributes; track $index) {
              <div class="width100">
                @if (fieldList[0] && fieldList[0].commonLabel) {
                <div>
                  <label>{{ fieldList[0].commonLabel }}</label>
                </div>
                } @for (field of fieldList; track field.jsonField) {
                <div class="width100">
                  @if (field.type !== 'yes-no' && field.type!=='grid-view') {
                  <div>
                    <input
                      class="largeInput"
                      [disabled]="true"
                      [ngModel]="selectedKey.eskmKeyFields[field.jsonField]"
                    />
                  </div>
                  } @if (field.type === 'yes-no') {
                  <div>
                    <app-taas-shared-input
                      [inputField]="field"
                      [value]="
                        selectedKey.eskmKeyFields[field.jsonField] === 'yes'
                          ? true
                          : false
                      "
                      (update)="onEskmKeyAttributesUpdate($event, field)"
                      id="{{ 'createEskmKeyFields' + field.jsonField }}"
                    >
                    </app-taas-shared-input>
                    @if (field.type === 'yes-no') {
                    <label class="sublabel" for="field.jsonField">{{
                      field.label | appToText
                    }}</label>
                    }
                  </div>
                  }
                </div>
                }
              </div>
              }
            </div>
          </div>
          <!-- validation errors -->
          @if (uploadKeyTypeOptions[0].checked && keyFormValidityMsg.status &&
          keyFormValidityMsg.class==='text-warning') {
          <div class="textAlignCenter warningText">
            <span [class]="keyFormValidityMsg.class"
              >&#9830; {{ keyFormValidityMsg.msg }}</span
            >
          </div>
          }
          <!-- submit and cancel btn for Create New Key and Upload popup -->
          <div class="footer">
            <lib-taas-button
              btnId="stepperCreateKey"
              buttonType="highlight"
              (click)="addNewKey(eventList[0])"
              [disabled]="!createBtn || !sfCasListReady"
              >{{ getButtonText("create") }}
            </lib-taas-button>

            <lib-taas-button
              btnId="stepperCreateCancel"
              buttonType="primary"
              (click)="onCancelModal()"
              >{{ getButtonText("cancel") }}
            </lib-taas-button>
          </div>
        </div>
      </div>
    </mat-step>

    <!-- Summary step -->
    <mat-step
      label="{{ eventList[1].label ? eventList[1].label : '' }}"
      completed="{{ eventList[1].completed }}"
    >
      <p>{{ eventList[1].text ? eventList[1].text : "" }}</p>
      <div>
        <!-- Summary popup -->
        <div>
          @if (keyFormValidityMsg.status &&
          keyFormValidityMsg.class==='text-success') {
          <div class="textAlignCenter warningText">
            <span [class]="keyFormValidityMsg.class"
              >&#9830; {{ keyFormValidityMsg.msg }}
            </span>
          </div>
          }
          <div class="textAlignCenter">
            <!-- keyName -->
            <div class="widthFull">
              <div>
                <label for="keyName" i18n="Stepper-View: label for Key Name"
                  >Key Name</label
                >
              </div>
              <div>
                <label class="summaryField" for="keyName">{{
                  selectedKey.keyName
                }}</label>
              </div>
            </div>
            <!-- keyOwner -->
            @if (!(selectedCloudType.eskmKeyFields &&
            selectedCloudType.eskmKeyFields.length)) {
            <div class="widthFull">
              <div>
                <label i18n="Stepper-View: label for key owner"
                  >Key Owner</label
                >
              </div>
              <div>
                <label class="summaryField">{{ selectedKey.keyOwner }}</label>
              </div>
            </div>
            }
            <!-- algorithm -->
            <div class="widthFull">
              <div>
                <label i18n="Stepper-View: label for Algorithm"
                  >Algorithm</label
                >
              </div>
              <div>
                <label class="summaryField"
                  >{{ selectedKey.algorithm }}-{{
                    selectedKey.keyLength
                  }}</label
                >
              </div>
            </div>

            <!-- dynamic eskmKeyFields -->
            <div class="widthFull">
              <!-- dynamic eskmKeyFields -->
              @for (field of selectedCloudType.eskmKeyFields; track
              field.jsonField) {
              <div class="width100">
                @if (field.type!=='yes-no' && field.type!=='grid-view' &&
                !field.commonLabel &&
                selectedKey.eskmKeyFields[field.jsonField]) {
                <div>
                  <label for="'field.jsonField">{{
                    field.label | appToText
                  }}</label>
                </div>
                } @if (field.type!=='yes-no' && field.type!=='grid-view' &&
                !field.commonLabel &&
                selectedKey.eskmKeyFields[field.jsonField]) {
                <div>
                  <label class="summaryField" for="'field.jsonField">{{
                    selectedKey.eskmKeyFields[field.jsonField]
                  }}</label>
                </div>
                } @if (field.type==='yes-no' && !field.commonLabel) {
                <div class="customMargin textInitial">
                  <app-taas-shared-input
                    [inputField]="field"
                    [value]="
                      selectedKey.eskmKeyFields[field.jsonField] === 'yes'
                        ? true
                        : false
                    "
                    (update)="onEskmKeyAttributesUpdate($event, field)"
                    id="{{ 'stepperKey' + field.jsonField }}"
                    [disabled]="true"
                  >
                  </app-taas-shared-input>
                  <label class="sublabel" for="'field.jsonField">{{
                    field.label | appToText
                  }}</label>
                </div>
                }
              </div>
              }
              <!-- dynamic eskmKeyAttributes -->
              @for (fieldList of eskmKeyAttributes; track $index) {
              <div class="width100">
                @if (fieldList[0] && fieldList[0].commonLabel) {
                <div>
                  <label>{{ fieldList[0].commonLabel }}</label>
                </div>
                } @for (field of fieldList; track field.jsonField) {
                <div class="width100">
                  @if (field.type !== 'yes-no' && field.type !== 'grid-view') {
                  <div>
                    <input
                      class="largeInput"
                      [disabled]="true"
                      [ngModel]="selectedKey.eskmKeyFields[field.jsonField]"
                    />
                  </div>
                  } @if (field.type === 'yes-no') {
                  <div>
                    <app-taas-shared-input
                      [inputField]="field"
                      [value]="
                        selectedKey.eskmKeyFields[field.jsonField] === 'yes'
                          ? true
                          : false
                      "
                      (update)="onEskmKeyAttributesUpdate($event, field)"
                      [disabled]="true"
                      id="{{ 'createEskmKeyFields' + field.jsonField }}"
                    >
                    </app-taas-shared-input>
                    @if (field.type === 'yes-no') {
                    <label class="sublabel" for="'field.jsonField">{{
                      field.label | appToText
                    }}</label>
                    }
                  </div>
                  }
                </div>
                }
              </div>
              }
            </div>
          </div>
          <!-- submit and cancel btns for summary -->
          <div class="footer">
            @if (selectedCloudType.uploadKeyEnabled) {
            <lib-taas-button
              btnId="stepperKeyNextSum"
              buttonType="highlight"
              (click)="onSummaryNext()"
              >{{ getButtonText("next") }}
            </lib-taas-button>
            }
            <lib-taas-button
              btnId="stepperKeyCloseSum"
              buttonType="primary"
              (click)="onCancelModal()"
              >{{ getButtonText("cancel") }}
            </lib-taas-button>
          </div>
        </div>
      </div>
    </mat-step>

    <!-- Final upload key step popup -->
    @if (selectedCloudType.uploadKeyEnabled) {
    <mat-step
      [label]="eventList[2].label || ''"
      [completed]="eventList[2].completed"
    >
      <p>{{ eventList[2].text ? eventList[2].text : "" }}</p>
      <div>
        <!-- Upload Key popup step 2 -->
        <div>
          <div>
            <!--ESKM Key-->
            <div class="widthFull">
              <div>
                <label
                  for="eskmKey"
                  i18n="Stepper-View-FinalUpload: label for EKMaaS Key Name"
                  >EKMaaS Key Name</label
                >
              </div>
              <div>
                <input
                  type="text"
                  class="largeInput"
                  id="stepperEskmKeyUpload"
                  [disabled]="true"
                  [(ngModel)]="selectedKey.keyName"
                  autocomplete="off"
                />
              </div>
            </div>
            <!-- keyName -->
            <div class="widthFull">
              <div>
                @if (selectedCloudType.eskmKeyFields) {
                <label
                  for="keyName"
                  i18n="Stepper-View-FinalUpload: label for EKMaaS Key Name"
                  >*EKMaaS Key Name</label
                >
                } @else {
                <label
                  for="keyName"
                  i18n="Stepper-View-FinalUpload: label for Cloud Key Name"
                  >*Cloud Key Name</label
                >
                }
              </div>
              <div>
                <input
                  type="text"
                  class="largeInput"
                  id="stepperCloudKeyNameUpload"
                  [(ngModel)]="selectedKey.cloudKeyName"
                  autocomplete="off"
                />
              </div>
              @if (validateSelectedKey['cloudKeyName'].msg.length > 0) {
              <div class="displayFlex">
                <mat-error>{{
                  validateSelectedKey.cloudKeyName.msg
                }}</mat-error>
              </div>
              }
            </div>

            <!-- dynamic keyFields -->
            @for (field of selectedCloudType.keyFields; track field.jsonField) {
            <div class="widthFull">
              @if (field.isImportField) { @if (field.type !== 'multi-select') {
              <div>
                <label for="'field.jsonField'">
                  @if (field.required) {
                  <span>*</span>
                  }
                  {{ getLocalizedKeyFieldsLabel(field) }}
                </label>
              </div>
              } @switch (field.type) { @case ('multi-select') {
              <div>
                <app-taas-shared-input
                  [inputField]="dropdownOptionList[field.jsonField]"
                  [value]="selectedKey.keyFields[field.jsonField]"
                  id="{{ 'addCloudInsConf' + field.jsonField }}"
                  [inputLabel]="field.label"
                  [maxLength]="field.maxSelect"
                  (update)="onSharedFieldUpdate($event, field)"
                >
                </app-taas-shared-input>
              </div>
              } @case ('select') {
              <div>
                <app-taas-shared-input
                  [inputField]="dropdownOptionList[field.jsonField]"
                  [value]="selectedKey.keyFields[field.jsonField]"
                  (update)="onSharedFieldUpdate($event, field)"
                  id="{{ 'stepperUpload' + field.jsonField }}"
                >
                </app-taas-shared-input>
              </div>
              } @case ('yes-no') {
              <div class="customMargin textInitial">
                <app-taas-shared-input
                  [inputField]="field"
                  [value]="selectedKey.keyFields[field.jsonField]"
                  (update)="onSharedFieldUpdate($event, field)"
                  id="{{ 'stepperUpload' + field.jsonField }}"
                >
                </app-taas-shared-input>
              </div>
              } @default {
              <div>
                <app-taas-shared-input
                  [inputField]="field"
                  [value]="selectedKey.keyFields[field.jsonField]"
                  (update)="onSharedFieldUpdate($event, field)"
                  id="{{ 'stepperUpload' + field.jsonField }}"
                >
                </app-taas-shared-input>
              </div>
              } } @if (!validateSelectedKey[field.jsonField].value) {
              <div class="displayFlex">
                <mat-error>{{
                  validateSelectedKey[field.jsonField].msg
                }}</mat-error>
              </div>
              } }
            </div>
            }
          </div>
          <div class="footer">
            <!-- submit and cancel btns for Upload Key popup -->
            <lib-taas-button
              id="stepperFinalUpload"
              (click)="uploadKey()"
              [disabled]="!uploadBtn"
              [buttonText]="getButtonText('upload')"
              buttonType="highlight"
            ></lib-taas-button>

            <lib-taas-button
              id="tepperFinalUCalcel"
              (click)="onCancelModal()"
              [buttonText]="getButtonText('cancel')"
              buttonType="primary"
            ></lib-taas-button>
          </div>
        </div>
      </div>

      <!-- TODO what is this used for-->
      <!-- Salesforce-Byok upload confirmation modal
      <div
        id="sfUploadConfirmationModal"
        role="dialog"
        #sfUploadConfirmationModal
        data-backdrop=""
      >
        <div>
          <div>
            <div class="modal-header">
              {{ sfUploadConfirmation.header }}
              <mat-icon
                id="sfUploadConfirmationModalCancel"
                data-dismiss="modal"
                (click)="confirmationClose(false)"
              >
                close
              </mat-icon>
            </div>
            <div>
              <div>
                <span>
                  <h5>
                    <strong>{{ sfUploadConfirmation.msgHead }}</strong>
                  </h5>
                </span>
                <span>
                  <h5>{{ sfUploadConfirmation.msgBody }}</h5>
                </span>
                <div>
                  <span class="text-warning"
                    >&#9830;{{ sfUploadConfirmation.msgTail }}
                  </span>
                </div>
              </div>
            </div>
            <div>
              <button
                type="button"
                class="cloudBtn"
                id="sfUploadConfirmationYes"
                (click)="onSfUpload(sfUploadConfirmation.selectedKey)"
                i18n="Stepper-View-Salesforce: button text for Upload"
              >
                Upload
              </button>
              <button
                type="button"
                class="cloudBtn"
                id="sfUploadConfirmationModalCancel"
                data-dismiss="modal"
                (click)="confirmationClose(false)"
                i18n="Stepper-View-Salesforce: button text for Cancel"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      </div>-->
    </mat-step>
    }
  </mat-horizontal-stepper>
  }
</div>

