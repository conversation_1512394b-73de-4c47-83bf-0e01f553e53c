
.btnContainer {
    margin-top: 1.5rem;
    padding-right: 1.2rem;
}

button {
    margin: 10px;
    margin-right: 0px;
}

.alignCenter {
    text-align: center;
    margin: auto;
}

.largeInput,
.largeInput:focus-visible {
    width: 100%;
    height: 30px;
    border-top: none;
    border-left: none;
    border-right: none;
    border-bottom: 1px solid #648787;
    color: #212529;
    font-size: inherit !important;
    cursor: text;
}

label {
    color: #666;
    font-size: 13px;
    margin-bottom: 0px;
}

.sublabel {
    font-size: 12px;
}

.widthFull {
    width: 95%;
    margin-top: 0.5rem
}

.form-checkbox {
    vertical-align: middle;
    width: 15px;
    height: 15px;
}

.displayFlex {
    display: flex;
}

.mat-icon {
    border: 1px solid #ced4da;
    padding: 2px;
    height: calc(1.8125rem + 2px);
    width: 30px;
    border-radius: 0.2rem;
}

.leftInputPadding {
    padding-left: 0px;
    padding-right: 30px;
}


.list-group {
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    padding-left: 0;
    margin-bottom: 0;
    margin-right: 15px;
    max-height: 160px;
    overflow: auto;
}

.list-group .list-group-item {
    position: relative;
    display: block;
    padding: 0.5rem 0.9rem;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.cloudBtn{
    color: #fff;
    background-color: #0068b4;
    border-color: black;
    float: right;
}

.addTag {
    color: #0068b4;
    border: none;
}

.removeTag {
    color: red;
    border: none;
}

.list-group-item:hover {
    background-color: #007ad9 !important;
    color: #ffffff;
}

.summaryField {
    font-weight: 400;
    font-size: 14px;
    color: #212529;
    overflow-wrap: anywhere;
}

.confirmationBoxMesage {
    font-weight: 400;
    font-size: 13px;
    color: #212529;
    overflow-wrap: anywhere;
}

.textAlignCenter {
    text-align: center;
}

.warningText {
    font-weight: bold;
    font-style: italic;
}

#confirmationBox {
    .modal-header {
        font: 400 15px/24px Roboto, "Helvetica Neue", sans-serif;
        font-weight: bold;
    }

    .form-group {
        margin-bottom: 0px;
    }

    .mat-icon {
        border: none;
        padding: none;
        height: 24px;
        width: 24px;
        color: #0068b4;
        font-size: 20px;
    }
}

.aligned-with-icon{
    position: absolute;
    margin-top: 5px;
    margin-left: 5px; /* optional */
}

#sfUploadConfirmationModal {
    .modal-header {
        font: 400 15px/24px Roboto, "Helvetica Neue", sans-serif;
        font-weight: bold;
    }

    .form-group {
        margin-bottom: 0px;
    }

    .mat-icon {
        border: none;
        padding: none;
        height: 24px;
        width: 24px;
        font-size: 20px;
    }
}

.fa-check-circle {
    color: limegreen;
    font-size: 25px;
}

.checkboxWrapp {
    width: 100%;
}

.width100 {
    width: 100%;
    display: contents;
    word-break: break-word;
}

@media (min-width: 992px) {
    .width100 .customMargin {
        padding-left: 3rem !important;
    }
}

@media (max-width: 992px) {
    .width100 .customMargin {
        padding-left: 2rem !important;
    }
}

.textInitial {
    text-align: initial;
}


.footer {
  display: flex;
  flex-direction: row-reverse;
  gap: 5px;
  margin-top: 30px;
}
