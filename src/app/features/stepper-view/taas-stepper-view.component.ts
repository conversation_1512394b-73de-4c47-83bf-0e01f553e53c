import {
  Component,
  OnInit,
  Input,
  Output,
  ViewChild,
  EventEmitter,
} from '@angular/core';
import { TaasCloudInstanceService } from '../../core/services/cloud/taas-cloud-instance.service';
import { MatStepper, MatStepperModule } from '@angular/material/stepper';
import { MatRadioModule } from '@angular/material/radio';
import { TaasSharedInputComponent } from '../../shared/components/taas-shared-input/taas-shared-input.component';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { ToText } from '../../core/pipes/toText';
import { SearchKeyPipe } from '../../core/pipes/searchKey.pipe';
import { TaasButtonComponent, ToastService } from 'utimaco-common-ui-angular';
import {
  FIELD_KEY_TRANSLATIONS,
  VALIDATION_MESSAGES,
} from '../../shared/components/taas-shared-localization';
import { KeyFields } from '../../shared/components/models/key-fields';

export const LABELS = {
  KEY_SELECTION_LABEL: $localize`:Stepper-View-Key Selection label for events in stepper view@@key_selection_label:Key Selection`,
  SUMMARY_LABEL: $localize`:Stepper-View-Summary label for events in stepper view@@summary_label:Summary`,
  UPLOADKEY_LABEL: $localize`:Stepper-View-Upload Key label for events in stepper view@@upload_key_label:Upload Key`,
  CREATE_UPLOAD_LABEL: $localize`:Stepper-View-Create key Name for radio button in stepper view@@create_upload_label:Create Key & Upload`,
  EXISTING_KEY_LABEL: $localize`:Stepper-View-Existing Key Name for radio button in stepper view@@select_existing_label:Select Existing Key`,
};

@Component({
  selector: 'app-taas-stepper-view',
  templateUrl: './taas-stepper-view.component.html',
  styleUrls: ['./taas-stepper-view.component.scss'],
  imports: [
    TaasSharedInputComponent,
    MatIcon,
    MatIconModule,
    MatFormFieldModule,
    FormsModule,
    CommonModule,
    MatDialogModule,
    ToText,
    MatStepperModule,
    MatRadioModule,
    SearchKeyPipe,
    TaasButtonComponent,
  ],
  standalone: true,
})
export class TaasStepperViewComponent implements OnInit {
  @ViewChild('keyStepper')
  private keyStepper!: MatStepper; //stepper element
  @Input() cloudInstance: any; //selected cloud instance
  @Input() selectedCloudType: any; //selected cloud type
  @Output() cancel: EventEmitter<any> = new EventEmitter(); //cancel event
  @Output() keyTypeChange: EventEmitter<any> = new EventEmitter(); //keytype change event
  @ViewChild('confirmationToggle') confirmationToggle: any;
  @ViewChild('sfUploadConfirmationModal') sfUploadConfirmationModal: any; //sfUpload modal popup element
  @ViewChild('sfUploadConfirmationToggle') sfUploadConfirmationToggle: any; //sfUpload popup toggle
  keySearchText = ''; //existing key search input
  //submit btn enable disable
  modifiedValueArray: any = {};
  existingNewKeySubmitEnabled = false; //creake key submit btn flag
  existingKeySearchSubmitEnabled = false; //search submit btn flag
  existingUploadKeySubmitEnabled = false; //upload existing key btn flag
  createBtn = true;
  uploadBtn = true;
  sfCasListReady = true;
  //validation msg object
  keyFormValidityMsg = {
    status: false,
    msg: '',
    class: '',
  };
  //confirmationbox modal data object
  confirmation = {
    header: '',
    msg: '',
    visible: false,
  };
  searchWarning = { status: false, msg: '' }; //warning on search

  getLocalizedKeyFieldsLabel(field: KeyFields): string {
    //console.log('reupload-field',field);
    return FIELD_KEY_TRANSLATIONS[field.jsonField] || field.label;
  }

  //field validation status object
  validateSelectedKey: any = {
    //key field errors
    keyName: { value: false, msg: '' },
    algorithm: { value: false, msg: '' },
    cloudKeyName: { value: true, msg: '' },
    eskmKeyFields: {},
  };
  //To fix ERROR TypeError: newCollection[Symbol.iterator] is not a function error
  //eskmKeyAttributes: any ={};
  eskmKeyAttributes: any[][] = [];
  //selected key object
  selectedKey: any = {
    keyName: '',
    keyOwner: '',
    algorithm: '',
    keyLength: 0,
    cloudKeyName: '',
    keyFields: {},
    eskmKeyFields: {},
  };

  sfUploadConfirmation = {
    //sfUpload confirmation popup data object
    header: $localize`:Stepper-View-Header for confirmation box@@header:Alert!`,
    msgHead: null,
    msgBody: null,
    msgTail: null,
    selectedKey: null,
  };
  //list of events in stepper with data
  eventList = [
    {
      id: 1,
      label: LABELS.KEY_SELECTION_LABEL,
      text: '',
      completed: false,
      active: true,
      disableChange: false,
    },
    {
      id: 2,
      label: LABELS.SUMMARY_LABEL,
      completed: false,
      active: false,
      disableChange: false,
    },
    {
      id: 3,
      label: LABELS.UPLOADKEY_LABEL,
      text: '',
      completed: false,
      active: false,
      disableChange: false,
    },
  ];

  //radio btn list on 1st step
  uploadKeyTypeOptions = [
    { name: LABELS.CREATE_UPLOAD_LABEL, id: 1, checked: true },
    { name: LABELS.EXISTING_KEY_LABEL, id: 2, checked: false },
  ];

  //dd lists to be fetched
  algorithmList: any = []; //list of supported algotithms
  dropdownOptionList: any = {}; //list of dropdownOptionList to be searched from
  keyList = []; //list of existing keys to search from
  timezone: any;

  constructor(
    private cloudInstanceService: TaasCloudInstanceService,
    private _toast: ToastService
  ) {}

  ngOnInit(): void {
    this.getDropdownData(); //fetch data for required dropdown
    this.getKeyList(); //fetch list of keys which can be uploaded
    this.selectedKey.keyOwner = this.cloudInstance.eskmKeyUser; //set keyOwner in key
    //console.log('stepper-selectedCloudType', this.selectedCloudType);
    if (this.selectedCloudType.keyFields) {
      //set keyFields
      this.resetKeyFields();
    }
    if (this.selectedCloudType.eskmKeyFields) {
      //set eskmKeyFields
      this.initDefaultEskmKeyFields();
    }
  }

  //default set eskmKeyFields
  initDefaultEskmKeyFields() {
    const newEskmKeyAttributes: any = [];
    let keyAttrObj: any = {};
    this.eskmKeyAttributes = [];
    this.selectedCloudType.eskmKeyFields.forEach(
      (data: {
        jsonField: string | number;
        type: string;
        showOnListing: string;
        commonLabel: string;
        required: boolean;
      }) => {
        //create and set object fields
        if (data.commonLabel) {
          const tempField = JSON.parse(JSON.stringify(data));
          //tempField.selectAll=true;
          newEskmKeyAttributes.push(tempField);
        }
        if (data.type == 'yes-no') {
          this.selectedKey.eskmKeyFields[data.jsonField] = 'no';
          //this.validateSelectedKey[data.jsonField]={value:true,msg:''};
        } else if (
          data.jsonField == 'eskmKeyOwner' &&
          data.type == 'text-field'
        ) {
          this.selectedKey.eskmKeyFields[data.jsonField] =
            this.cloudInstance.eskmKeyUser;
          this.validateSelectedKey.eskmKeyFields[data.jsonField] = {
            value: !data.required,
            msg: '',
          };
        } else if (data.type != 'grid-view' && data.type != 'select') {
          this.selectedKey.eskmKeyFields[data.jsonField] = null;
          this.validateSelectedKey.eskmKeyFields[data.jsonField] = {
            value: !data.required,
            msg: '',
          };
        }
      }
    );
    keyAttrObj = this.groupBy(newEskmKeyAttributes, (val) => val.commonLabel);
    this.eskmKeyAttributes = Object.keys(keyAttrObj).map((key) => {
      return keyAttrObj[key];
    });
  }

  //group elements based on common element
  groupBy(arr: any[], fn: (item: any) => any) {
    return arr.reduce((prev, curr) => {
      const groupKey = fn(curr);
      const group = prev[groupKey] || [];
      group.push(curr);
      return { ...prev, [groupKey]: group };
    }, {});
  }

  //merge fields together
  mergeNewConfigFields(value: string, newValueArr: any[]) {
    const newValue = newValueArr.join(',');
    if (!value) return newValue;
    if (!newValue) return value;
    if (value && newValue) {
      return value + ',' + newValue;
    }
    return '';
  }

  //check list for duplicate values
  hasDuplicates(list: any) {
    return new Set(list).size !== list.length;
  }

  //validate single object field
  //eslint error: removed isAlphanumeric as it is not being used in the method
  //used  for step validation dynamically to be checked
  validateStep(
    event: any,
    field: any,
    maxLength: any,
    isAlphanumeric = false,
    isEskmField = false
  ) {
    event.disableChange = true;
    const isValid = true;
    let newKeyValid = true;
    const msg = '';
    console.log(isAlphanumeric);
    //set values in validation object
    if (!isEskmField) {
      this.validateNonEskmField(isValid, msg, field, maxLength, isEskmField);
    } else if (isEskmField) {
      this.validateEskmField(isValid, msg, field);
    }
    //check fixed fields in validation object and set flag
    if (
      !this.validateSelectedKey.keyName.value ||
      !this.validateSelectedKey.algorithm.value
    ) {
      newKeyValid = false;
    }
    //check dynamic fields in validation object and set flag
    if (
      this.selectedCloudType.eskmKeyFields &&
      this.validateSelectedKey.eskmKeyFields
    )
      Object.keys(this.validateSelectedKey.eskmKeyFields).forEach((key) => {
        if (this.validateSelectedKey.eskmKeyFields[key].value == false) {
          newKeyValid = false;
        }
      });
    //set btn flag to enable/disable
    this.existingNewKeySubmitEnabled = newKeyValid;
  }

  //validate check Non Eskm Fields
  validateNonEskmField(
    isValid: boolean,
    msg: string,
    field: string | number,
    maxLength: string | number,
    isEskmField: boolean
  ) {
    if (!this.selectedKey[field]) {
      //check required
      isValid = false;
      msg = VALIDATION_MESSAGES.FIELD_REQUIRED;
    } else if (
      !isEskmField &&
      maxLength &&
      this.selectedKey[field].length > maxLength
    ) {
      //check length
      isValid = false;
      msg =
        VALIDATION_MESSAGES.VALIDATION_CHECK_MSG +
        maxLength +
        VALIDATION_MESSAGES.CHARACTER;
    }
    //set validation object values
    this.validateSelectedKey[field].value = isValid;
    this.validateSelectedKey[field].msg = msg;
  }

  //validate check Eskm Fields
  validateEskmField(isValid: boolean, msg: string, field: string | number) {
    if (
      this.selectedCloudType.eskmKeyFields &&
      !this.selectedKey.eskmKeyFields[field]
    ) {
      //check required
      isValid = false;
      msg = VALIDATION_MESSAGES.FIELD_REQUIRED;
    }
    //set validation object values
    this.validateSelectedKey.eskmKeyFields[field].value = isValid;
    this.validateSelectedKey.eskmKeyFields[field].msg = msg;
  }

  //validate key object
  validateAddKey() {
    this.validateStep(this.eventList[0], 'keyName', 56, true);
    this.validateStep(this.eventList[0], 'algorithm', 50);
    //loop through eskmKeyFields and validate
    if (this.selectedCloudType.eskmKeyFields)
      this.selectedCloudType.eskmKeyFields.forEach(
        (field: {
          type: string;
          jsonField: any;
          required: any;
          maxLength: any;
        }) => {
          //donot validate yes-no fields as it cannot be null
          if (
            field.type != 'yes-no' &&
            field.type != 'grid-view' &&
            field.type != 'select'
          ) {
            this.validateStep(
              this.eventList[0],
              field.jsonField,
              { required: field.required, maxLength: field.maxLength },
              false,
              true
            );
          }
        }
      );
    return this.existingNewKeySubmitEnabled;
  }

  //send add new key req and set warning msgs
  addNewKey(event: { completed: boolean }) {
    if (!this.validateAddKey()) return; //check vlidation
    this.createBtn = false;
    const newKey = {
      //default new key object
      keyName: this.selectedKey.keyName,
      algorithm: this.selectedKey.algorithm,
      keyLength: this.selectedKey.keyLength.toString(),
      keyOwner: this.selectedKey.keyOwner.trim(),
    };
    //set key object
    if (this.selectedCloudType.eskmKeyFields) {
      this.setAddKeyEskmFields(newKey);
    }
    event.completed = true;
    //extract cloudKeyName
    this.selectedKey.cloudKeyName =
      this.selectedKey.keyName.split(/_(.*)/s).length > 1
        ? this.selectedKey.keyName.split(/_(.*)/s)[1]
        : this.selectedKey.keyName;
    //add new key api req
    this.cloudInstanceService
      .addNewKey(newKey, this.cloudInstance.id)
      .subscribe((res) => {
        this.createBtn = true;
        this.addNewKeyResponseHandler(res);
      });

    /* if (this.selectedCloudType.name.toLowerCase().includes('cache-only')) {
      this.selectedCloudType.eskmKeyFields.forEach((data: { jsonField: string; type: string; }) => {
        if (data.jsonField == "salesForceCAs" && data.type == 'select') {
          var val = this.selectedKey.eskmKeyFields[data.jsonField];
          this.selectedKey.eskmKeyFields[data.jsonField] = val.split("-", 1);
        }
      });
    }
*/
  }

  //set eskm key fields values
  setAddKeyEskmFields(newKey: {
    [x: string]: any;
    keyName?: any;
    algorithm?: any;
    keyLength?: any;
    keyOwner?: any;
  }) {
    newKey['eskmKeyFields'] = {};
    const eskmKeyFields = JSON.parse(
      JSON.stringify(this.selectedKey.eskmKeyFields)
    );
    this.selectedCloudType.eskmKeyFields.forEach(
      (data: { jsonField: string; type: string; label: string | number }) => {
        if (data.jsonField != 'eskmKeyOwner' && data.type != 'grid-view')
          newKey['eskmKeyFields'][data.label] = eskmKeyFields[data.jsonField];
      }
    );
    newKey['eskmKeyFields'] = Object.keys(newKey['eskmKeyFields']).length
      ? JSON.stringify(newKey['eskmKeyFields'])
      : null;
  }

  //handle add new key response and refresh
  addNewKeyResponseHandler(res: {
    status: number;
    error: any;
    body: any;
    message: string;
    code: number;
  }) {
    if (res.status == 200 || res.status == 201) {
      //success
      this.selectedKey.keyName = res.body.data.id;
      this.selectedKey.cloudKeyName =
        res.body.data.id.split(/_(.*)/s).length > 1
          ? res.body.data.id.split(/_(.*)/s)[1]
          : res.body.data.id;
      //set success warning msgs
      this.keyFormValidityMsg.msg =
        $localize`:Stepper-View-Msg for Validation Success@@key_validation:Key created successfully on EKMaaS` ||
        'Key created successfully on EKmaaS';
      this.keyFormValidityMsg.class = 'text-success';
      this.keyFormValidityMsg.status = true;
      this.keyStepper.next();
      this.cloudInstanceService.manageKeysChanged();
    } else {
      //failure
      //set and display error msgs
      this.keyFormValidityMsg.class = 'text-warning';
      this.keyFormValidityMsg.status = false;
      if (res && res.error.message) {
        this._toast.error('Error', res.error.message);
      } else if (res.message) {
        this._toast.error('Error', res.message);
      } else if (res.code == 503) {
        this._toast.error(
          'Error',
          'Service unavailable, Please try again after some time.'
        );
      } else {
        this._toast.error('Error', 'Internal Server error');
      }
    }
  }

  //reset warning after submiting summary set
  onSummaryNext() {
    this.keyFormValidityMsg = {
      status: false,
      msg: '',
      class: 'text-warning',
    };
    this.keyStepper.next();
  }

  //fetch selected key after search
  fetchExistingKey(key: any, event: any) {
    if (key) {
      //if key is selected from results
      this.setHsmKey(key);
      this.checkKeyValidity(this.selectedKey);
      this.keySearchText = key.eskmKeyName;
      //fetch Search Associations
      this.cloudInstanceService
        .getSearchSelectAssociation(this.cloudInstance.id, key.eskmKeyName)
        .subscribe((res: any) => {
          this.getSearchAssociationResponseHandler(res);
        });
    } else {
      //if text is entered and submitted without selecting
      event.completed = true;
      const res = this.keyList.find(
        (d: any) => d.eskmKeyName == this.keySearchText
      );
      if (res) {
        this.setHsmKey(res);
        this.keyStepper.next();
      }
    }
  }

  //handle search association response
  getSearchAssociationResponseHandler(res: {
    status: number;
    body: { data: [] };
    error: { message: string };
  }) {
    if (res.status == 200 && res.body.data) {
      //success
      if (res.body.data.length) {
        res.body.data.forEach((d: any) => {
          //set warning msg
          this.setSearchKeyWarning(d);
        });
      }
    } else if (res.error && res.error.message)
      //known error
      this._toast.error('Error', res.error.message);
    else if (res.status == 503)
      this._toast.error(
        'Error',
        'Service unavailable, Please try again after some time.'
      );
    else this._toast.error('Error', 'Internal Server error');
    this.existingKeySearchSubmitEnabled = true;
  }

  //set Search Key Warning avlue and messages
  setSearchKeyWarning(data: { cloudKeyID: any; cloudKeyName: any }) {
    if (data.cloudKeyID || data.cloudKeyName) {
      this.searchWarning.status = true;
      this.searchWarning.msg = $localize`:Stepper-View-Search Key Warning Msg@@Warning:Warning: This key is already uploaded to the cloud instance`;
    } else {
      this.searchWarning.status = false;
      this.searchWarning.msg = '';
    }
  }

  //set selectedKey object
  setHsmKey(key: any) {
    const [algorithm, size] = key.keyAlgoSize.split('-');
    this.selectedKey.keyName = key.eskmKeyName;
    this.selectedKey.keyOwner = key.eskmKeyOwner;
    this.selectedKey.algorithm = algorithm;
    this.selectedKey.keyLength = size;
    this.selectedKey.cloudKeyName =
      key.eskmKeyName.split(/_(.*)/s).length > 1
        ? key.eskmKeyName.split(/_(.*)/s)[1]
        : key.eskmKeyName;
    this.selectedKey.cloudKeyID = key.cloudKeyID;
    this.selectedKey.eskmKeyID = key.id;
    this.selectedKey.deletable = 'yes';
    this.selectedKey.exportable = 'yes';
  }

  //upload updated key and display confirmation box
  uploadKey() {
    if (!this.validateUploadKey()) return;
    this.uploadBtn = false;
    const selectedKey = JSON.parse(JSON.stringify(this.selectedKey));
    if (this.selectedCloudType.keyFields) {
      this.selectedCloudType.keyFields.forEach((field: any) => {
        this.setUploadKeyField(field, selectedKey);
      });
    }

    if (this.selectedCloudType.name.toLowerCase().includes('salesforce-byok')) {
      // const keyType = this.selectedKey.keyFields['sfKeyType'];
      // this.sfByokuploadKey(keyType, selectedKey);
    } else {
      //upload key api call
      this.uploadKeyApiCall(selectedKey);
    }
  }

  private uploadKeyApiCall(selectedKey: any) {
    this.cloudInstanceService
      .uploadKey(selectedKey, this.cloudInstance)
      .subscribe((res: any) => {
        this.uploadBtn = true;
        this.uploadKeyResponseHandler(res);
      });
  }

  // sfByokuploadKey(keytype: any, selectedKey: null) {
  //   let sfKeyList = JSON.parse(localStorage.getItem("sfKeyList"));
  //   let valKeyType = this.dropdownOptionList["sfKeyType"].values.find((val: { id: any; }) => val.id == keytype)
  //   let activeKeyExist = sfKeyList.find((k: { keyAttributes: { [x: string]: string; }; }) => k.keyAttributes['type'] == valKeyType.value && k.keyAttributes['keyState'] == "ACTIVE");
  //   if (activeKeyExist) {
  //     this.sfUploadConfirmation.msgHead = "An active key already exists. Uploading a new key will archive the existing key.";
  //     this.sfUploadConfirmation.msgBody = "An archived key can’t encrypt new data, but it can be used to decrypt data previously encrypted  when it was active.";
  //     this.sfUploadConfirmation.msgTail = "You can have up to 50 active and archived tenant secrets of each type";
  //     this.sfUploadConfirmation.selectedKey = selectedKey;
  //     this.sfUploadConfirmationToggle.nativeElement.click();
  //   } else {
  //     this.uploadKeyApiCall(selectedKey);
  //   }

  // }

  onSfUpload(selectedKey: any) {
    this.uploadKeyApiCall(selectedKey);
  }

  //set upload key field value
  setUploadKeyField(
    field: { type: string; jsonField: string | number },
    selectedKey: { keyFields: { [x: string]: any } }
  ) {
    if (field.type == 'value-array' || field.type == 'multi-select') {
      //handle value array type
      const modifiedValue = this.modifiedValueArray[field.jsonField].join(',');
      if (this.modifiedValueArray[field.jsonField].length)
        if (selectedKey.keyFields[field.jsonField].length)
          selectedKey.keyFields[field.jsonField] =
            selectedKey.keyFields[field.jsonField] + ',' + modifiedValue;
        else selectedKey.keyFields[field.jsonField] = modifiedValue;
    } else if (field.type == 'name-value_editable-array') {
      const modifiedValue = this.modifiedValueArray[field.jsonField];
      if (this.modifiedValueArray[field.jsonField].length)
        if (selectedKey.keyFields[field.jsonField].length)
          selectedKey.keyFields[field.jsonField] =
            selectedKey.keyFields[field.jsonField].concat(modifiedValue);
        else selectedKey.keyFields[field.jsonField] = modifiedValue;
    }
  }

  //upload key response handler
  uploadKeyResponseHandler(res: {
    status: number;
    body: { data: any; message: string };
    error: { message: string };
  }) {
    if ((res.status == 200 || res.status == 201) && res.body.data) {
      //success
      this.cloudInstanceService.manageKeysChanged();
      //this.confirmation.header = "Success!";
      this.confirmation.header =
        $localize`:Stepper-View-Confirmation Header@@success_header:Success!` ||
        'Success!';
      this.confirmation.msg = res.body.message;
      //this.confirmationToggle.nativeElement.click();
      this._toast.success(this.confirmation.header, this.confirmation.msg);
      this.confirmationClose(false);
      this.cancel.emit();
    } else if (res.error && res.error.message) {
      this._toast.error('Error', res.error.message);
    } else if (res.status == 503) {
      this._toast.error(
        'Error',
        'Service unavailable, Please try again after some time.'
      );
    } else {
      //failure
    }
  }

  //close confirmation box and reset data
  confirmationClose(isError: any) {
    if (!isError) this.resetKeyPopupData();
  }

  //set taskList status on radio btn change
  uploadKeyTypChange(key: { id: number }) {
    this.keyTypeChange.emit(key);
    //set validate key object based on type
    this.resetValidateKeyObject(key);
    //reset key and validation objects
    if (this.selectedCloudType.keyFields) {
      this.resetKeyFields();
    }
    if (this.selectedCloudType.eskmKeyFields) {
      this.resetEskmKeyFields();
    }
    //set checked unchecked option
    this.uploadKeyTypeOptions.map((data) => {
      if (data.id == key.id) data.checked = true;
      else data.checked = false;
    });
  }

  //reset Validate Key Object data
  resetValidateKeyObject(key: { id: number }) {
    if (key.id == 1) {
      this.validateSelectedKey = {
        keyName: { value: false, msg: '' },
        algorithm: { value: false, msg: '' },
        cloudKeyName: { value: true, msg: '' },
      };
    } else if (key.id == 2) {
      this.validateSelectedKey = {
        keyName: { value: true, msg: '' },
        algorithm: { value: true, msg: '' },
        cloudKeyName: { value: true, msg: '' },
      };
    }
  }

  //reset key fields data
  resetKeyFields() {
    if (this.selectedCloudType.keyFields) {
      this.selectedCloudType.keyFields.forEach(
        (data: { type: string; jsonField: string | number; required: any }) => {
          if (data.type == 'yes-no') {
            this.selectedKey.keyFields[data.jsonField] = false;
            this.validateSelectedKey[data.jsonField] = { value: true, msg: '' };
          } else if (
            data.type == 'value-array' ||
            data.type == 'multi-select'
          ) {
            this.selectedKey.keyFields[data.jsonField] = '';
            this.modifiedValueArray[data.jsonField] = [];
            this.validateSelectedKey[data.jsonField] = {
              value: !data.required,
              msg: '',
            };
          } else if (data.type == 'name-value_editable-array') {
            this.selectedKey.keyFields[data.jsonField] = [];
            this.modifiedValueArray[data.jsonField] = [];
            this.validateSelectedKey[data.jsonField] = {
              value: !data.required,
              msg: '',
            };
          } else if (data.type == 'select') {
            this.selectedKey.keyFields[data.jsonField] = '';
            this.validateSelectedKey[data.jsonField] = {
              value: !data.required,
              msg: '',
            };
          } else {
            this.selectedKey.keyFields[data.jsonField] = null;
            this.validateSelectedKey[data.jsonField] = {
              value: !data.required,
              msg: '',
            };
          }
        }
      );
    }
  }

  //reset eskm key fields data
  resetEskmKeyFields() {
    this.selectedCloudType.eskmKeyFields.forEach(
      (data: { type: string; jsonField: string | number }) => {
        //create and set object fields
        if (data.type == 'yes-no') {
          this.selectedKey.eskmKeyFields[data.jsonField] = false;
          //this.validateSelectedKey[data.jsonField]={value:true,msg:''};
        } else if (data.type != 'grid-view' && data.type != 'select') {
          this.selectedKey.eskmKeyFields[data.jsonField] = null;
          //this.validateSelectedKey[data.jsonField]={value:!data.required,msg:''};
        }
      }
    );
  }

  //set status of task list on completion of particular step
  onStepperChange(event: any) {
    if (this.eventList.length > event.selectedIndex) {
      this.eventList.map((data, index) => {
        data.completed = index <= event.selectedIndex ? true : false;
      });
    }
  }

  //set algorithm, cloudSupportID on algorithm dd change
  onAlgorithmChange(event: any) {
    if (event.target.selectedIndex) {
      this.selectedKey.algorithm =
        this.algorithmList[event.target.selectedIndex - 1].keyAlgo;
      this.selectedKey.keyLength =
        this.algorithmList[event.target.selectedIndex - 1].keySize;
    } else {
      this.selectedKey.algorithm = '';
    }
  }

  //validate keys object
  validateUploadKey() {
    this.validateFields(
      this.selectedKey['cloudKeyName'],
      'cloudKeyName',
      { required: true, maxLength: 56 },
      'text-field',
      'Cloud Key Name'
    );
    if (this.selectedCloudType.keyFields) {
      this.selectedCloudType.keyFields.forEach(
        (data: {
          jsonField: string;
          required: any;
          maxLength: any;
          type: any;
          label: string | undefined;
        }) => {
          this.validateFields(
            this.selectedKey.keyFields[data.jsonField],
            data.jsonField,
            { required: data.required, maxLength: data.maxLength },
            data.type,
            data.label
          );
        }
      );
    }

    return this.existingUploadKeySubmitEnabled;
  }

  //check validation object and set warning msgs on searching and selecting existing key
  checkKeyValidity(key: { keyOwner: string }) {
    let valid = true;
    this.keyFormValidityMsg.msg = $localize`:Stepper-View-Warning text@@key_validation_warning:Warning: `;
    if (this.cloudInstance.eskmKeyUser != key.keyOwner) {
      this.keyFormValidityMsg.msg =
        this.keyFormValidityMsg.msg +
        $localize`:Stepper-View-Key Form Validity Msg text@@key_owned_text:Key is owned by ` +
        key.keyOwner;
      valid = false;
    }
    if (!valid) {
      this.keyFormValidityMsg.class = 'text-warning';
      this.keyFormValidityMsg.status = true;
    } else {
      this.keyFormValidityMsg.msg = '';
      this.keyFormValidityMsg.class = '';
      this.keyFormValidityMsg.status = false;
    }
  }

  //fetch list of keys to search
  getKeyList() {
    this.cloudInstanceService
      .getSearchKeys(this.cloudInstance.id, this.selectedCloudType.name)
      .subscribe((cloudKeysData) => {
        this.searchWarning = { status: false, msg: '' };
        if (cloudKeysData.status == 200) this.keyList = cloudKeysData.body.data;
        else if (cloudKeysData.error && cloudKeysData.error.message)
          this._toast.error('Error', cloudKeysData.error.message);
        else if (cloudKeysData.status == 503)
          this._toast.error(
            'Error',
            'Service unavailable, Please try again after some time.'
          );
        else this._toast.error('Error', 'Internal Server error');
      });
  }

  //fire observable on cancel
  onCancelModal() {
    this.onSummaryNext();
    this.cancel.emit();
  }

  //get options of dropdown lists
  getDropdownData() {
    if (
      this.selectedCloudType.name.toLowerCase().includes('salesforce') ||
      this.selectedCloudType.name.toLowerCase().includes('gcp-ekm')
    ) {
      if (this.selectedCloudType.keyFields)
        this.selectedCloudType.keyFields.forEach((data: any) => {
          this.setKeyFieldDropdown(data);
        });
      if (this.selectedCloudType.eskmKeyFields)
        this.selectedCloudType.eskmKeyFields.forEach((data: any) => {
          this.setEskmFieldDropdown(data);
        });
    } else {
      if (this.cloudInstance.keyFields)
        this.cloudInstance.keyFields.forEach((data: any) => {
          this.setKeyFieldDropdown(data);
        });
      if (this.cloudInstance.eskmKeyFields)
        this.cloudInstance.eskmKeyFields.forEach((data: any) => {
          this.setEskmFieldDropdown(data);
        });
    }

    //fetch algorithms
    this.cloudInstanceService
      .getAlgorithmList(this.cloudInstance.cloudType)
      .subscribe((res) => {
        if ((res.status == 200 || res.status == 201) && res.body) {
          this.algorithmList = res.body.data;
        } else if (res.error && res.error.errorMessage) {
          this._toast.error('Error', res.error.errorMessage);
        } else if (res.status == 503) {
          this._toast.error(
            'Error',
            'Service unavailable, Please try again after some time.'
          );
        } else this._toast.error('Error', 'Internal Server error');
      });
  }

  //set Key Field Dropdown lists
  setKeyFieldDropdown(data: {
    jsonField: string;
    type: string;
    label: any;
    values: any[];
  }) {
    // if (data.jsonField == 'salesForceCAs' && data.type == 'select') {
    //   let caList = JSON.parse(localStorage.getItem("caList"))
    //   this.sfCasListReady = caList ? true : false;
    //   this.dropdownOptionList[data.jsonField] = {
    //     "label": data.label,
    //     "jsonField": data.jsonField,
    //     "type": "select",
    //     "values": caList ? caList.map((d: { developerName: string; id: string; }) => { return { id: d.developerName + '-' + d.id, value: d.developerName } }) : {}
    //   }
    // } else if (data.jsonField == 'sfKeyType' && data.type == 'select') {
    //   this.dropdownOptionList[data.jsonField] = {
    //     "label": data.label,
    //     "jsonField": data.jsonField,
    //     "type": "select",
    //     "values": data.values.map((d: { id: any; value: any; }) => { return { id: d.id, value: d.value } })
    //   }
    // } else {
    this.selectedCloudType.keyFields.forEach((d: { jsonField: any }) => {
      if (d.jsonField == data.jsonField) {
        this.dropdownOptionList[data.jsonField] = { ...d, values: data.values };
      }
    });
    //}
  }

  //set Eskm Key Field Dropdown lists
  setEskmFieldDropdown(data: { jsonField: string; type: string; label: any }) {
    if (data.jsonField == 'eskmKeyOwner' && data.type == 'select') {
      this.cloudInstance.configFields = JSON.parse(
        this.cloudInstance.configFields
      );
      //eslint: not sure of catch{} usage, commented for now.
      // try {
      //   this.cloudInstance.configFields = JSON.parse(this.cloudInstance.configFields);
      // } catch { }
      this.dropdownOptionList[data.jsonField] = {
        label: 'ESKM Key Owner',
        jsonField: 'eskmKeyOwner',
        type: 'select',
        values: this.cloudInstance.configFields['serviceAccountName']
          .split(',')
          .map((d: any) => {
            return { id: d, value: d };
          }),
      };
      // } else if (data.jsonField == 'salesForceCAs' && data.type == 'select') {
      //   let caList = JSON.parse(localStorage.getItem("caList"))
      //   this.sfCasListReady = caList ? true : false;
      //   this.dropdownOptionList[data.jsonField] = {
      //     "label": data.label,
      //     "jsonField": data.jsonField,
      //     "type": "select",
      //     "values": caList ? caList.map((d: { developerName: string; id: string; }) => { return { id: !this.selectedCloudType.name.toLowerCase().includes("market-byok") ? d.developerName + '-' + d.id : d.id, value: d.developerName } }) : {}
      //   }
    } else if (data.type == 'select') {
      //else {
      this.dropdownOptionList[data.jsonField] =
        this.cloudInstance.configFields[data.jsonField].split(',');
    }
  }

  //reset the entire upload keys modal data
  resetKeyPopupData() {
    this.keySearchText = '';
    this.onSummaryNext();
    this.searchWarning = { status: false, msg: '' };
    this.selectedKey = {
      keyName: '',
      keyOwner: '',
      algorithm: '',
      keyLength: 0,
      cloudKeyName: '',
      keyFields: {},
    };
    this.validateSelectedKey = {
      keyName: { value: false, msg: '' },
      algorithm: { value: false, msg: '' },
      cloudKeyName: { value: true, msg: '' },
    };
    //reset keyFields
    if (this.selectedCloudType.keyFields) {
      this.resetKeyFields();
    }
    //reset eskmKeyFields
    if (this.selectedCloudType.eskmKeyFields) {
      this.resetEskmKeyFields();
    }

    this.eventList = [
      {
        id: 1,
        label: LABELS.KEY_SELECTION_LABEL,
        text: '',
        completed: false,
        active: true,
        disableChange: false,
      },
      {
        id: 2,
        label: LABELS.SUMMARY_LABEL,
        completed: false,
        active: false,
        disableChange: false,
      },
      {
        id: 3,
        label: LABELS.UPLOADKEY_LABEL,
        text: '',
        completed: false,
        active: false,
        disableChange: false,
      },
    ];
    this.uploadKeyTypeOptions = [
      { name: LABELS.CREATE_UPLOAD_LABEL, id: 1, checked: true },
      { name: LABELS.EXISTING_KEY_LABEL, id: 2, checked: false },
    ];
    this.onCancelModal();
  }

  //update keyFields dynamic field
  onSharedFieldUpdate(event: any, field: any) {
    if (
      (field.type == 'value-array' || field.type == 'multi-select') &&
      Array.isArray(event.event.target.value) &&
      event.event.target.value.length == 2
    ) {
      this.setValueArrayInput(event, field);
    } else if (
      field.type == 'name-value_editable-array' &&
      Array.isArray(event.event.target.value) &&
      event.event.target.value.length == 2
    ) {
      this.validateSelectedKey[field.jsonField].value = true;
      this.validateSelectedKey[field.jsonField].msg = [];
      this.selectedKey.keyFields[field.jsonField] = event.event.target.value[0];
      this.modifiedValueArray[field.jsonField] = event.event.target.value[1];
    } else
      this.selectedKey.keyFields[field.jsonField] = event.event.target.value;
  }

  //set value-array and multi-select on update
  setValueArrayInput(event: any, field: { jsonField: string }) {
    const confValues1: any[] = [],
      confValues2: any[] = [];
    event.event.target.value[0].forEach((elm: string) => {
      if (elm) confValues1.push(elm.trim());
    });
    event.event.target.value[1].forEach((elm: string) => {
      if (elm) confValues2.push(elm.trim());
    });
    //add validation and styling for serviceAccountName
    if (
      field.jsonField == 'serviceAccountName' &&
      !confValues1.length &&
      !confValues2.length
    ) {
      this.validateSelectedKey[field.jsonField].value = false;
      this.validateSelectedKey[field.jsonField].msg = [
        $localize`:Stepper-View-Service Account Validation Msg@@instance_delete_warning:Warning: Instance will be deleted if no service account are present`,
      ];
    } else {
      this.validateSelectedKey[field.jsonField].value = true;
      this.validateSelectedKey[field.jsonField].msg = [];
    }
    this.selectedKey.keyFields[field.jsonField] = confValues1.join(',');
    this.modifiedValueArray[field.jsonField] = confValues2;
  }

  //update EskmKeyAttributes dynamic field
  onEskmKeyAttributesUpdate(
    event: any,
    field: { jsonField: string; type: string }
  ) {
    if (field.jsonField == 'eskmKeyOwner') {
      this.selectedKey.keyOwner = event.event.target.value;
      this.selectedKey.eskmKeyFields[field.jsonField] =
        event.event.target.value;
    } else if (field.type == 'yes-no') {
      this.selectedKey.eskmKeyFields[field.jsonField] =
        event.event.target.value == true ? 'yes' : 'no';
      // if(field.commonLabel && commonLabel) this.setSelectAll(commonLabel);
    } else {
      this.selectedKey.eskmKeyFields[field.jsonField] =
        event.event.target.value;
    }
  }

  //validate indivisual field from key object
  //eslint: removed fieldLable as no longer being used
  //used dynamically to check cloud key name to be checked
  validateFields(
    value: any,
    field: string,
    validator: { required: any; maxLength: any },
    type: string,
    fieldLabel = ''
  ) {
    let result = true;
    console.log(fieldLabel);
    const maxLength = validator.maxLength > 0 ? validator.maxLength : 50;
    if (
      (type == 'value-array' || type == 'multi-select') &&
      validator.required
    ) {
      const configFields = this.selectedKey.keyFields[field];
      this.modifiedValueArray[field] = this.modifiedValueArray[field]
        ? this.modifiedValueArray[field].filter((data: string) => data != '')
        : [];
      //set config fields
      const newConfigFields = this.mergeNewConfigFields(
        configFields,
        this.modifiedValueArray[field]
      );
      this.validateMultiSelectNewConfigFields(
        newConfigFields,
        field,
        maxLength
      );
    } else if (type == 'name-value_editable-array') {
      //needs testing
      const configFields = this.selectedKey.keyFields[field];
      this.modifiedValueArray[field] = this.modifiedValueArray[field]
        ? this.modifiedValueArray[field].filter(
            (data: { name: string; value: string }) =>
              data.name != '' || data.value != ''
          )
        : [];
      //set config fields
      const newConfigFields = configFields.concat(
        this.modifiedValueArray[field]
      );
      this.validateNameArrNewConfigFields(
        newConfigFields,
        field,
        maxLength,
        validator
      );
    } else if (validator.required && !value && type !== 'yes-no') {
      this.validateSelectedKey[field].value = false;
      this.validateSelectedKey[field].msg = [
        VALIDATION_MESSAGES.FIELD_REQUIRED,
      ];
    } else if (value && value.length > maxLength) {
      this.validateSelectedKey[field].value = false;
      this.validateSelectedKey[field].msg = [
        VALIDATION_MESSAGES.VALIDATION_CHECK_MSG +
          maxLength +
          VALIDATION_MESSAGES.CHARACTER,
      ];
    } else if (type == 'date-time-picker') {
      const val = this.selectedKey.keyFields[field];
      if (Array.isArray(val) && val.length == 2) {
        if (!this.isDateTimeLocalString(val[0])) {
          this.validateSelectedKey[field].value = false;
          this.validateSelectedKey[field].msg = [
            $localize`:Stepper-View-Invalid Datetime validation Msg@@invalid_date:Invalid datetime value`,
          ];
        } else {
          this.selectedKey.keyFields[field] = val.join('');
          this.validateSelectedKey[field].value = true;
          this.validateSelectedKey[field].msg = [];
        }
      }
    } else {
      this.validateSelectedKey[field].value = true;
      this.validateSelectedKey[field].msg = [];
    }
    Object.keys(this.validateSelectedKey).forEach((key) => {
      if (this.validateSelectedKey[key].value == false) {
        result = false;
      }
    });
    this.existingUploadKeySubmitEnabled = result;
  }

  isDateTimeLocalString(str: string): boolean {
    const date = new Date(str);
    return (
      !isNaN(date.getTime()) &&
      str.length === 19 && // YYYY-MM-DDTHH:mm:ss
      str.charAt(10) === 'T'
    );
  }

  //validate new Config Fields for multiselect input
  validateMultiSelectNewConfigFields(
    newConfigFields: string | undefined,
    field: string,
    maxLength: number
  ) {
    if (!newConfigFields || newConfigFields == '') {
      this.validateSelectedKey[field].value = false;
      this.validateSelectedKey[field].msg = [
        VALIDATION_MESSAGES.FIELD_REQUIRED,
      ];
    } else if (this.hasDuplicates(this.modifiedValueArray[field])) {
      this.validateSelectedKey[field].value = false;
      this.validateSelectedKey[field].msg = ['Field values must be unique'];
    } else if (
      this.modifiedValueArray[field].filter(
        (d: { length: number }) => d.length > maxLength
      ).length
    ) {
      this.validateSelectedKey[field].value = false;
      this.validateSelectedKey.msg =
        VALIDATION_MESSAGES.VALIDATION_CHECK_MSG +
        maxLength +
        VALIDATION_MESSAGES.CHARACTER;
    } else {
      this.validateSelectedKey[field].value = true;
      this.validateSelectedKey[field].msg = '';
    }
  }

  //validate new Config Fields for multiselect input
  validateNameArrNewConfigFields(
    newConfigFields: any,
    field: string | number,
    maxLength: number,
    validator: { required: any }
  ) {
    if (validator.required && (!newConfigFields || newConfigFields.length)) {
      this.validateSelectedKey[field].value = false;
      this.validateSelectedKey[field].msg = [
        VALIDATION_MESSAGES.FIELD_REQUIRED,
      ];
    } else if (this.checkTagsListDuplicates(newConfigFields)) {
      this.validateSelectedKey[field].value = false;
      this.validateSelectedKey[field].msg = ['Field values must be unique'];
    } else if (
      this.modifiedValueArray[field].filter(
        (d: string | any[]) => d.length > maxLength
      ).length
    ) {
      this.validateSelectedKey[field].value = false;
      this.validateSelectedKey.msg =
        VALIDATION_MESSAGES.VALIDATION_CHECK_MSG +
        maxLength +
        VALIDATION_MESSAGES.CHARACTER;
    } else {
      this.validateSelectedKey[field].value = true;
      this.validateSelectedKey[field].msg = '';
    }
  }

  //check list for duplicates
  checkTagsListDuplicates(list: any[]) {
    const idsAlreadySeen: any[] = [],
      valuesAlreadySeen: any[] = [];
    list.forEach((data: { name: any; value: any }) => {
      const name = data.name,
        value = data.value;
      if (idsAlreadySeen.indexOf(name) == -1) {
        idsAlreadySeen.push(name);
      }
      if (valuesAlreadySeen.indexOf(value) == -1) {
        valuesAlreadySeen.push(value);
      }
    });
    if (
      list.length == idsAlreadySeen.length &&
      list.length == valuesAlreadySeen.length
    )
      return false;
    else return true;
  }

  //enable, disable search existing key step submit btn
  existingKeySearchCheck(event: any) {
    this.eventList[0].completed = true;
    this.eventList[0].disableChange = true;
    this.existingKeySearchSubmitEnabled = this.keyList.some(
      (d: any) => d.keyName == event.target.value
    );
  }

  //check min max dates
  compareDate(
    minDate: string | number | Date,
    maxDate: string | number | Date
  ) {
    if (!minDate || !maxDate) return true;
    const min = new Date(minDate).valueOf();
    const max = new Date(maxDate).valueOf();
    return min < max;
  }

  getButtonText(buttonType: string) {
    switch (buttonType) {
      case 'create':
        return $localize`:Stepper-View\:button text for Create: Create`;
        break;
      case 'cancel':
        return $localize`:Stepper-View\:button text for Cancel: Cancel`;
        break;
      case 'next':
        return $localize`:Stepper-View\:button text for Next: Next`;
        break;
      case 'upload':
        return $localize`:Stepper-View\:button text for Upload: Upload`;
        break;
      default:
        return '';
    }
  }
}
