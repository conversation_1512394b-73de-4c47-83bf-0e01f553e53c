<!-- TODO add i18n support in complete file-->
<mat-card class="feature-card">
  <mat-card-header>
    <mat-card-title class="card-title">
      Feature Under Development
    </mat-card-title>
  </mat-card-header>
  <mat-card-content class="card-content">
    <p>The service is updated continuously and this feature will be rolled out soon.</p>
  </mat-card-content>
</mat-card>


<div class="filter-container custom-elevation">
  <form [formGroup]="filterForm">
    <mat-form-field appearance="outline" class="filter-field">
      <mat-label>Key</mat-label>
      <input matInput formControlName="keyName" />
    </mat-form-field>

    <mat-form-field appearance="outline" class="filter-field">
      <mat-label>Client</mat-label>
      <mat-select formControlName="clientFunction">
        @for (client of clients; track client.clientName) {
        <mat-option [value]="client.clientName">{{ client.clientName
          }}</mat-option>
        }
      </mat-select>
    </mat-form-field>

    <mat-form-field appearance="outline" class="filter-field">
      <mat-label>Assessment Result</mat-label>
      <mat-select formControlName="assessmentResultFunction">
        @for (assessResult of assessmentResults; track assessResult.detail) {
        <mat-option [value]="assessResult.detail">
          {{ assessResult.detail }}
        </mat-option>
        }
      </mat-select>
    </mat-form-field>

    <div class="button-row">
      <lib-taas-button buttonText="Clear Filter" buttonType="secondary" (buttonPressedEvent)="resetFilters()"></lib-taas-button>
      <lib-taas-button buttonText="Apply Filter" buttonType="primary" (buttonPressedEvent)="onFilterChange()"></lib-taas-button>
    </div>
  </form>
</div>

<div>
  <app-taas-horizontal-stacked-bar></app-taas-horizontal-stacked-bar>
</div>

<div class="dashboard-table">
  <lib-sortable-table uniqueId="dashboard" [dataSource]="dataSource" [columns]="tableColumns">
  </lib-sortable-table>
</div>