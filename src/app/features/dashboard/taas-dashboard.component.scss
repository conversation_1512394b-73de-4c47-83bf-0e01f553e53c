.filter-field {
  padding-top: 10px;
  width: 300px;
  //margin: 5px;
  margin-right: 20px;
  margin-left: 20px;
}

.filter-card {
  margin-bottom: 40px;
  //border: 1px solid black; 
  border-radius: 8px;
  margin-top: 100px;
}

form {
  // padding-top: 10px;
  padding-left: 10px;
}


.mat-mdc-card,
.md-card,
.mat-mdc-card-content {
  border-radius: 8px !important;
}

.button-row {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.dashboard-table {
  margin-top: 100px;
  margin-bottom: 20px;
}

.feature-card {
  max-width: 600px;
  margin: 20px auto;
  border-radius: 24px !important;
  /* Increased border radius */
  border-width: thin;
  border-color: black;
}

.card-title {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 16px;
  width: 100%;
  text-align: center;
}

.card-content {
  text-align: center;
  color: rgba(0, 0, 0, 0.7);
}

:host {
  display: block;
  padding: 20px;
}

/* Center header content */
::ng-deep .mat-mdc-card-header {
  justify-content: center;
  padding: 16px 16px 0;
}

/* Remove default margin from header */
::ng-deep .mat-mdc-card-header-text {
  margin: 0;
  text-align: center;
}