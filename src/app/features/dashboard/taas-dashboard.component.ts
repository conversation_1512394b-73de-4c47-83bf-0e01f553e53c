import { Component, OnInit, TemplateRef, ViewChild, inject } from '@angular/core';
import { TaasHorizontalStackedBarComponent } from '../../shared/components/taas-horizontal-stacked-bar/taas-horizontal-stacked-bar.component';
import { PaginatedDataSource, SortableTableComponent } from 'utimaco-common-ui-angular';
import { TableData } from 'utimaco-common-ui-angular';
import { TableColumn } from 'utimaco-common-ui-angular';
import { MatTableModule } from '@angular/material/table';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { TaasButtonComponent } from 'utimaco-common-ui-angular';
import { AssessmentResult } from './models/assessment-result';
import { Client } from './models/client';
import { TableOptionsContext } from './models/table-options-context';
import { UserRoleService } from '../../core/services/role/user-role.service';
import { Role } from '../../core/models/roles.enum';

@Component({
  selector: 'app-taas-dashboard',
  standalone: true,
  imports: [
    TaasHorizontalStackedBarComponent,
    SortableTableComponent,
    MatTableModule,
    MatFormFieldModule,
    MatInputModule,
    CommonModule,
    FormsModule,
    MatSelectModule,
    ReactiveFormsModule,
    MatCardModule,
    TaasButtonComponent,
    TaasHorizontalStackedBarComponent,
  ],
  templateUrl: './taas-dashboard.component.html',
  styleUrl: './taas-dashboard.component.scss',
})
export class TaasDashboardComponent implements OnInit {
  resetFilters() {
    this.filterForm.reset();
  }
  onFilterChange() {
    throw new Error('Method not implemented.');
  }

  testData: string = '';
  testData1: string = '';
  testData2: string = '';
  userName: string | null = null;
  
  // Keep track of current user permissions
  public hasViewerPermission = false;
  public hasEditorPermission = false;
  public hasAdminPermission = false;

  private fb = inject(FormBuilder);
  private userRoleService = inject(UserRoleService);
  
  constructor() {
    this.generateTableColumns();
    this.filterForm = this.fb.group({
      keyName: [''],
      clientFunction: [''],
      assessmentResultFunction: [''],
    });
    
    // Initialize role-based permissions
    this.initializeRolePermissions();
  }
  
  /**
   * According to the matrix, dashboard is available to all permission levels
   * but should only show stats on keys the user has permission for
   */
  private initializeRolePermissions(): void {
    // Check if user has Viewer permission or higher
    this.hasViewerPermission = this.userRoleService.hasAnyRole([
      Role.INSTANCE_VIEWER,
      Role.INSTANCE_EDITOR,
      Role.INSTANCE_ADMIN,
      Role.ORG_ADMIN
    ]);
    
    // Check if user has Editor permission or higher
    this.hasEditorPermission = this.userRoleService.hasAnyRole([
      Role.INSTANCE_EDITOR,
      Role.INSTANCE_ADMIN,
      Role.ORG_ADMIN
    ]);
    
    // Check if user has Admin permission
    this.hasAdminPermission = this.userRoleService.hasAnyRole([
      Role.INSTANCE_ADMIN,
      Role.ORG_ADMIN
    ]);
  }

  ngOnInit(): void {
    // Set up subscription to user roles changes
    this.userRoleService.userRoles$.subscribe(() => {
      this.initializeRolePermissions();
    });
    
    this.loadDropdownData();
  }
  
  /**
   * Filter data based on user permissions
   * The dashboard should only show stats/data for keys the user has permissions for
   * @param data The raw data to be filtered
   * @returns Filtered data based on user permissions
   */
  private filterDataByUserPermissions(data: any[]): any[] {
    // If user is org-admin, show all data
    if (this.userRoleService.hasRole(Role.ORG_ADMIN)) {
      return data;
    }
    
    // TODO: filter data based on instance permissions
    // This would typically involve checking instance IDs against user's permissions    
    // In a real implementation, you would check against specific instance IDs
    // that the user has access to based on their permissions
    return data.filter(item => {
      // For demonstration purposes - in a real implementation, this would
      // check if the user has permissions for the specific instance's instanceId
      if (item.instanceId) {
        // Simulate checking if user has permission for this instance
        return this.hasPermissionForInstance(item.instanceId);
      }
      return this.hasViewerPermission;
    });
  }
  
  /**
   * Check if user has permission for a specific instance
   * This is a simplified implementation - in a real app, this would check
   * against a permissions service or stored permissions data
   * @param instanceId The instance ID to check permissions for
   * @returns Whether the user has permissions for the instance
   */
  private hasPermissionForInstance(instanceId: string): boolean {
    // For demo purposes - in a real implementation this would check
    // against actual instance permissions
    
    // Simulating an allowed instances list
    const allowedInstances = ['instance-1', 'instance-3'];
    
    // If user is admin, they can see all instances
    if (this.hasAdminPermission) {
      return true;
    }
    
    // Otherwise, check if instance is in allowed list
    return allowedInstances.includes(instanceId);
  }

  filterForm!: FormGroup;
  clients: Client[] = [];
  assessmentResults: AssessmentResult[] = [];
  @ViewChild('tableOptions', { static: true }) tableOptions!: TemplateRef<TableOptionsContext>;
  public tableData: TableData[] = [];
  public tableColumns: TableColumn[] = [];
  public dataSource!: PaginatedDataSource;

  loadDropdownData() {
    // TODO: In a real implementation, these would be loaded from a service
    const allClients = [
      { clientId: 1, clientName: 'Client1', instanceId: 'instance-1' },
      { clientId: 2, clientName: 'Client2', instanceId: 'instance-2' },
      { clientId: 3, clientName: 'Client3', instanceId: 'instance-3' },
    ];
    
    // Filter clients based on user permissions
    this.clients = this.filterDataByUserPermissions(allClients);
    
    // Assessment results don't need filtering - they're just status values
    this.assessmentResults = [
      { assessmentResultId: 1, detail: 'Compliant' },
      { assessmentResultId: 2, detail: 'Risk' },
      { assessmentResultId: 3, detail: 'Pending' },
    ];
    
    // TODO: In a real implementation, HTTP calls would be made and filtered:
    // this.http.get<Client[]>('/api/clients').subscribe(data => {
    //   this.clients = this.filterDataByUserPermissions(data);
    // });
    
    // If we had real table data, we'd filter it the same way
    // this.tableData = this.filterDataByUserPermissions(rawTableData);
  }

  generateTableColumns() {
    this.tableColumns = [
      {
        header: $localize`Key Name`,
        valuePropertyName: 'keyName',
        sortable: true,
        initialSortDirection: 'asc',
      },
      {
        header: $localize`Algorithm`,
        valuePropertyName: 'algorithm',
        sortable: true,
        initialSortDirection: 'asc',
      },
      {
        header: $localize`Created`,
        valuePropertyName: 'created',
        sortable: true,
        initialSortDirection: 'asc',
      },
      {
        header: $localize`Client`,
        valuePropertyName: 'client',
        sortable: true,
        initialSortDirection: 'asc',
      },
      {
        header: $localize`Assessment Date`,
        valuePropertyName: 'assessment date',
        sortable: true,
        initialSortDirection: 'asc',
      },
      {
        header: $localize`Assessment Result`,
        valuePropertyName: 'assessment Result',
        sortable: true,
        initialSortDirection: 'asc',
      },
    ];
  }
}
