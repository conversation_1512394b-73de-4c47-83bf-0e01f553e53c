<div class="divContainer">
  <div class="row row-padding">
    <!-- <div class="d-flex align-items-center" style="min-width: 1500px;"> -->
    <div>
      <!-- cloud type and logo section -->
      <div>
        <div>
          <!-- TODO: Have to change below link to change based on cloud client, currently restructured
                      menuitems for POC, hence /cloud-instances/ route broke -->
          <button routerLink="/cloud-clients/azure">
            <mat-icon>arrow_back_ios</mat-icon>
          </button>
          <span class="labelHeader" i18n="Manage Cloud Keys: label for Cloud Instance">
            Cloud Instance:
          </span>
          @if (!isReadOnly) {
          <div style="display: inline">
            <img src="assets/icons/{{ selectedCloudInstance.cloudType }}.svg" title="{{ selectedCloudInstance.cloudType }}" class="rowIcon"
              alt="type" />
            <span class="labelValue">{{
              selectedCloudInstance.instanceName
              }}</span>
          </div>
          }
        </div>
      </div>
    </div>
    <!-- add + upload key button-->
    @if (!isReadOnly) {
    <lib-taas-button btnId="createAndUploadKeyBtn" (click)="openUploadKeyModal()" buttonType="highlight" icon="add">
      @if (selectedCloudType.uploadKeyEnabled) {
      <span id="openUploadKeyBtnText" i18n="Manage Cloud Keys: button text for Create/Upload key">Create/Upload Key</span>
      } @else if (!selectedCloudType.uploadKeyEnabled) {
      <span i18n="Manage Cloud Keys: button text for Create key">Create Key</span>
      }
    </lib-taas-button>
    }
    <!-- </div> -->
  </div>

  <div class="row-padding row-last">
    <ng-template #actionTemplate let-key="row">
      <select class="largeCombo" id="manageCloudKeysAction" (change)="onActionChange($event, key)" [(value)]="key.action"
        (click)="$event.stopPropagation()">
        <option class="alignCenter" *ngFor="
            let action of actionList | appFilterAction : key : selectedCloudType
          " id="{{ 'cloudKeysAction' + action.action }}" [value]="action.action">
          {{ action.action }}
        </option>
      </select>
    </ng-template>

    <!-- Cloud Table -->
    @if (tableData.length > 0 && isKeysLoaded) {
    <lib-sortable-table uniqueId="kmaas" [dataSource]="dataSource" [columns]="tableColumns" [selectableRows]="true"
      (rowSelected)="rowSelected($event)">
    </lib-sortable-table>
    }
  </div>

  <!-- if no keys are fetched -->
  @if (tableData.length === 0 && isKeysLoaded) {
  <div class="noConfig">
    <span class="width: 100%;" i18n="Manage Cloud Keys: No keys available text">No Keys Available</span>
  </div>
  }

  <!-- loader while fetch is in progress -->
  <lib-taas-modal-loading-spinner uniqueId="manageCloudKeysLoadingSpinner" width="300px" height="300px" [spinnerVisible]="!isKeysLoaded">
  </lib-taas-modal-loading-spinner>
</div>

<lib-taas-modal-dialogue uniqueId="uploadKey" [title]="uploadKeyLocalized" width="900px" height="700px" [modalVisible]="openUploadModal"
  (cancel)="onCancelModal()" [showFooter]="false" #uploadKeyModalRef>
  @if(openUploadModal) {
  <app-taas-upload-key [modalReference]="uploadKeyModalRef" [cloudInstance]="selectedCloudInstance" [selectedCloudType]="selectedCloudType"
    (cancel)="onCancelModal()"></app-taas-upload-key>
  }
</lib-taas-modal-dialogue>

<!-- TODO: move this dialog or its content to its own component to reduce complexity in this file and lines of code -->
<!-- key details modal -->
<lib-taas-modal-dialogue [modalVisible]="showModal" [title]="getKeyDetailsTitle()" uniqueId="keyDetailsModal" [showFooter]="false"
  (cancel)="closeModal()" variant="info">
  <div class="modal-body form-group">
    <!-- show specific fields -->
    <lib-taas-modal-loading-spinner uniqueId="manageCloudKeysLoadingSpinner" width="300px" height="300px" [spinnerVisible]="((keyDetails.status && !(keyDetails.status.includes('Error') || keyDetails.status.includes('Delete'))) &&
    ((selectedCloudType.fetchCloudKeyDetails || selectedCloudType.fetchESKMKeyDetails) && !eskmKeyFieldsLoaded))">
    </lib-taas-modal-loading-spinner>
    @if ((keyDetails.status && !(keyDetails.status.includes('Error') ||
    keyDetails.status.includes('Delete'))) &&
    (((selectedCloudType.fetchCloudKeyDetails ||
    selectedCloudType.fetchESKMKeyDetails) && eskmKeyFieldsLoaded) ||
    (!selectedCloudType.fetchCloudKeyDetails &&
    !selectedCloudType.fetchESKMKeyDetails))) {
    <div>
      <div>
        <div>
          <label i18n="Manage Cloud Keys-KeyDetails: label for Key Name">Name</label>
        </div>
        <div>
          <input data-testid="modal-input-key-name" class="largeInput sublabel" [ngModel]="keyDetails.name" [disabled]="true" />
        </div>
      </div>

      @for (field of selectedCloudType.keyFields; track field.jsonField) { @if
      (keyDetails.keyAttributes[field.jsonField] ||
      keyDetails.keyAttributes[field.jsonField]===false) {
      <div class="row disabled">
        <div>
          <label>{{ getLocalizedKeyFieldsLabel(field) }}</label>
        </div>
        <div>
          @if (field.type !== 'name-value-array' && field.type !==
          'name-value_editable-array' && field.type !== 'select' && field.type
          !== 'yes-no' && field.type !== 'date-time-picker' && field.type !==
          'text-area') {
          <input data-testid="modal-input-sublabel" class="largeInput sublabel" [ngModel]="keyDetails.keyAttributes[field.jsonField]"
            [disabled]="true" />
          } @else if (field.type === 'text-area') {
          <textarea data-testid="modal-input-textarea" class="largeTextArea" [ngModel]="keyDetails.keyAttributes[field.jsonField]"
            id="{{ field.jsonField }}" [disabled]="true">
          </textarea>
          } @else if (field.type === 'date-time-picker') {
          <input attr.data-testid="modal-input-{{ field.jsonField }}" class="largeInput sublabel" [disabled]="true"
            [ngModel]="convertTime(keyDetails.keyAttributes[field.jsonField])" />
          } @else if (field.type === 'yes-no') {
          <label data-testid="modal-input-enabled" class="sublabel">
            {{
            keyDetails.keyAttributes[field.jsonField] === true
            ? "Yes"
            : keyDetails.keyAttributes[field.jsonField] === false
            ? "No"
            : "-"
            }}
          </label>
          } @else if (field.type === 'select') {
          <input data-testid="modal-input-key-vault" class="largeInput sublabel" [ngModel]="keyDetails.keyAttributes[field.jsonField]"
            [disabled]="true" />
          } @else if ((field.type === 'name-value-array' || field.type ===
          'name-value_editable-array') &&
          keyDetails.keyAttributes[field.jsonField]) {
          <app-taas-shared-input [inputField]="field" [value]="keyDetails.keyAttributes[field.jsonField]" [disabled]="true"
            id="{{ field.jsonField }}">
          </app-taas-shared-input>
          }
        </div>
      </div>
      } } @if (eskmKeyFieldsLoaded) { @if (keyUri) {
      <div class="row disabled">
        @if (selectedCloudType.name.toLowerCase().includes('gcp')) {
        <div>
          <label for="'field.jsonField" i18n="Manage Cloud Keys: label for Key URI">Key URI</label>
        </div>
        } @if (selectedCloudType.name.toLowerCase().includes('microsoft')) {
        <div>
          <label for="'field.jsonField" i18n="Manage Cloud Keys: label for DKE Key URL">DKE Key URL</label>
        </div>
        } @if (selectedCloudType.name.toLowerCase().includes('microsoft') ||
        selectedCloudType.name.toLowerCase().includes('gcp')) {
        <div>
          <input class="largeInput sublabel" [disabled]="true" id="keyUri" [ngModel]="keyUri" />
        </div>
        }
      </div>
      } @if (keyAlogSize) {
      <div class="row disabled">
        @if (selectedCloudType.name.toLowerCase().includes('salesforce')) {
        <div>
          <label for="keyAlogSize" i18n="Manage Cloud Keys: label for Key Algorithm">Key Algorithm</label>
        </div>
        <div>
          <input class="largeInput sublabel" [disabled]="true" id="keyAlogSize" [ngModel]="keyAlogSize" />
        </div>
        }
      </div>
      } @for (field of selectedCloudType.eskmKeyFields; track field.jsonField) {
      @if (!field.commonLabel) {
      <div class="row disabled">
        @if (field.type !== 'yes-no') {
        <div>
          <label for="'field.jsonField'">{{ field.label | appToText }}</label>
        </div>
        } @if (field.type === 'select' || field.type === 'text-field') {
        <div>
          <input class="largeInput sublabel" [disabled]="true" [ngModel]="keyDetails.eskmKeyAttributes[field.jsonField]" />
        </div>
        } @else if (field.type !== 'yes-no' && field.type !== 'grid-view') {
        <div>
          <app-taas-shared-input [inputField]="field" [value]="keyDetails.eskmKeyAttributes[field.jsonField]" id="{{ field.jsonField }}">
          </app-taas-shared-input>
        </div>
        } @else if (field.type === 'yes-no') {
        <div>
          <app-taas-shared-input [inputField]="field" [value]="
              keyDetails.eskmKeyAttributes[field.jsonField] === 'yes'
                ? true
                : false
            " [disabled]="true" id="{{ field.jsonField }}">
          </app-taas-shared-input>
          <label class="sublabel" for="'field.jsonField">
            {{ field.label | appToText }}
          </label>
        </div>
        } @else if (field.type === 'grid-view') {
        <div>
          <app-taas-shared-input [inputField]="field" [value]="keyDetails.eskmKeyAttributes[field.jsonField]" id="{{ field.jsonField }}"
            [disabled]="true">
          </app-taas-shared-input>
        </div>
        }
      </div>
      } } @for (fieldList of eskmKeyAttributes; track fieldList) {
      <div class="width100">
        @if (fieldList[0] && fieldList[0].commonLabel) {
        <div>
          <label>{{ fieldList[0].commonLabel }}</label>
        </div>
        } @for (field of fieldList; track field) {
        <div class="width100">
          @if (field.type !== 'yes-no' && field.type !== 'grid-view') {
          <div>
            <input class="largeInput sublabel" [disabled]="true" [ngModel]="keyDetails.eskmKeyAttributes[field.jsonField]" />
          </div>
          } @if (field.type === 'yes-no') {
          <div>
            <app-taas-shared-input [inputField]="field" [value]="
                keyDetails.eskmKeyAttributes[field.jsonField] === 'yes'
                  ? true
                  : false
              " [disabled]="true" id="{{ field.jsonField }}">
            </app-taas-shared-input>
            <label class="sublabel" for="'field.jsonField">
              {{ field.label | appToText }}
            </label>
          </div>
          }
        </div>
        }
      </div>
      } }
    </div>
    }
    <!-- error msg on update/upload failure -->
    @if (keyDetails.status && (keyDetails.status.includes('Error') ||
    keyDetails.status.includes('Delete'))) {
    <div>
      <span>{{ keyDetails.statusDetails }}</span>
    </div>
    }
  </div>

  <div class="modal-footer">
    <lib-taas-button btnId="keyDetailsClose" (click)="closeModal()" [buttonText]="keyDetailsCloseLocalized">
    </lib-taas-button>
  </div>
</lib-taas-modal-dialogue>

<lib-taas-modal-dialogue uniqueId="deleteConfirmationModal" [modalVisible]="deleteKeyConfirmation.visible" [showFooter]="true"
  variant="danger" width="550px" height="300px" [title]="deleteKeyConfirmation.header" (cancel)="onCancelDeleteKeyConfirmation()"
  (confirm)="onDelete(deleteKeyConfirmation.onForceDelete)" [disableConfirmButton]="!deleteKeyBtn">
  <div class="delete-modal-content">
    <span [innerHTML]="deleteKeyConfirmation.msg"></span>

    @if (deleteKeyConfirmation.deleteFromCloudCheck.keyStatus !== 'Not Uploaded' &&
    !deleteKeyConfirmation.deleteFromCloudCheck.keyStatus.includes('Delete'))
    {
    @for (field of selectedCloudType.removeKeyFields; track field.jsonField) {
    <div>
      <div>
        <label>
          @if (field.required) {
          <span>*</span>
          }
          {{ field.label }}
        </label>
      </div>
      @if (selectedDeleteAction['actionAttributes']) {
      <div>
        <app-taas-shared-input [inputField]="field" [value]="
              selectedDeleteAction['actionAttributes']['removeKeyAttributes'][field.jsonField]
            " id="{{ 'deleteConf' + field.jsonField }}" (onUpdate)="onRemoveFieldUpdate($event, field)">
        </app-taas-shared-input>
      </div>
      }
      @if (validateRemoveKeyFields[field.jsonField] &&
      validateRemoveKeyFields[field.jsonField].msg.length) {
      <div>
        <mat-error>{{validateRemoveKeyFields[field.jsonField].msg}}</mat-error>
      </div>
      }
      @if (field.fieldNote) {
      <div>
        <span class="fontSmall">Note: {{ field.fieldNote }}</span>
      </div>
      }
    </div>
    }
    }
    @if (deleteKeyConfirmation.deleteFromCloudCheck.status &&
    !deleteKeyConfirmation.deleteFromCloudCheck.keyStatus.includes('Delete'))
    {
    <div>
      <input type="checkbox" id="deleteFromCloudCheck" [disabled]="!deleteKeyBtn"
        [(ngModel)]="deleteKeyConfirmation.deleteFromCloudCheck.value" (change)="onDeleteESKMCheckboxChange()" />
      <span>
        {{ deleteKeyConfirmation.deleteFromCloudCheck.msg }}
      </span>
    </div>
    } @if (keyAssociationMsg) {
    <div>
      <span class="text-success fontSmall">&#9830; {{ keyAssociationMsg }}</span>
    </div>
    } @if (deleteKeyConfirmation.deleteFromCloudCheck.status &&
    deleteKeyConfirmation.deleteFromCloudCheck.value) {
    <div>
      <span class="text-warning fontSmall" i18n="
            Manage Cloud Keys: Warning text showing key deltion is irreversible
          ">
        &#9830; Warning! This action will delete the key from
      </span>
    </div>
    }
  </div>
</lib-taas-modal-dialogue>

<!-- reupload key modal -->
<lib-taas-modal-dialogue uniqueId="reuploadKeyModal" [modalVisible]="openReuploadModal" [showFooter]="false" height="620px"
  (cancel)="onCancelReuploadModal()" #modalRef>
  @if(openReuploadModal) {
  <app-taas-reupload-key [modalReference]="modalRef" [cloudType]="selectedCloudType" [selectedAction]="selectedUploadEditAction"
    [key]="selectedKey" [keyList]="tableData" [cloudInstance]="selectedCloudInstance"
    (cancel)="onCancelReuploadModal()"></app-taas-reupload-key>
  }
</lib-taas-modal-dialogue>