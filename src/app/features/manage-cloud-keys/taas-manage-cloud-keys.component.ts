import {
  Component,
  inject,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { ActivatedRoute, RouterLink, RouterModule } from '@angular/router';
import { TaasCloudInstanceService } from '../../core/services/cloud/taas-cloud-instance.service';
import { UserRoleService } from '../../core/services/role/user-role.service';
import { Role } from '../../core/models/roles.enum';
import { TaasSharedInputComponent } from '../../shared/components/taas-shared-input/taas-shared-input.component';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MatDialogModule } from '@angular/material/dialog';
import { TaasUploadKeyComponent } from '../upload-key/taas-upload-key.component';
import { ToText } from '../../core/pipes/toText';
import { ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { FilterAction } from '../../core/pipes/filterAction.pipe';
import { TaasReuploadKeyComponent } from '../reupload-key/taas-reupload-key.component';
import {
  TaasButtonComponent,
  TaasModalDialogueComponent,
  TaasModalLoadingSpinnerComponent,
  ToastService,
} from 'utimaco-common-ui-angular';
import { FIELD_KEY_TRANSLATIONS } from '../../shared/components/taas-shared-localization';
import { Subject, takeUntil } from 'rxjs';
import {
  SortableTableComponent,
  TableColumn,
  PaginatedDataSource,
  ClientSidePaginatedDataSource,
} from 'utimaco-common-ui-angular';
import { KeyFields } from '../../shared/components/models/key-fields';
import { FetchedKey } from '../reupload-key/components/models/fetched-key.model';

// TODO: reduce component size, externalize some logic in services or other components, more than 1000 lines of code is way to big and bad to read
@Component({
  selector: 'app-taas-manage-cloud-keys',
  templateUrl: './taas-manage-cloud-keys.component.html',
  styleUrls: ['./taas-manage-cloud-keys.component.scss'],
  imports: [
    MatIcon,
    MatIconModule,
    MatFormFieldModule,
    FormsModule,
    TaasSharedInputComponent,
    CommonModule,
    RouterModule,
    RouterLink,
    MatDialogModule,
    TaasUploadKeyComponent,
    ToText,
    ReactiveFormsModule,
    MatInputModule,
    FilterAction,
    SortableTableComponent,
    TaasModalDialogueComponent,
    TaasButtonComponent,
    TaasModalLoadingSpinnerComponent,
    TaasReuploadKeyComponent
  ],
  standalone: true,
})
export class TaasManageCloudKeysComponent implements OnInit, OnDestroy {
  @ViewChild('keyDetailsModal') keyDetailsModal: any; //key details popup element
  @ViewChild('deleteConfirmationModal') deleteConfirmationModal: any; //delete modal popup element
  @ViewChild('deleteConfirmationToggle') deleteConfirmationToggle: any; //delete popup toggle
  @ViewChild('versionConfirmationModal') versionConfirmationModal: any; //version modal popup
  @ViewChild('versionConfirmationToggle') versionConfirmationToggle: any; //delete popup toggle
  @ViewChild('reuploadFormToggle') reuploadFormToggle: any; //reupload popup toggle
  @ViewChild('exportKeyModal') exportKeyModal: any; //export modal popup
  @ViewChild('exportKeyToggle') exportKeyToggle: any; //export popup toggle
  loggedUser: string = '';
  eskmKeyAttributes: any = [];
  keyUri: string = '';
  tablePaginator: any = {
    //pagination and sort data object
    tableSetRows: 15,
    first: 0,
    last: 15,
    sortField: 'name',
    sortOrder: -1,
  };
  public dataSource!: PaginatedDataSource;
  openUploadModal = false; //upload modal toggle flag
  openReuploadModal = false; //reupload modal toggle flag
  isKeysLoaded = false; //set to true after keys are loaded
  eskmKeyFieldsLoaded = false; //fields loaded flag
  selectedKeyRow = {}; //selected grid row
  selectedKey = {
    name: '',
    keyId: '',
    keyAttributes: {},
    keyAlgoSize: '',
    eskmKeyAttributes: {},
    keySource: '',
  }; //selected key from keyList
  selectedDeleteAction: any = {}; //delete action object sent to backend
  deleteKeyConfirmation = {
    //delete confirmation popup data object
    header: $localize`:Manage Cloud Key-Header for confirmation box@@delete_alert:Alert!`,
    msg: '',
    onForceDelete: false,
    visible: false,
    deleteFromCloudCheck: {
      status: false,
      value: false,
      msg: '',
      keyStatus: '',
    },
  };
  versionKeyConfirmation = {
    //version confirmation popup data object
    header: $localize`:Manage Cloud Key-Header for confirmation box@@versionkey_alert:Alert!`,
    msg: null,
    showDialog: false,
    selectedKey: null,
    action: null,
    field: null,
  };
  validateRemoveKeyFields: any = []; //remove key fields validator object
  deleteKeyBtn = true; //delete button enable disable flag
  keyAssociationMsg: string | null | undefined; //warning on keyDelete based on keynassociation
  selectedCloudInstance = {
    //cloud instance selected from cloud dashboard
    cloudID: -1,
    cloudType: '',
    id: -1,
    eskmKeyUser: '',
    instanceName: '',
    eskmKeyFields: [],
    keyFields: [],
  };
  //details of keys on selecting row
  keyDetails: any = {
    name: '',
    keyId: null,
    status: null,
    keySource: null,
    keyOwner: null,
    eskmKeyAttributes: [],
    keyAttributes: [],
    statusDetails: '',
    action: 'Select',
  };
  actionList = [
    //list of actions in key action dropdown
    {
      action: $localize`:Manage Cloud Keys-dropdown label for Select@@select_label:Select`,
      showOnNotUpload: true,
      // All roles can select/view keys
      requiredPermission: 'viewer'
    },
    {
      action: $localize`:Manage Cloud Keys-dropdown label for Edit@@edit_label:Edit`,
      showOnNotUpload: false,
      // Only Editor and Admin roles can edit keys
      requiredPermission: 'editor'
    },
    {
      action: $localize`:Manage Cloud Keys-dropdown label for Delete@delete_label:Delete`,
      showOnNotUpload: true,
      // Only Admin roles can delete keys
      requiredPermission: 'admin'
    },
    {
      action: $localize`:Manage Cloud Keys-dropdown label for Upload@@upload_label:Upload`,
      showOnNotUpload: true,
      // Only Editor and Admin roles can upload keys
      requiredPermission: 'editor'
    },
    // {action:"New Version",showOnNotUpload:true}
  ];
  selectedUploadEditAction = { header: '', action: '' }; //edit/reupload popup data
  selectedCloudInstanceId: number = 0;
  supportedCloudProvidersList: any = [];
  //selected cloudtype object
  selectedCloudType: any = {
    name: '',
    keyFields: [],
    eskmKeyFields: [],
    configFields: [],
    removeKeyFields: [
      {
        jsonField: '',
        type: '',
        required: false,
        fieldNote: '',
        label: '',
        maxLength: 0,
        min: 0,
        max: 0,
        showOnListing: false,
        editable: false,
        isImportField: false,
        noReviewOnEdit: false,
      },
    ],
    uploadKeyEnabled: true,
    verifyCloudConfigEnabled: true,
    fetchCloudKeyDetails: false,
    fetchESKMKeyDetails: false,
    exportEnabled: false,
  };
  tableKeyFields: any = []; //dynamic table fields list
  dropdownOptionList: any = {}; //list of dropdownOptionList to be searched from
  isReadOnly = false;
  keyAlogSize: string = '';
  algorithmList: any = [];
  showModal: boolean = false;
  
  // Role-based permission flags
  hasViewerPermission = false;
  hasEditorPermission = false;
  hasAdminPermission = false;
  canCreateInstance = false;
  /* exportKey={//Exported KeyDetail popup data object
    header:null,KeyData:new SfKeyExportModel(),showDialog:false,
  };
*/

  @ViewChild('actionTemplate', { static: true })
  actionTemplate!: TemplateRef<any>;

  public tableData: FetchedKey[] = [];
  public tableColumns: TableColumn[] = [];

  private ngUnsubscribe = new Subject<void>();

  private cloudInstanceService = inject(TaasCloudInstanceService);
  private route = inject(ActivatedRoute);
  private _toast = inject(ToastService);
  private userRoleService = inject(UserRoleService);

  uploadKeyLocalized: string = $localize`:Upload Key\:title: Upload to `;
  keyDetailsCloseLocalized: string = $localize`:Manage Cloud Keys\: button text for Close: Close`;

  getLocalizedKeyFieldsLabel(field: KeyFields): string {
    //console.log('field',field);
    return FIELD_KEY_TRANSLATIONS[field.jsonField] || field.label;
  }

  /**
   * Initialize role-based permissions for this component
   * According to the matrix:
   * - Viewer can see keys but not modify them
   * - Editor can upload keys but not delete them
   * - Admin can upload and delete keys
   * - Azure instance creator and org-admin can add new instances
   */
  private initializePermissions(): void {
    // Check if user has Viewer permission or higher
    this.hasViewerPermission = this.userRoleService.hasAnyRole([
      Role.INSTANCE_VIEWER,
      Role.INSTANCE_EDITOR,
      Role.INSTANCE_ADMIN,
      Role.ORG_ADMIN
    ]);
    
    // Check if user has Editor permission or higher
    this.hasEditorPermission = this.userRoleService.hasAnyRole([
      Role.INSTANCE_EDITOR,
      Role.INSTANCE_ADMIN,
      Role.ORG_ADMIN
    ]);
    
    // Check if user has Admin permission
    this.hasAdminPermission = this.userRoleService.hasAnyRole([
      Role.INSTANCE_ADMIN,
      Role.ORG_ADMIN
    ]);
    
    // Check if user can create Azure instances
    this.canCreateInstance = this.userRoleService.hasAnyRole([
      Role.ORG_ADMIN,
      Role.AZURE_INSTANCE_CREATOR
    ]);
    
    // If user doesn't have at least Viewer permission, set as read-only
    this.isReadOnly = !this.hasViewerPermission;
  }

  ngOnInit(): void {
    // Initialize permissions
    this.initializePermissions();
    
    // Set up subscription to user roles changes
    this.userRoleService.userRoles$.subscribe(() => {
      this.initializePermissions();
    });
    
    // Fetch cloud instance id from URL params
    this.selectedCloudInstanceId = Number(
      this.route.snapshot.paramMap.get('id')
    );

    //fetch supported cloud providers and cloud instance
    this.fetchCloudInstance(this.selectedCloudInstanceId);

    this.listenForKeyUpdate();

    this.generateTableColumns();
  }

  generateTableColumns() {
    this.tableColumns = [
      {
        header: $localize`Key Name`,
        valuePropertyName: 'name',
        sortable: true,
        initialSortDirection: 'asc',
      },
      {
        header: $localize`Source`,
        valuePropertyName: 'keySource',
        sortable: true,
        initialSortDirection: 'asc',
      },
      {
        header: $localize`Status`,
        valuePropertyName: 'status',
        sortable: true,
        initialSortDirection: 'asc',
      },
      {
        header: $localize`Enabled`,
        valuePropertyName: 'enabled',
        sortable: true,
        initialSortDirection: 'asc',
        customDisplayValue: (row: any) =>
          row?.keyAttributes?.enabled ? 'Yes' : 'No',
      },
      {
        header: $localize`Key Vault`,
        valuePropertyName: 'keyVault',
        sortable: true,
        initialSortDirection: 'asc',
        customDisplayValue: (row: any) => row?.keyAttributes?.keyVault ?? '',
      },
      {
        header: $localize`Action`,
        valuePropertyName: 'action',
        template: this.actionTemplate,
      },
    ];
  }

  getKeyList() {
    this.isKeysLoaded = false;
    this.cloudInstanceService
      .getCloudKeysData(this.selectedCloudInstanceId)
      .subscribe((res) => {
        if ((res.status === 200 || res.status === 201) && res.body) {
          this.isKeysLoaded = true;
          this.tableData = res?.body?.data;
          this.tableData.map((data: any) => {
            data.keyAttributes = data.keyAttributes
              ? JSON.parse(data.keyAttributes)
              : {};
            data.eskmKeyAttributes = data.eskmKeyAttributes
              ? JSON.parse(data.eskmKeyAttributes)
              : {};
          });
          this.dataSource = new ClientSidePaginatedDataSource(this.tableData);
          return;
        }
        this.handleKeyListErrors(res);
      });
  }

  handleKeyListErrors(res: any) {
    if (res.error && res.error.errorMessage) {
      // known error
      this._toast.error('Error', res.error.errorMessage);
      // this.openSnackBar(res.error.errorMessage, 'errorSnackBar', 'Hide');
    } else if (res.status === 503) {
      this._toast.error(
        'Error',
        'Service unavailable, Please try again after some time.'
      );
      //this.openSnackBar('Service unavailable, Please try again after some time.','errorSnackBar', 'Hide');
    } else {
      // unknown error
      this._toast.error('Error', 'Internal Server error');
      //this.openSnackBar('Internal Server error', 'errorSnackBar', 'Hide');
    }
    this.isKeysLoaded = true;
  }

  rowSelected(event: any) {
    this.onRowSelect(event);
  }

  onRowSelect(event: any) {
    //set keyDetails object on selecting row
    this.keyUri = '';
    this.keyDetails = event;
    this.showModal = true;
    this.eskmKeyFieldsLoaded = false;

    //fetch custom key eskmKeyAttributes
    if (
      (this.selectedCloudType.fetchCloudKeyDetails ||
        this.selectedCloudType.fetchESKMKeyDetails) &&
      event.data.status !== 'Not Uploaded' &&
      !event.data.status.includes('Delete')
    ) {
      //fetch custom attributes
      this.fetchCustomKeyAttributes();
      return;
    }

    setTimeout(() => {
      this.eskmKeyFieldsLoaded = true;
    }, 100);
  }

  listenForKeyUpdate() {
    // Reload keys list in case of update
    this.cloudInstanceService.manageKeysListObservable
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(() => {
        this.getKeyList();
      });
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }

  getDropdownData() {
    //get options of dropdown lists
    if (this.selectedCloudInstance.keyFields)
      this.selectedCloudInstance.keyFields.forEach(
        (data: { jsonField: string; values: any }) => {
          this.selectedCloudType.keyFields.forEach(
            (d: { jsonField: string; values: any }) => {
              if (d.jsonField == data.jsonField) {
                this.dropdownOptionList[data.jsonField] = {
                  ...d,
                  values: data.values,
                };
              }
            }
          );
        }
      );
  }

  openUploadKeyModal() {
    // Check if user has proper permissions to upload keys
    // According to the matrix, only Editor and Admin roles can upload keys
    if (!this.hasEditorPermission) {
      this._toast.warning(
        'Permission Denied',
        'You need Editor or Admin permission to upload keys'
      );
      return;
    }
    
    // Show upload modal
    this.openUploadModal = true;
  }

  setSortedCol(column: unknown) {
    //save sort column data
    if (this.tablePaginator.sortField == column)
      this.tablePaginator.sortOrder = -1 * this.tablePaginator.sortOrder;
    else {
      this.tablePaginator.sortField = column;
      this.tablePaginator.sortOrder = 1;
    }
  }

  onActionChange(event: any, key: any) {
    //perform action on dropdown data
    // if(event.target.value.toLowerCase()=='select')
    //   return;
    // key.action = event.target.value;
    
    // Basic permission checks based on action type before proceeding
    if (event.target.value === 'Delete' && !this.hasAdminPermission) {
      this._toast.warning(
        'Permission Denied',
        'You need Admin permission to delete keys'
      );
      return;
    }
    
    if ((event.target.value === 'Upload' || event.target.value === 'Edit') && !this.hasEditorPermission) {
      this._toast.warning(
        'Permission Denied',
        'You need Editor or Admin permission to modify keys'
      );
      return;
    }
    
    const action = {
      //basic action object
      action: event.target.value.toLowerCase().replace(' ', '-'),
      status: key.status,
      keySource: key.keySource,
    };
    this.selectedUploadEditAction.action = event.target.value;
    this.selectedKey = JSON.parse(JSON.stringify(key));
    if (!event.target.value) return;
    switch (event.target.value) {
      case 'Delete': //delete action
        this.deleteKeyActionHandler(action, key);
        break;
      case 'Upload': //upload action
        this.uploadKeyHandler(event, key);
        break;
      case 'Edit': //edit action
        this.editKeyActionHandler(event, key);
        break;
      // case 'New Version':
      //   this.newVersionActionHandler(action, key);
      //   break;
      // case 'Export':
      //   this.exportKeyActionHandler(action, key);
      //   break;
      default: //other action
        this.defaultActionHandler(action, key);
        break;
    }
  }

  defaultActionHandler(action: any, key: any) {
    // handle default action
    this.isKeysLoaded = false;
    action['keyId'] = key.keyId;
    if (this.selectedCloudType.keyFields)
      action['keyAttributes'] = JSON.stringify(key.keyAttributes);
    if (this.selectedCloudType.eskmKeyFields)
      action['eskmKeyAttributes'] = JSON.stringify(key.eskmKeyAttributes);
    this.cloudInstanceService
      .performKeyAction(this.selectedCloudInstanceId, key, action)
      .subscribe((data) => {
        //perform action request
        if (data.status == 200 && data.body.data) {
          //action successfull
          // key.action = "";
          this.cloudInstanceService.manageKeysChanged();
          this._toast.success('Success', data.body.message);
        } else if (data.error && data.error.message) {
          //known action error
          // key.action = "";
          this.isKeysLoaded = true;
          this._toast.error('Error', data.error.message);
        } else if (data.status == 503) {
          //key.action = "";
          this.isKeysLoaded = true;
          this._toast.error(
            'Error',
            'Service unavailable, Please try again after some time.'
          );
        } else {
          //unknown failure error
          // key.action = "";
          this.isKeysLoaded = true;
          this._toast.error('Error', 'Internal Server error');
        }
      });
  }

  deleteKeyActionHandler(action: any, key: any) {
    // Check if user has Admin permission to delete keys
    // According to the matrix, only Admin roles can delete keys
    if (!this.hasAdminPermission) {
      this._toast.warning(
        'Permission Denied',
        'You need Admin permission to delete keys'
      );
      return;
    }
    
    // handle delete action
    action['actionAttributes'] = {};
    if (this.selectedCloudType.removeKeyFields) {
      this.removeKeyFieldsDeleteHandler(action);
    }
    const eskmKeyName =
      key.keySource.split('(ESKM').length > 0
        ? key.keySource.split('(ESKM')[0]
        : '';
    // show confirmation
    this.keyAssociationMsg = null;
    this.deleteKeyConfirmation.deleteFromCloudCheck.keyStatus = key.status;
    //if key status is uploaded or sync error
    if (
      key.status == 'Uploaded' ||
      (key.status &&
        (key.status.includes('Error') || key.status.includes('Deleted')))
    ) {
      this.uploadedDeleteKeyHandler(action, key, eskmKeyName);
    } else if (key.status == 'Active') {
      //external key delete popup
      this.activeDeleteKeyHandler(action, key, eskmKeyName);
    } else if (key.status == 'Not Uploaded') {
      //if key status is not uploaded
      this.notUploadedDeleteKeyHandler(action, key, eskmKeyName);
    } else if (key.status == '-') {
      this.noStatusDeleteKeyHandler(action, key);
    }
  }

  editKeyActionHandler(event: any, key: any) {
    // Check if user has Editor permission to edit keys
    // According to the matrix, only Editor and Admin roles can edit keys
    if (!this.hasEditorPermission) {
      this._toast.warning(
        'Permission Denied',
        'You need Editor or Admin permission to edit keys'
      );
      return;
    }
    
    // handle edit key action
    //set edit key popup data
    // this.selectedUploadEditAction.header = `Edit key
    //   (${key.keySource.split('(ESKM').length > 1 ? key.keySource.split('(ESKM')[0] : key.name}) on `;
    const keyName =
      key.keySource.split('(ESKM').length > 1
        ? key.keySource.split('(ESKM')[0]
        : key.name;
    this.selectedUploadEditAction.header =
      $localize`:Manage Cloud Keys@@editKeyPrefix:Edit key ` +
      keyName +
      $localize`:Manage Cloud Keys@@editKeySuffix: on `;

    this.selectedUploadEditAction.action = event.target.value;
    this.openReuploadModal = true;
    // setTimeout(() => {
    //   key.action = "";
    //   this.reuploadFormToggle.nativeElement.click();
    // }, 100);
  }

  uploadKeyHandler(event: any, key: any) {
    // Check if user has Editor permission to upload keys
    // According to the matrix, only Editor and Admin roles can upload keys
    if (!this.hasEditorPermission) {
      this._toast.warning(
        'Permission Denied',
        'You need Editor or Admin permission to upload keys'
      );
      return;
    }
    
    // handle upload key action
    //set upload key popup data
    // this.selectedUploadEditAction.header = `Upload key
    //   (${key.keySource.split('(ESKM').length > 1 ? key.keySource.split('(ESKM')[0] : key.name}) to `;

    const keyName =
      key.keySource.split('(ESKM').length > 1
        ? key.keySource.split('(ESKM')[0]
        : key.name;
    this.selectedUploadEditAction.header =
      $localize`:Manage Cloud Keys@@uploadKeyPrefix:Upload key ` +
      keyName +
      $localize`:Manage Cloud Keys@@uploadKeySuffix: to `;

    this.selectedUploadEditAction.action = event.target.value;
    this.openReuploadModal = true;
    // setTimeout(() => {
    //   key.action = "";
    //   this.reuploadFormToggle.nativeElement.click();
    // }, 100);
  }
  removeKeyFieldsDeleteHandler(action: any) {
    // handle removeKeyFields for delete
    action['actionAttributes'] = { removeKeyAttributes: {} };
    this.selectedCloudType.removeKeyFields.forEach(
      (d: {
        label: string;
        jsonField: string;
        type: string;
        required: boolean;
        maxLength: number;
        min: number;
        showOnListing: boolean;
        editable: boolean;
        isImportField: boolean;
        noReviewOnEdit: boolean;
      }) => {
        this.validateRemoveKeyFields[d.jsonField] = { value: true, msg: '' };
        if (d.type == 'numeric') {
          action['actionAttributes']['removeKeyAttributes'][d.jsonField] = d.min
            ? d.min
            : 0;
        } else
          action['actionAttributes']['removeKeyAttributes'][d.jsonField] = '';
      }
    );
  }

  activeDeleteKeyHandler(action: any, key: any, eskmKeyName: string) {
    // handle delete with status active
    this.deleteKeyConfirmation.deleteFromCloudCheck.status = false;
    this.deleteKeyConfirmation.deleteFromCloudCheck.value = false;
    this.deleteKeyConfirmation.deleteFromCloudCheck.msg = '';
    //this.deleteKeyConfirmation.msg = `Are you sure you want to delete key (${eskmKeyName}) from EKMaaS ?`;
    this.deleteKeyConfirmation.msg = $localize`:Manage Cloud Keys-Confirmation message for key deletion|@@deleteActiveKeyConfirmation:Are you sure you want to delete key <span class="no-break">(${eskmKeyName}:INTERPOLATION:)</span> from EKMaaS?`;
    //action['keyId']=key.keyId;
    if (this.selectedCloudType.eskmKeyFields) {
      action['eskmKeyOwner'] = key.eskmKeyAttributes['eskmKeyOwner'];
      //action['eskmKeyAttributes']=JSON.stringify(key.eskmKeyAttributes);
    }
    this.selectedDeleteAction = action;
    // setTimeout(() => { key.action = ""; }, 100);
    this.deleteKeyConfirmation.visible = true;
    //this.deleteConfirmationToggle.nativeElement.click();//show confirmation
  }

  uploadedDeleteKeyHandler(action: any, key: any, eskmKeyName: string) {
    // handle delete with status uploaded
    // make get call and check length of list. If length = 1 then show checkbox
    if (this.selectedCloudInstanceId && eskmKeyName) {
      this.cloudInstanceService
        .getSearchSelectAssociation(null, eskmKeyName) //fetch keys associations
        .subscribe((res) => {
          if ((res.status == 200 || res.status == 201) && res.body.data) {
            //successfull request
            const keyLength = res.body.data.length;
            this.deleteKeyConfirmation.deleteFromCloudCheck.status =
              keyLength == 1;
            this.deleteKeyConfirmation.deleteFromCloudCheck.value = false;

            // this.deleteKeyConfirmation.deleteFromCloudCheck.msg = keyLength == 1 ? "Delete from EKMaaS" : "";
            // this.deleteKeyConfirmation.msg = `Are you sure you want to delete key (${key.name}) from ${this.selectedCloudInstance.instanceName} (${this.selectedCloudType.name}) ?`;

            // For the deleteFromCloudCheck message with conditional
            this.deleteKeyConfirmation.deleteFromCloudCheck.msg =
              keyLength == 1
                ? $localize`:Manage Cloud Keys-Message for delete from EKMaaS option|@@deleteFromEKMaaS:Delete from EKMaaS`
                : '';

            // For the confirmation message with multiple interpolations
            this.deleteKeyConfirmation.msg = $localize`:Manage Cloud Keys-Confirmation message for key deletion|@@deleteKeyConfirmation:Are you sure you want to delete key <span class="no-break">(${key.name}:INTERPOLATION:)</span> from <span class="no-break">${this.selectedCloudInstance.instanceName}:INTERPOLATION_1: (${this.selectedCloudType.name}:INTERPOLATION_2:)</span>?`;

            action['keyId'] = key.keyId;
            if (key.status == 'Uploaded') {
              //set msg and attributes if status is uploaded
              //this.keyAssociationMsg = keyLength > 1 ? `Note: Key will be deleted only from ${this.selectedCloudType.name}.` : null;
              this.keyAssociationMsg =
                keyLength > 1
                  ? $localize`:Manage Cloud Keys-Note on deleted keys@@keyAssociation.deleteNote:Note: Key will be deleted only from ${this.selectedCloudType.name}:INTERPOLATION:`
                  : null;
              action['keyAttributes'] = JSON.stringify(key.keyAttributes);
              action['eskmKeyAttributes'] = JSON.stringify(
                key.eskmKeyAttributes
              );
            }
            //show checkbox on popup if keyAssociation length = 1
            action['actionAttributes']['deleteESKMKey'] =
              keyLength == 1
                ? this.deleteKeyConfirmation.deleteFromCloudCheck.value
                : false;
            this.selectedDeleteAction = action;
            this.deleteKeyConfirmation.visible = true;
            //this.deleteConfirmationToggle.nativeElement.click();//show confirmation
            //key.action = "";
          } else if (res.error && res.error.message) {
            //valid error
            this._toast.error('Error', res.error.message);
          } else if (res.status == 503) {
            this._toast.error(
              'Error',
              'Service unavailable, Please try again after some time.'
            );
          } //other unknown error
          else this._toast.error('Error', 'Internal Server error');
        });
    }
  }

  notUploadedDeleteKeyHandler(action: any, key: any, eskmKeyName: string) {
    // handle delete with status notUploaded
    // hide checkbox
    this.deleteKeyConfirmation.deleteFromCloudCheck.status = false;
    this.deleteKeyConfirmation.deleteFromCloudCheck.value = false;
    this.deleteKeyConfirmation.deleteFromCloudCheck.msg = '';
    this.deleteKeyConfirmation.msg = $localize`:Manage Cloud Keys-Confirmation message for key deletion|@@deleteNotUploadedKeyConfirmation:Are you sure you want to delete key <span class="no-break">(${eskmKeyName}:INTERPOLATION:)</span> from EKMaaS?`;
    //this.deleteKeyConfirmation.msg = `Are you sure you want to delete key (${eskmKeyName}) from EKMaaS ?`;
    this.selectedDeleteAction = action;
    //setTimeout(() => { key.action = ""; }, 100);
    this.deleteKeyConfirmation.visible = true;
    //this.deleteConfirmationToggle.nativeElement.click();//show confirmation
  }

  noStatusDeleteKeyHandler(action: any, key: any) {
    // handle delete with status -
    action['keyId'] = key.keyId;
    if (this.selectedCloudType.keyFields)
      action['keyAttributes'] = JSON.stringify(key.keyAttributes);
    if (this.selectedCloudType.eskmKeyFields)
      action['eskmKeyAttributes'] = JSON.stringify(key.eskmKeyAttributes);
    // hide checkbox
    this.deleteKeyConfirmation.deleteFromCloudCheck.status = false;
    this.deleteKeyConfirmation.deleteFromCloudCheck.value = false;
    this.deleteKeyConfirmation.deleteFromCloudCheck.msg = '';
    //this.deleteKeyConfirmation.msg = `Are you sure you want to delete key (${!key.name && this.selectedCloudType.name.toLowerCase().includes('salesforce-byok') ? 'Version - '+key.keyAttributes['version'] : key.name }) from ${this.selectedCloudInstance.instanceName} (${this.selectedCloudType.name}) ?`;
    this.deleteKeyConfirmation.msg = $localize`:Manage Cloud Keys-Confirmation message for key deletion|@@deleteNoStatusKeyConfirmation:Are you sure you want to delete key <span class="no-break">(${
      !key.name &&
      this.selectedCloudType.name.toLowerCase().includes('salesforce-byok')
        ? 'Version - ' + key.keyAttributes['version']
        : key.name
    }:INTERPOLATION:)</span> from <span class="no-break">${
      this.selectedCloudInstance.instanceName
    }:INTERPOLATION_1: (${this.selectedCloudType.name}:INTERPOLATION_2:)</span>?`;

    this.selectedDeleteAction = action;
    //setTimeout(() => { key.action = ""; }, 100);
    this.deleteKeyConfirmation.visible = true;
    // this.deleteConfirmationToggle.nativeElement.click();//show confirmation
  }

  onDeleteESKMCheckboxChange() {
    //set action attributes on changing delete popup checkbox
    this.selectedDeleteAction['actionAttributes']['deleteESKMKey'] =
      this.deleteKeyConfirmation.deleteFromCloudCheck.value;
  }

  onRemoveFieldUpdate(event: any, field: any) {
    //handle dynamic removeFiled update
    if (field.type == 'numeric') {
      this.selectedDeleteAction['actionAttributes']['removeKeyAttributes'][
        field.jsonField
      ] = Number(event.event.target.value);
    } else
      this.selectedDeleteAction['actionAttributes']['removeKeyAttributes'][
        field.jsonField
      ] = event.event.target.value;
    this.validateRemoveKey();
  }

  onDelete(onForceDelete = false) {
    //delete key operation
    this.deleteKeyBtn = false;
    let action = JSON.parse(JSON.stringify(this.selectedDeleteAction));
    if (onForceDelete) {
      action = {
        action: 'delete',
        status: 'Not Uploaded',
        actionAttributes: {
          forceDelete: true,
        },
      };
      this.deleteKeyConfirmation.onForceDelete = false;
    } else {
      if (this.selectedCloudType.removeKeyFields) {
        action['actionAttributes']['removeKeyAttributes'] = JSON.stringify(
          action['actionAttributes']['removeKeyAttributes']
        );
      }
    }
    this.isKeysLoaded = false;
    //execute delete req
    this.cloudInstanceService
      .performKeyAction(
        this.selectedCloudInstanceId,
        this.selectedKey,
        action,
        onForceDelete
      )
      .subscribe((data) => {
        if (data.status == 200 && data.body) {
          //successful delete
          this.cloudInstanceService.manageKeysChanged();
          this.deleteKeyConfirmation.visible = false;
          this._toast.success('Success', data.body.message);
        } else if (data.error && data.error.message) {
          //known error
          this.isKeysLoaded = true;
          if (data.error.message.includes('success')) {
            this.cloudInstanceService.manageKeysChanged();
          }
          if (
            this.selectedDeleteAction['status'] &&
            this.selectedDeleteAction['status'] == 'Not Uploaded' &&
            data.error.message.includes('Authentication failed')
          ) {
            const eskmKeyName =
              this.selectedKey.keySource.split('(ESKM').length > 0
                ? this.selectedKey.keySource.split('(ESKM')[0]
                : '';
            //show popup
            setTimeout(() => {
              this.deleteKeyConfirmation.deleteFromCloudCheck.status = false;
              this.deleteKeyConfirmation.deleteFromCloudCheck.value = false;
              this.deleteKeyConfirmation.deleteFromCloudCheck.msg = '';
              this.deleteKeyConfirmation.onForceDelete = true;
              this.deleteKeyConfirmation.msg = `Do you want to remove the key (${eskmKeyName}) from listing?`;
              this.deleteKeyConfirmation.visible = true;
              // this.deleteConfirmationToggle.nativeElement.click();//show confirmation
            }, 1000);
          }

          this._toast.error('Error', data.error.message);
          this.deleteKeyConfirmation.visible = false;
        } else if (data.status == 503) {
          this.isKeysLoaded = true;
          this._toast.error(
            'Error',
            'Service unavailable, Please try again after some time.'
          );
        } else {
          //unknown error
          this.isKeysLoaded = true;
          this._toast.error('Error', 'Internal Server error');
        }
        this.deleteKeyBtn = true;
      });
  }

  onCancelDeleteKeyConfirmation() {
    this.cloudInstanceService.manageKeysChanged();
    this.deleteKeyConfirmation.onForceDelete = false;
    this.deleteKeyConfirmation.visible = false;
  }

  onCancelModal() {
    //hide upload modal on cancel
    this.cloudInstanceService.manageKeysChanged();
    this.openUploadModal = false;
  }

  onCancelReuploadModal() {
    //close reupload modal
    this.cloudInstanceService.manageKeysChanged();
    this.openReuploadModal = false;
    this.selectedUploadEditAction = { header: '', action: '' };
  }

  paginate(event: any): void {
    this.tablePaginator.first = event.first;
    this.tablePaginator.tableSetRows = event.rows;
    //console.log('paginate',event);
  }

  // openSnackBar(message: string, type: string, action: string) {//display error
  //   this._snackBar.open(message, action, {
  //     duration: 6000,
  //     panelClass: [type]
  //   });
  // }

  closeModal() {
    this.showModal = false;
    this.selectedKeyRow = {};
  }

  convertTime(value: any) {
    //convert time from utc to local timezone string
    return new Date(value).toLocaleString().replace(':00 ', ' ');
  }

  fetchCustomKeyAttributes() {
    // fetch CustomKeyAttributes for a given key
    this.cloudInstanceService
      .getCustomKeyAttributes(
        this.selectedCloudInstanceId,
        this.keyDetails,
        this.selectedCloudType.fetchCloudKeyDetails,
        this.selectedCloudType.fetchESKMKeyDetails
      )
      .subscribe((res) => {
        if ((res.status == 200 || res.status == 201) && res.body) {
          this.keyAttributesResponseHandler(res);
        } else if (res.error && res.error.errorMessage) {
          this._toast.error('Error', res.error.errorMessage);
        } else if (res.status == 503) {
          this._toast.error(
            'Error',
            'Service unavailable, Please try again after some time.'
          );
        } else {
          this._toast.error('Error', 'Internal Server error');
        }
      });
  }

  keyAttributesResponseHandler(res: any) {
    // handle getCustomKeyAttributes response
    const eskmKeyAttributes: any = {};
    let keyAttributes = {},
      attr: any = {};
    const newEskmKeyAttributes: any[] = [];
    let keyAttrObj: any = {};
    if (res.body.eskmKeyAttributes) {
      attr = JSON.parse(res.body.eskmKeyAttributes);
      this.eskmKeyAttributes = [];
      this.selectedCloudType.eskmKeyFields.forEach((field: any) => {
        if (field.commonLabel)
          newEskmKeyAttributes.push(JSON.parse(JSON.stringify(field)));
        if (field.type == 'grid-view') {
          this.cloudInstanceService
            .getGridListValues(this.selectedCloudInstance.id, this.keyDetails)
            .subscribe((res) => {
              this.eskmKeyFieldsLoaded = false;
              this.gridListResponseHandler(res, field.jsonField);
            });
        }
        if (
          field.jsonField == 'salesForceCAs' &&
          field.type == 'select' &&
          !this.selectedCloudType.name.toLowerCase().includes('market-byok')
        ) {
          const val = attr[field.label];
          eskmKeyAttributes[field.jsonField] = val.split('-', 1);
        } else {
          eskmKeyAttributes[field.jsonField] = attr[field.label];
        }
      });
      keyAttrObj = this.groupBy(newEskmKeyAttributes, (val) => val.commonLabel);
      this.eskmKeyAttributes = Object.keys(keyAttrObj).map(
        (key) => keyAttrObj[key]
      );
      //console.log('this.eskmKeyAttributes',this.eskmKeyAttributes);
      eskmKeyAttributes['eskmKeyOwner'] = res.body.eskmKeyOwner;
      this.keyDetails.eskmKeyAttributes = eskmKeyAttributes;
      this.eskmKeyFieldsLoaded = true;
    }
    if (res.body.keyAttributes) {
      keyAttributes = JSON.parse(res.body.keyAttributes);
      this.keyDetails.keyAttributes = keyAttributes;
      this.eskmKeyFieldsLoaded = true;
      //commenting for eslint, recheck for exception cases when using this method in future.
      // try {
      //   keyAttributes = JSON.parse(res.body.keyAttributes);
      //   this.keyDetails.keyAttributes = keyAttributes;
      //   this.eskmKeyFieldsLoaded = true;
      // } catch (e) { }
    }
  }

  gridListResponseHandler(res: any, field: any) {
    //handle get gridList Response
    if (res.status == 200 && res.body) {
      this.keyDetails.eskmKeyAttributes[field] = res.body.Versions;
      setTimeout(() => {
        this.eskmKeyFieldsLoaded = true;
      }, 100);
    } else if (res.error && res.error.errorMessage) {
      this._toast.error('Error', res.error.errorMessage);
      this.eskmKeyFieldsLoaded = true;
    } else if (res.status == 503) {
      this._toast.error(
        'Error',
        'Service unavailable, Please try again after some time.'
      );
      this.eskmKeyFieldsLoaded = true;
    } else {
      this._toast.error('Error', 'Internal Server error');
      this.eskmKeyFieldsLoaded = true;
    }
  }

  //fetch selected cloud instance using id
  fetchCloudInstance(selectedCloudInstanceId: number) {
    this.cloudInstanceService
      .getCloudInstance(selectedCloudInstanceId)
      .subscribe((res) => {
        if ((res.status === 200 || res.status === 201) && res.body) {
          this.fetchCloudProviders();
          this.selectedCloudInstance = res.body.data;
          this.getKeyList();
        } else if (res.error && res.error.errorMessage) {
          this._toast.error('Error', res.error.errorMessage);
        } else if (res.status == 503) {
          this._toast.error(
            'Error',
            'Service unavailable, Please try again after some time.'
          );
        } else {
          this._toast.error('Error', 'Internal Server error');
        }
      });
  }

  //group elements based on common element
  groupBy(arr: any[], fn: (item: any) => any) {
    return arr.reduce((prev, curr) => {
      const groupKey = fn(curr);
      const group = prev[groupKey] || [];
      group.push(curr);
      return { ...prev, [groupKey]: group };
    }, {});
  }

  fetchCloudProviders() {
    //fetch list of cloud providers supported
    this.cloudInstanceService.getSupportedCloudProviders().subscribe((res) => {
      if (
        (res.status == 200 || res.status == 201) &&
        res.body &&
        res.body.data.cloudProviders
      ) {
        //successful
        this.supportedCloudProvidersList = res.body.data.cloudProviders;
        this.supportedCloudProvidersList.forEach(
          (data: {
            name: string;
            keyFields: [];
            eskmKeyFields: [];
            configFields: [];
            removeKeyFields: [];
            uploadKeyEnabled: boolean;
            verifyCloudConfigEnabled: boolean;
            fetchCloudKeyDetails: boolean;
            fetchESKMKeyDetails: boolean;
            exportEnabled: boolean;
          }) => {
            if (data.name == this.selectedCloudInstance.cloudType) {
              this.selectedCloudType = data; //set applicable cloud provider
              //console.log('this.selectedCloudType',this.selectedCloudType);
              if (this.selectedCloudType.keyFields) {
                this.tableKeyFields = [];
                this.selectedCloudType.keyFields.forEach(
                  (d: {
                    label: string;
                    jsonField: string;
                    type: string;
                    required: boolean;
                    maxLength: number;
                    showOnListing: boolean;
                    editable: boolean;
                    isImportField: boolean;
                    noReviewOnEdit: boolean;
                  }) => {
                    //create and set dynamic fields in keyDetails object
                    if (d.showOnListing) this.tableKeyFields.push(d);
                    //console.log('this.selectedCloudType.keyFields-this.tableKeyFields',this.tableKeyFields);
                    //this.keyDetails.keyAttributes[d.jsonField]="";
                  }
                );
              }
              if (this.selectedCloudType.eskmKeyFields) {
                this.tableKeyFields = [];
                this.selectedCloudType.eskmKeyFields.forEach(
                  (d: {
                    jsonField: string | number;
                    type: string;
                    showOnListing: string;
                    commonLabel: string;
                  }) => {
                    if (d.showOnListing) this.tableKeyFields.push(d);
                    //console.log('this.selectedCloudType.eskmKeyFields-this.tableKeyFields',this.tableKeyFields);
                    //this.keyDetails.eskmKeyAttributes[d.jsonField]="";
                  }
                );
              }
              if (this.selectedCloudType.exportEnabled) {
                this.actionList.push({
                  action: 'Export',
                  showOnNotUpload: false,
                  requiredPermission: 'viewer'  // Export should be available to all users who can view keys
                });
              }
            }
          }
        );
      } else if (res.error && res.error.errorMessage) {
        //known error
        this._toast.error('Error', res.error.errorMessage);
      } else if (res.status == 503) {
        this._toast.error(
          'Error',
          'Service unavailable, Please try again after some time.'
        );
      } else {
        //unknown error
        this._toast.error('Error', 'Internal Server error');
      }
    });
  }
  validateRemoveKey() {
    // validate Remove Key fields
    let result = true;
    this.selectedCloudType.removeKeyFields.forEach(
      (field: {
        label: string;
        jsonField: string;
        type: string;
        required: boolean;
        maxLength: number;
        showOnListing: boolean;
        editable: boolean;
        isImportField: boolean;
        noReviewOnEdit: boolean;
        fieldNote: string;
        max: number;
        min: number;
      }) => {
        if (
          this.selectedDeleteAction['actionAttributes']['removeKeyAttributes'][
            field.jsonField
          ].toString() &&
          !this.validateNumericInput(field)
        ) {
          result = false;
        } else if (
          field.required &&
          !this.selectedDeleteAction['actionAttributes']['removeKeyAttributes'][
            field.jsonField
          ]
        ) {
          this.validateRemoveKeyFields[field.jsonField] = {
            value: false,
            msg: 'Field required',
          };
          result = false;
        } else
          this.validateRemoveKeyFields[field.jsonField] = {
            value: true,
            msg: '',
          };
      }
    );
    this.deleteKeyBtn = result;
  }

  validateNumericInput(field: {
    label: string;
    jsonField: string;
    type: string;
    required: boolean;
    maxLength: number;
    showOnListing: boolean;
    editable: boolean;
    isImportField: boolean;
    noReviewOnEdit: boolean;
    max: number;
    min: number;
  }) {
    //validate Numeric field type
    if (
      field.type == 'numeric' &&
      (this.selectedDeleteAction['actionAttributes']['removeKeyAttributes'][
        field.jsonField
      ] > field.max ||
        this.selectedDeleteAction['actionAttributes']['removeKeyAttributes'][
          field.jsonField
        ] < field.min)
    ) {
      this.validateRemoveKeyFields[field.jsonField] = {
        value: false,
        msg: 'Field value must be between ' + field.min + ' to ' + field.max,
      };
      return false;
    } else {
      this.validateRemoveKeyFields[field.jsonField] = { value: true, msg: '' };
      return true;
    }
  }

  getKeyDetailsTitle(): string {
    if (
      this.keyDetails &&
      this.keyDetails.keySource &&
      (!this.keyDetails.name || this.keyDetails.name === '-')
    ) {
      return this.keyDetails.keySource.replace('(ESKM)', '');
    } else if (
      this.keyDetails &&
      this.keyDetails.name &&
      this.keyDetails.name !== '-'
    ) {
      return this.keyDetails.name;
    } else {
      return '';
    }
  }
}
