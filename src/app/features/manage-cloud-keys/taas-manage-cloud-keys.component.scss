.divContainer {
  //padding: 10px 5px 10px 5px;
  padding: 30px 15px 10px 5px;
}

.btn {
  border-radius: 5px;
  border: none;
  padding: 5px 10px 5px 10px;
}

.row-padding {
  display: flex;
  justify-content: space-between;
  padding: 5px 0px 10px 0px;

  &.row-last {
    margin-top: 12px;
  }
}

.labelHeader {
  font-weight: 600;
}

.alignCenter {
  //text-align: center!important;
  padding: 0.5em;
  font-size: small;
}

.mat-icon {
  vertical-align: middle;
  margin: auto;
  line-height: 0.95;
}

.closeIcon {
  float: right;
}

.iconFont {
  font-size: 20px;
  cursor: pointer;
}

.cloudBtn {
  color: #fff;
  background-color: #0068b4;
  border-color: black;
  float: right;
}

.createKeybtn {
  padding-top: 20px;
}

.colWidth {
  width: 250px;
  padding-top: 10px;
  padding-bottom: 10px;
}

.largeCombo {
  width: 100%;
  height: 27px;
  max-width: 120px;
}

.keyVaultInput {
  float: right;
  min-width: 121px;
  height: 27px;
}

.noConfig {
  width: 100%;
  /* Set the width to 100% */
  text-align: center;
  padding-top: 50px;
  font-size: 12px;
  color: #666666;
}

div.disabled {
  pointer-events: none;
  cursor: default;
}

.rowIcon {
  width: 15px;
  height: 15px;
}

.rowIconBiger {
  width: 20px;
  height: 20px;
  margin-right: 0px;
}

.disabled {
  pointer-events: none;
  cursor: default;
}

#keyDetailsModal .mat-icon {
  font-size: 20px;
}

#deleteFromCloudCheck {
  border-radius: 0.25em;
  width: 1.3em;
  height: 1.3em;
}

.modal-header {
  font-size: large;
  background-color: #0068b4;
}

.largeInput,
.largeInput:focus-visible {
  width: 100%;
  height: 30px;
  border-top: none;
  border-left: none;
  border-right: none;
  border-bottom: 1px solid #648787;
  color: #212529;
  cursor: text;
}

.fontSmall {
  font-size: small;
  font-style: italic;
}

.keyDetailsHeader {
  max-width: 90%;
  overflow-wrap: anywhere;
  float: left;
}

.fa-angle-double-left {
  color: #007593;
  font-size: 14px;
  cursor: pointer;
}

.sublabel {
  color: #666;
}

.largeTextArea,
.largeTextArea:focus-visible {
  width: 100%;
  border-top: none;
  border-left: none;
  border-right: none;
  border-bottom: 1px solid #648787;
  color: #212529;
  cursor: text;
  font-size: inherit;
}

.custom-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: block;
}

.modal-footer {
  display: flex;
  flex-direction: row-reverse;
  margin-top: 20px;
}

.delete-modal-content {
  font-size: 14px;

  ::ng-deep span.no-break {
    word-break: keep-all;
  }
}