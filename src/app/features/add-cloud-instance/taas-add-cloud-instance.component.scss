.largeInput,.largeInput:focus-visible{
    width:100%;
    height: 30px;
    border-top: none;
    border-left: none;
    border-right: none;
    border-bottom: 1px solid #648787;
    color: #212529;
    cursor: text;
}
.rowIcon{
    width:15px;
    height: 15px;
}
.rowIconBig{
    width:20px;
    height: 20px;
    float: right;
}
.iconFont{
    font-size:20px;
    cursor: pointer;
}
.helpFont{
    font-size:22px;
    cursor: pointer;
}
.btnPadding{
    margin-bottom: 4px;
    margin-left: 2px;
}
label{
    color: #666;
    font-size: 12px;
    margin-bottom: 0px;
}
.verifyBtn {
    color: #fff;
    background-color: green!important;
    border-color: green!important;
}
.disabled {
    pointer-events: none;
    cursor: default;
}

// .custom-modal-overlay {
//     position:fixed;
//     top: 0;
//     left: 0;
//     width: 100%;
//     height: 100%;
//     background-color: rgba(0, 0, 0, 0.5);
//     display: block;
//   }




.custom-modal-overlay{
    position: fixed; /* Ensure the modal overlays the entire page */
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5); /* Overlay with some transparency */
    display: flex;
    justify-content: center;
    align-items: center;
  }


  .modal-content {
    background-color: white;
    //border-radius: 8px;
    //border-radius: 8px;
    //padding: 20px;
    max-width: 500px;
    width: 100%;
    max-height: 80vh;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    overflow: hidden;
  }


  .modal-header {
    //padding: 10px;
    border-bottom: 1px solid #ddd;
  }


  .modal-body-wrapper {
    overflow-y: auto;
    max-height: 60vh;
    //padding-right: 10px;
    overflow-y: auto;
    max-height: 60vh;
    //padding-right: 10px;
  }




  .modal-body-wrapper::-webkit-scrollbar {
    width: 8px;
    width: 8px;
  }


  .modal-body-wrapper::-webkit-scrollbar-thumb {
    border-radius: 4px;
    border-radius: 4px;
  }


  .modal-body-wrapper::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    border-radius: 4px;
    background-color: #f1f1f1;
    border-radius: 4px;
  }


  .modal-body-wrapper {
    scrollbar-width: thin;
    scrollbar-width: thin;
  }

  .add-cloud-instance-container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 400px;
    height: 100%;

    .add-cloud-instance-form {
      flex-grow: 1;
    }

    .add-cloud-instance-footer {
      display: flex;
      flex-direction: row-reverse;
      gap: 5px;
      margin-top: 30px;
    }
  }

