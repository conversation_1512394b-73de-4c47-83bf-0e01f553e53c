<!-- Instance Name -->
<div class="add-cloud-instance-container">
  <div class="add-cloud-instance-form">
    <div>
      <div>
        <label
          for="cloudInstanceName"
          i18n="AddCloudInstance: label field Instance Name"
          >* Instance Name</label
        >
      </div>
    </div>
    <div>
      <div>
        <input
          class="largeInput"
          [(ngModel)]="newCloudInstance.instanceName"
          id="addCloudInstanceName"
          autocomplete="off"
        />
      </div>
      @if (validateNewCloudInstance.instanceName.msg.length > 0) {
      <div>
        <mat-error>{{ validateNewCloudInstance.instanceName.msg }}</mat-error>
      </div>
      }
    </div>
    <!-- Cloud Type -->
    <div>
      <div>
        <label for="cloudType" i18n="AddCloudInstance: label field Cloud Type"
          >* Cloud Type</label
        >
      </div>
    </div>
    <div>
      <div>
        <select
          class="largeInput"
          id="editCloudType"
          (change)="onCloudTypeChange($event)"
          [disabled]="instanceVerified"
        >
          <option
            value=""
            selected="selected"
            i18n="AddCloudInstance: option field Select"
          >
            Select
          </option>
          @for (cloud of supportedCloudProvidersList; track cloud.name) {
          <option [value]="cloud.name">
            {{ cloud.name | appCloudTypeName }}
          </option>
          }
        </select>
      </div>
      @if (validateNewCloudInstance.cloudType.msg.length > 0) {
      <div>
        <mat-error>{{ validateNewCloudInstance.cloudType.msg }}</mat-error>
      </div>
      }
    </div>
    <!-- Dynamic Fields -->
    @if (selectedCloudType.configFields && selectedCloudType.configFields.length
    > 0) { @for (field of selectedCloudType.configFields; track field.jsonField)
    {
    <div>
      @if (field.type !== 'value-array' && field.type !== 'multi-select' &&
      field.type !== 'yes-no') {
      <div>
        <label for="field.jsonField">
          @if (field.required) {<span>*</span>}
          {{ getLocalizedLabel(field) }}
        </label>
      </div>
      <div [ngClass]="{ disabled: instanceVerified }">
        <app-taas-shared-input
          [inputField]="field"
          [disabled]="instanceVerified"
          [value]="newCloudInstance.configFields[field.jsonField]"
          id="{{ 'addCloudInsConf' + field.jsonField }}"
          (update)="onSharedFieldUpdate($event, field)"
        >
        </app-taas-shared-input>
      </div>
      } @if (field.type === 'value-array') {
      <div>
        <app-taas-shared-input
          [inputField]="field"
          [value]="newCloudInstance.configFields[field.jsonField]"
          id="{{ 'addCloudInsConf' + field.jsonField }}"
          [inputLabel]="field.label"
          (update)="onSharedFieldUpdate($event, field)"
          [disabled]="true"
        >
        </app-taas-shared-input>
      </div>
      } @if (field.type === 'multi-select') {
      <div>
        <app-taas-shared-input
          [inputField]="field"
          [value]="newCloudInstance.configFields[field.jsonField]"
          id="{{ 'addCloudInsConf' + field.jsonField }}"
          [inputLabel]="field.label"
          [maxLength]="field.maxSelect"
          (update)="onSharedFieldUpdate($event, field)"
        >
        </app-taas-shared-input>
      </div>
      } @if (field.type === 'yes-no') {
      <div>
        <div>
          <div>
            <label for="{{ 'addCloudInsConf' + field.jsonField }}">
              {{ getLocalizedLabel(field) }}
            </label>
          </div>
          <div id="{{ 'addCloudInsConf' + field.jsonField }}">
            <label class="toggle">
              <app-taas-shared-input
                [inputField]="field"
                [value]="
                  newCloudInstance.configFields[field.jsonField] === 'yes'
                    ? true
                    : false
                "
                id="{{ 'addCloudInsConf' + field.jsonField }}"
                [inputLabel]="field.label"
                [maxLength]="field.maxSelect"
                (update)="onSharedFieldUpdate($event, field)"
              >
              </app-taas-shared-input>
            </label>
          </div>
        </div>
      </div>
      } @if (validateNewCloudInstance[field.jsonField].msg.length > 0) {
      <div>
        <mat-error>{{
          validateNewCloudInstance[field.jsonField].msg
        }}</mat-error>
      </div>
      }
    </div>
    } }

    <!-- Error list on submission -->
    <div class="row">
      @if (!addInstanceError.value && addInstanceError.msg.length > 0) {
      <div>
        @for (error of addInstanceError.msg; track error) {
        <mat-error class="{{ addInstanceError.class }}">{{
          error.trim()
        }}</mat-error>
        }
      </div>
      }
    </div>
  </div>
  <!-- Add and cancel btn -->
  <div class="add-cloud-instance-footer">
    <lib-taas-button
      btnId="confirmButtonAddCloudInstance"
      buttonType="highlight"
      (click)="onAddCloudInstance()"
      [disabled]="!addCloudBtn"
      >{{ getButtonText() }}
    </lib-taas-button>

    <lib-taas-button
      #addInstanceCancel
      btnId="cancelButtonAddCloudInstance"
      buttonType="primary"
      (click)="triggerCloseModal()"
      i18n="AddCloudInstance: Cancel"
      >{{ cancelLocalized }}
    </lib-taas-button>
  </div>
</div>

