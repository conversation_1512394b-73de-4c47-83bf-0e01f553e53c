import {
  Component,
  OnInit,
  Output,
  EventEmitter,
  ViewChild,
  ElementRef,
  inject,
  Input,
} from '@angular/core';
import { TaasCloudInstanceService } from '../../core/services/cloud/taas-cloud-instance.service';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule } from '@angular/forms';
import { FormatCloudTypeName } from '../../core/pipes/formatCloudTypeName.pipe';
import { TaasSharedInputComponent } from '../../shared/components/taas-shared-input/taas-shared-input.component';
import { ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import {
  TaasModalDialogueComponent,
  ToastService,
} from 'utimaco-common-ui-angular';
import {
  LABEL_TRANSLATIONS,
  VALIDATION_MESSAGES,
} from '../../shared/components/taas-shared-localization';
import { LabelFields } from '../../shared/components/models/label-fields';
import { TaasButtonComponent } from 'utimaco-common-ui-angular';
@Component({
  selector: 'app-taas-add-cloud-instance',
  standalone: true,
  imports: [
    MatIconModule,
    CommonModule,
    MatFormFieldModule,
    FormsModule,
    FormatCloudTypeName,
    TaasSharedInputComponent,
    MatInputModule,
    ReactiveFormsModule,
    TaasButtonComponent,
  ],

  //imports: [MatIcon,MatInput,MatIconModule,CommonModule,FormatCloudTypeName,TaasSharedInputComponent,MatFormField, MatFormFieldModule,MatLabel ],
  templateUrl: './taas-add-cloud-instance.component.html',
  styleUrl: './taas-add-cloud-instance.component.scss',
})
export class TaasAddCloudInstanceComponent implements OnInit {
  @Input() modalReference!: TaasModalDialogueComponent;
  @Output() modalCancelRequest: EventEmitter<any> = new EventEmitter();
  @ViewChild('addInstanceCancel') addInstanceCancel!: ElementRef;
  //initalize new instance object
  newCloudInstance: any = {
    instanceName: '',
    cloudType: '',
    configFields: {},
    createdDate: '',
    lastUpdated: '',
  };
  //verify/add btn
  addBtn = {
    class: 'verifyBtn',
    //text: $localize`:@@addCloudInstanceButton.text:Verify`
    text: 'Verify',
  };
  //field validation error object
  validateNewCloudInstance: any = {
    instanceName: { value: false, msg: '' },
    cloudType: { value: false, msg: '' },
  };
  //main generalerror object
  addInstanceError: any = { value: false, msg: [], class: '' };
  supportedCloudProvidersList: any = [];

  //comment below for actual backend calls an uncomment above
  //   supportedCloudProvidersList: {name: string,keyFields:{},configFields:{},verifyCloudConfigEnabled:any,fetchCloudKeyDetails:any,fetchESKMKeyDetails:any,uploadKeyEnabled:any,exportEnabled:any}[] =
  //     [
  //       {
  //         "name": "Azure",
  //         "keyFields": [
  //             {
  //                 "label": "Enabled",
  //                 "jsonField": "enabled",
  //                 "type": "yes-no",
  //                 "required": false,
  //                 "maxLength": 3,
  //                 "showOnListing": true,
  //                 "editable": true,
  //                 "isImportField": true
  //             },
  //             {
  //                 "label": "Key Vault",
  //                 "jsonField": "keyVault",
  //                 "type": "select",
  //                 "required": true,
  //                 "maxLength": 100,
  //                 "showOnListing": true,
  //                 "editable": false,
  //                 "isImportField": true
  //             },
  //             {
  //                 "label": "Activation Date",
  //                 "jsonField": "activationDate",
  //                 "type": "date-time-picker",
  //                 "required": false,
  //                 "maxLength": 200,
  //                 "showOnListing": false,
  //                 "editable": true,
  //                 "isImportField": true
  //             },
  //             {
  //                 "label": "Expiration Date",
  //                 "jsonField": "expirationDate",
  //                 "type": "date-time-picker",
  //                 "required": false,
  //                 "maxLength": 200,
  //                 "showOnListing": false,
  //                 "editable": true,
  //                 "isImportField": true
  //             },
  //             {
  //                 "label": "Tags",
  //                 "jsonField": "tags",
  //                 "type": "name-value-array",
  //                 "required": false,
  //                 "maxLength": 200,
  //                 "showOnListing": false,
  //                 "editable": true,
  //                 "isImportField": true
  //             }
  //         ],
  //         "configFields": [
  //             {
  //                 "label": "Tenant ID",
  //                 "jsonField": "tenantID",
  //                 "type": "text-field",
  //                 "required": true,
  //                 "maxLength": 50,
  //                 "isImportField": true
  //             },
  //             {
  //                 "label": "Client ID",
  //                 "jsonField": "clientID",
  //                 "type": "text-field",
  //                 "required": true,
  //                 "maxLength": 50,
  //                 "isImportField": true
  //             },
  //             {
  //                 "label": "Client Secret",
  //                 "jsonField": "clientSecret",
  //                 "type": "password-field",
  //                 "required": true,
  //                 "maxLength": 50,
  //                 "isImportField": true
  //             },
  //             {
  //                 "label": "Key Vaults",
  //                 "jsonField": "keyVaults",
  //                 "type": "text-field",
  //                 "hint": "Specify comma separated values for multiple key vault",
  //                 "required": true,
  //                 "maxLength": 200,
  //                 "isImportField": true
  //             }
  //         ],
  //         "verifyCloudConfigEnabled": false,
  //         "fetchCloudKeyDetails": false,
  //         "fetchESKMKeyDetails": false,
  //         "uploadKeyEnabled": true,
  //         "exportEnabled": false
  //     },
  // ];
  //selected cloud type object with dynamic fields
  selectedCloudType: any = {
    name: '',
    keyFields: [],
    configFields: [],
    uploadKeyEnabled: true,
    verifyCloudConfigEnabled: true,
  };
  modifiedValueArray: any = {};
  addCloudInstanceEnabled = false;
  //enable disable add btn
  addCloudBtn = true;
  //enable disable verify btn
  instanceVerified = false;
  cloudInstanceService: any;
  toastService: ToastService = inject(ToastService);
  cancelLocalized: string = $localize`:AddCloudInstance\:Cancel: Cancel`;

  getButtonText() {
    if (this.addBtn.text === 'Add') {
      //console.log(this.addBtn.text);
      return $localize`:AddCloudInstance-Add@@addBtnText.add:Add`;
    } else {
      //console.log(this.addBtn.text);
      return $localize`:AddCloudInstance-Verify@@addBtnText.verify:Verify`;
    }
  }

  getLocalizedLabel(field: LabelFields): string {
    return LABEL_TRANSLATIONS[field.jsonField] || field.label;
  }
  constructor(
    private taasCloudInstanceService: TaasCloudInstanceService,
    public dialog: MatDialog
  ) { }

  ngOnInit(): void {
    //get list of supported cloud types
    //console.log('providers ngint');
    const errorTitle = $localize`Error while trying to fetch supported cloud types`;
    this.taasCloudInstanceService
      .getSupportedCloudProviders()
      .subscribe((res) => {
        if (
          (res.status == 200 || res.status == 201) &&
          res.body &&
          res.body.data.cloudProviders
        ) {
          //console.log('supportedCloudProvidersList',res.body.data.cloudProviders);
          this.supportedCloudProvidersList = res.body.data.cloudProviders;
        } else if (res.error && res.error.message) {
          this.toastService.error(errorTitle, res.error.message);
        } else if (res.status == 503) {
          this.toastService.error(
            errorTitle,
            'Service unavailable, Please try again after some time.'
          );
        } else {
          this.toastService.error(errorTitle, 'Internal Server error');
        }
      });
  }

  onCloudTypeChange(event: any) {
    //set value on cloud dd change
    let isEmpty = true;
    //console.log('onCloudTypeChange',event.target.value)
    this.newCloudInstance.cloudType = event.target.value;
    this.validateNewCloudInstance = {
      instanceName: { value: false, msg: '' },
      cloudType: { value: false, msg: '' },
    };
    this.newCloudInstance.configFields = {};
    //reset cloud fields
    this.supportedCloudProvidersList.forEach((data: { name: any }) => {
      if (data.name == event.target.value) {
        //console.log('onCloudTypeChange-data.name',data.name)
        isEmpty = false;
        this.resetConfigFields(data);
        this.addCloudInstanceEnabled = false;
        this.selectedCloudType = data;
        //console.log('this.selectedCloudType',data);
        this.addBtn.class = this.selectedCloudType.verifyCloudConfigEnabled
          ? 'verifyBtn'
          : 'cloudBtn';
        //console.log('this.addBtn.class',this.addBtn.class);
        this.addBtn.text = this.selectedCloudType.verifyCloudConfigEnabled
          ? 'Verify'
          : 'Add';
        //console.log('this.addBtn.text',this.addBtn.text);
      }
    });
    if (isEmpty) {
      this.newCloudInstance.configFields = [];
      this.selectedCloudType = {
        name: '',
        keyFields: [],
        configFields: [],
        uploadKeyEnabled: true,
        verifyCloudConfigEnabled: true,
      };
    }
  }

  resetConfigFields(data: any) {
    //reset config field data
    //console.log('resetConfigFields',data)
    if (data.configFields)
      data.configFields.forEach(
        (field: {
          jsonField: string | number;
          type: string;
          required: any;
        }) => {
          //console.log('resetConfigFields1',data)
          this.newCloudInstance.configFields[field.jsonField] = '';
          if (field.type == 'value-array' || field.type == 'multi-select')
            this.modifiedValueArray[field.jsonField] = [];
          this.validateNewCloudInstance[field.jsonField] = field.required
            ? { value: false, msg: '' }
            : { value: true, msg: '' };
        }
      );
  }

  onAddCloudInstance() {
    //add cloud instance request
    if (!this.validateAddInstance()) return;
    this.addCloudBtn = false;
    const newCloudInstance = JSON.parse(JSON.stringify(this.newCloudInstance));
    if (this.selectedCloudType.configFields)
      this.selectedCloudType.configFields.map(
        (field: { type: string; jsonField: string | number }) => {
          if (field.type == 'value-array' || field.type == 'multi-select') {
            newCloudInstance.configFields[field.jsonField] = this
              .modifiedValueArray[field.jsonField].length
              ? this.modifiedValueArray[field.jsonField].join(',')
              : null;
          }
          if (field.type == 'yes-no') {
            newCloudInstance.configFields[field.jsonField] = newCloudInstance
              .configFields[field.jsonField]
              ? newCloudInstance.configFields[field.jsonField]
              : false;
          }
        }
      );
    //add cloud instance req
    this.taasCloudInstanceService
      .addCloudInstance(newCloudInstance, this.addBtn.class == 'verifyBtn')
      .subscribe((res) => {
        this.addCloudBtn = true;
        this.addInstanceError.msg = [];
        if (res.status == 200 || res.status == 201) {
          //success scenario
          this.onVerifySuccess(res);
        } else if (res.error && res.error.message) {
          //handle and display failure error
          this.addInstanceError.value = false;
          this.addInstanceError.msg.push(res.error.message);
          //this.addInstanceError.msg=res.error.message.split(',');
          this.addInstanceError.class = '';
        } else if (res.status == 503) {
          this.addInstanceError.value = false;
          this.addInstanceError.msg = [
            'Service unavailable, Please try again after some time.',
          ];
          this.addInstanceError.class = '';
        } else {
          //handle unknown error
          this.addInstanceError.value = false;
          this.addInstanceError.msg = ['Internal Server error'];
          this.addInstanceError.class = '';
        }
      });
  }

  onVerifySuccess(res: any) {
    //instance verification success
    switch (this.addBtn.class) {
      case 'verifyBtn':
        this.addBtn.class = 'cloudBtn';
        this.addBtn.text = 'Add';
        this.addInstanceError.value = false;
        this.instanceVerified = true;
        if (res.body && res.body.message) {
          this.addInstanceError.msg = [res.body.message];
          this.addInstanceError.class = 'text-success';
        }
        break;
      default:
        this.addInstanceError.value = true;
        this.addInstanceError.msg = [];
        this.addInstanceError.class = '';
        if (res.body && res.body.message) {
          this.toastService.success('Success', res.body.message);
        }
        //fire observable to update cloud instances
        this.taasCloudInstanceService.cloudInstanceChanged();
        this.triggerCloseModal();
        break;
    }
  }

  onSharedFieldUpdate(event: any, field: any) {
    //on change value of dynamic field
    if (
      (field.type == 'value-array' || field.type == 'multi-select') &&
      Array.isArray(event.event.target.value) &&
      event.event.target.value.length == 2
    ) {
      this.newCloudInstance.configFields[field.jsonField] =
        event.event.target.value[0].join(',');
      this.modifiedValueArray[field.jsonField] = event.event.target.value[1];
    } else
      this.newCloudInstance.configFields[field.jsonField] =
        event.event.target.value;
  }

  validateAddInstance() {
    //validate instance on submit
    let status = true;
    this.validateStep(
      this.newCloudInstance.instanceName,
      { jsonField: 'instanceName', label: 'instanceName', type: 'text-field' },
      { required: true, maxLength: 56, isAlphanumeric: true }
    );
    this.validateStep(
      this.newCloudInstance.cloudType,
      { jsonField: 'cloudType', label: 'Cloud Type', type: 'select' },
      { required: true, maxLength: 50 }
    );
    if (this.selectedCloudType.configFields)
      this.selectedCloudType.configFields.forEach(
        (field: {
          jsonField: string | number;
          required: any;
          maxLength: any;
        }) => {
          this.validateStep(
            this.newCloudInstance.configFields[field.jsonField],
            field,
            { required: field.required, maxLength: field.maxLength }
          );
        }
      );
    status = this.addCloudInstanceEnabled;
    return status;
  }

  validateStep(value: any, field: any, validator: any) {
    //validate step and enable next
    let result = true;
    const maxLength = validator.maxLength > 0 ? validator.maxLength : 50;
    if (field.type !== 'yes-no') {
      if (
        (field.type == 'value-array' || field.type == 'multi-select') &&
        validator.required
      ) {
        const configFields =
          this.newCloudInstance.configFields[field.jsonField];
        this.modifiedValueArray[field.jsonField] = this.modifiedValueArray[
          field.jsonField
        ]
          ? this.modifiedValueArray[field.jsonField].filter(
            (data: string) => data != ''
          )
          : [];
        //set config fields
        const newConfigFields = this.mergeNewConfigFields(
          configFields,
          this.modifiedValueArray[field.jsonField]
        );
        this.validateMultiSelectField(newConfigFields, field, maxLength);
      } else if ((validator.required && !value) || value.trim() == '') {
        this.validateNewCloudInstance[field.jsonField].value = false;
        this.validateNewCloudInstance[field.jsonField].msg =
          VALIDATION_MESSAGES.FIELD_REQUIRED;
      } else if (value && value.length > maxLength) {
        this.validateNewCloudInstance[field.jsonField].value = false;
        this.validateNewCloudInstance[field.jsonField].msg =
          VALIDATION_MESSAGES.VALIDATION_CHECK_MSG +
          maxLength +
          VALIDATION_MESSAGES.CHARACTER;
      } else if (
        validator.isAlphanumeric &&
        !value.match(/^[A-Za-z0-9._:-]+$/)
      ) {
        this.validateNewCloudInstance[field.jsonField].value = false;
        this.validateNewCloudInstance[
          field.jsonField
        ].msg = $localize`:AddCloudInstance-New Cloud Instance Validation Msg@@validate_instance:Field can only contain letters, numbers, hyphens, underscores, and periods`;
      } else {
        this.validateNewCloudInstance[field.jsonField].value = true;
        this.validateNewCloudInstance[field.jsonField].msg = '';
      }
    }
    Object.keys(this.validateNewCloudInstance).forEach((key) => {
      if (this.validateNewCloudInstance[key].value == false) {
        result = false;
      }
    });

    this.addCloudInstanceEnabled = result;
  }

  validateMultiSelectField(newConfigFields: any, field: any, maxLength: any) {
    //validate multi-select or value-array input type
    if (!newConfigFields || newConfigFields == '') {
      this.validateNewCloudInstance[field.jsonField].value = false;
      this.validateNewCloudInstance[field.jsonField].msg = [
        VALIDATION_MESSAGES.FIELD_REQUIRED,
      ];
    } else if (this.hasDuplicates(this.modifiedValueArray[field.jsonField])) {
      this.validateNewCloudInstance[field.jsonField].value = false;
      this.validateNewCloudInstance[field.jsonField].msg = [
        VALIDATION_MESSAGES.UNIQUE_CHECK,
      ];
    } else if (
      this.modifiedValueArray[field.jsonField].filter(
        (d: string | any[]) => d.length > maxLength
      ).length
    ) {
      this.validateNewCloudInstance[field.jsonField].value = false;
      this.validateNewCloudInstance[field.jsonField].msg =
        VALIDATION_MESSAGES.VALIDATION_CHECK_MSG +
        maxLength +
        VALIDATION_MESSAGES.CHARACTER;
    } else {
      this.validateNewCloudInstance[field.jsonField].value = true;
      this.validateNewCloudInstance[field.jsonField].msg = '';
    }
  }

  mergeNewConfigFields(value: any, newValueArr: any) {
    //merge fields together
    const newValue = newValueArr.join(',');
    if (!value) return newValue;
    if (!newValue) return value;
    if (value && newValue) return value + ',' + newValue;
  }

  hasDuplicates(list: any) {
    //check if list has duplicates
    return new Set(list).size !== list.length;
  }

  // openHelpDialog(){//help page link director
  //   if(this.selectedCloudType && this.selectedCloudType.uploadKeyEnabled)
  //     this.dialog.open(HelpComponent, {
  //       width: '95%',
  //       data: {nodeName: 'Add New Cloud Instance'},
  //       panelClass:'help-dialog'
  //     });
  //   else
  //     this.dialog.open(HelpComponent, {
  //       width: '95%',
  //       data: {nodeName: 'Add New External Cloud Instance'},
  //       panelClass:'help-dialog'
  //     });
  // }
  public triggerCloseModal() {
    this.modalReference?.closeModal();
  }
}
