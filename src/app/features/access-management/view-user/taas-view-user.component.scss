.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.manage-access-content {
  display: flex;
  flex: 1;
  margin-top: 20px;
  overflow: hidden;
}

.users-panel {
  width: 350px;
  height: 100%;
  overflow-y: auto;
  overflow: hidden;
  flex-shrink: 0;
  background-color: #f8f9fa;
}

.permissions-panel {
  flex: 1;
  padding-left: 20px;
  overflow: hidden;
  min-width: 0;
}

@media (max-width: 768px) {
  .manage-access-content {
    flex-direction: column;
  }

  .users-panel {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 20px;
  }

  .permissions-panel {
    padding-left: 0;
  }

  .loading-container{
    margin-top: 500px;
    margin-left: 600px;
  }
}
