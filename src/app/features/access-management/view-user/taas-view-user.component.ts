import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSliderModule } from '@angular/material/slider';
import { TaasUserListComponent } from '../taas-user-list/taas-user-list.component';
import { TaasUserPermissionManagerComponent } from '../taas-user-permission-manager/taas-user-permission-manager.component';
import { TaasAccessManagementService } from '../../../core/services/accessManagement/taas-access-management.service';
import { User, Instance } from '../../../core/models/access-management';
import { TaasModalLoadingSpinnerComponent } from 'utimaco-common-ui-angular';
@Component({
  selector: 'app-taas-view-user',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    MatTabsModule,
    MatSliderModule,
    TaasModalLoadingSpinnerComponent,
    TaasUserPermissionManagerComponent,
    TaasUserListComponent,
  ],
  templateUrl: './taas-view-user.component.html',
  styleUrl: './taas-view-user.component.scss',
})
export class TaasViewUserComponent implements OnInit {
  users: User[] = [];
  availableInstances: Instance[] = [];
  selectedUserId: string | null = null;
  selectedUser: User | null = null;
  hasUnsavedChanges = false;
  isLoadingUsers = false;
  isLoadingInstances = false;
  errorMessage = '';

  pendingUserId: string | null = null;

  constructor(
    private userService: TaasAccessManagementService
  ) {}

  ngOnInit() {
    this.loadUsers();
    this.loadAvailableInstances();
  }

  loadUsers() {
    this.isLoadingUsers = true;
    this.errorMessage = '';

    this.userService.getUsers().subscribe({
      next: (users) => {
        this.users = users;
        this.isLoadingUsers = false;

        if (users.length > 0) {
          this.onUserSelected(users[0].id);
        }
      },
      error: (error) => {
        console.error('Error loading users:', error);
        this.isLoadingUsers = false;
        this.errorMessage = 'Failed to load users. Please try again later.';
      },
    });
  }

  loadAvailableInstances() {
    this.isLoadingInstances = true;

    this.userService.getAvailableInstances().subscribe({
      next: (instances) => {
        this.availableInstances = instances;
        this.isLoadingInstances = false;
      },
      error: (error) => {
        //console.error('Error loading instances:', error);
        this.isLoadingInstances = false;
        this.errorMessage = 'Failed to load instances. Please try again later. ' + error;
      },
    });
  }

  onUserSelected(userId: string) {
    if (this.hasUnsavedChanges) {
      this.pendingUserId = userId;
    } else {
      this.changeUser(userId);
    }
  }

  discardChangesAndSwitch() {
    if (this.pendingUserId !== null) {
      this.changeUser(this.pendingUserId);
      this.pendingUserId = null;
      this.hasUnsavedChanges = false;
    }
  }

  cancelSwitch() {
    this.pendingUserId = null;
  }

  private changeUser(userId: string) {
    this.selectedUserId = userId;
    this.selectedUser = this.users.find((user) => user.id === userId) || null;
    this.hasUnsavedChanges = false;
  }

  onPermissionsSaved() {
    console.log('User permissions saved successfully');
    this.hasUnsavedChanges = false;
  }

  onUnsavedChanges(hasChanges: boolean) {
    this.hasUnsavedChanges = hasChanges;
  }
}
