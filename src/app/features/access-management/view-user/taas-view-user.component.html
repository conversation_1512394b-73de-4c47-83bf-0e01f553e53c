<div class="app-container" data-testid="app.container">
  <div class="main-content" data-testid="app.content.main">
    <div class="tabs" data-testid="app.container.tabs">
      <div class="manage-access-content" data-testid="app.content.manageaccess">
        <!-- Error message display -->
        <!-- @if (errorMessage) {
          <div class="error-message" data-testid="app.message.error">
            {{ errorMessage }}
            <button mat-button color="primary" (click)="loadUsers(); loadAvailableInstances()" data-testid="app.button.retry">Retry</button>
          </div>
        } -->

        <!-- Loading indicators -->
        @if (isLoadingUsers) {
        <lib-taas-modal-loading-spinner uniqueId="load-users-spinner" [spinnerVisible]="isLoadingUsers"
          content="Loading users..." data-testid="app.spinner.modal">
        </lib-taas-modal-loading-spinner>
        } @else if (users.length === 0) {
        <div class="no-data-message" data-testid="app.container.nodata">
          <p data-testid="app.text.nousers">No users found in the system.</p>
        </div>
        } @else {
        <div class="users-panel" data-testid="app.panel.users">
          <app-taas-user-list [users]="users" [selectedUserId]="selectedUserId" (userSelected)="onUserSelected($event)"
            data-testid="app.component.userlist">
          </app-taas-user-list>
        </div>
        <div class="permissions-panel" data-testid="app.panel.permissions">
          <!-- <app-taas-user-permission-manager [selectedUser]="selectedUser" [availableInstances]="availableInstances"
            (saveChanges)="onPermissionsSaved()" (hasUnsavedChanges)="onUnsavedChanges($event)"
            data-testid="app.component.permissionmanager">
          </app-taas-user-permission-manager> -->
          <app-taas-user-permission-manager [selectedUser]="selectedUser" [availableInstances]="availableInstances"
            [pendingUserId]="pendingUserId" (saveChanges)="onPermissionsSaved()"
            (hasUnsavedChanges)="onUnsavedChanges($event)" (discardChanges)="discardChangesAndSwitch()"
            (cancelUserSwitch)="cancelSwitch()" data-testid="app.component.permissionmanager">
          </app-taas-user-permission-manager>
        </div>
        <!-- <div class="permissions-panel" data-testid="app.panel.permissions">
          <app-taas-user-permission-manager [selectedUser]="selectedUser" [availableInstances]="availableInstances"
            (saveChanges)="onPermissionsSaved()" data-testid="app.component.permissionmanager">
          </app-taas-user-permission-manager>
        </div> -->
        }
      </div>
    </div>
  </div>
</div>
