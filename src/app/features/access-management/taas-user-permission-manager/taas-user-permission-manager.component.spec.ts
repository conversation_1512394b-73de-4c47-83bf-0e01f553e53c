import { ComponentFixture, TestBed } from '@angular/core/testing';

import { TaasUserPermissionManagerComponent } from './taas-user-permission-manager.component';

describe('TaasUserPermissionManagerComponent', () => {
  let component: TaasUserPermissionManagerComponent;
  let fixture: ComponentFixture<TaasUserPermissionManagerComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TaasUserPermissionManagerComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(TaasUserPermissionManagerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
