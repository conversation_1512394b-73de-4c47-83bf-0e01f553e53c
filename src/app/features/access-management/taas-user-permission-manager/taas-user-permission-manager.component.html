<div class="permission-manager" data-testid="permission.container">
  <div class="note-indicator" data-testid="permission.container.note">
    You have unsaved changes (marked
    <span class="add-indicator-icon">
      <img src="assets/icons/right-arrow.svg" alt="type" />
    </span>
    <span class="delete-indicator-icon">
      <img src="assets/icons/right-arrow.svg" alt="type" />
    </span>
    )
  </div>
  <div class="header" data-testid="permission.header">
    <div class="header-row" data-testid="permission.row.header">
      <div class="add-instance-button" data-testid="permission.container.addbutton">
        <button mat-button (click)="startAddInstance()" [disabled]="isLoading"
          data-testid="permission.button.addinstance">
          <div class="button-content" data-testid="permission.container.buttoncontent">
            <img src="assets/icons/plus-square.svg" title="Add" class="add-instance-icon" alt="type" />
            <span data-testid="permission.text.instances">Instances</span>
          </div>
        </button>
      </div>
      <div class="permissions-col-header" data-testid="permission.container.permissionslabel">
        <div class="permissions-label" data-testid="permission.text.permissions">Permissions</div>
      </div>
      <div class="save-button-container" data-testid="permission.container.save">
        <lib-taas-button [buttonText]="'SAVE'" [addClass]="'save-button'" [buttonType]="'primary'"
          [disabled]="isSaving || !isDirty" (buttonPressedEvent)="onSave()" data-testid="permission.button.save">
        </lib-taas-button>
      </div>
    </div>
  </div>
  <lib-taas-modal-dialogue uniqueId="showUnsavedWarnings" [modalVisible]="showUnsavedChangesWarning" [showFooter]="true"
    variant="danger" [title]="modalTitle" [content]="warningMessage" (cancel)="cancelDiscardChanges()"
    (confirm)="confirmDiscardChanges()">
  </lib-taas-modal-dialogue>

  <div class="instances-section" data-testid="permission.section.instances">
    <!-- Loading spinner -->
    <lib-taas-modal-loading-spinner uniqueId="load-permissions-spinner" [spinnerVisible]="isLoading"
      content="Loading permissions..." data-testid="permission.spinner.modal">
    </lib-taas-modal-loading-spinner>

    @if (selectedUser && !isLoading && (userInstances.length !== 0 || isAddingInstance)) {
    <div class="permission-labels" data-testid="permission.container.permissionlabels">
      <div class="instance-col" data-testid="permission.col.instance"></div>
      <div class="permissions-slider-wrapper" data-testid="permission.container.sliderwrapper">
        <div class="slider-permission-labels" data-testid="permission.container.sliderlabels">
          <span class="permission-label" data-testid="permission.label.viewer">Viewer</span>
          <span class="permission-label" data-testid="permission.label.editor">Editor</span>
          <span class="permission-label" data-testid="permission.label.admin">Admin</span>
        </div>
      </div>
    </div>
    }

    <div class="add-instance-row" [class.highlight]="isAddingInstance" data-testid="permission.row.addinstance">
      @if (isAddingInstance) {
      <div class="instance-col" data-testid="permission.col.addinstance">
        <div class="dropdown-wrapper">
          <div class="dropdown-container" data-testid="permission.container.dropdown">
            <input type="text" placeholder="Select..." [value]="searchQuery" (input)="updateSearchQueryAndShowDropdown($event)"
              (click)="toggleDropdown($event)" class="search-input" data-testid="permission.input.search" />
            <div class="dropdown-arrow" (click)="toggleDropdown($event)" (keydown.enter)="toggleDropdown($event)"
              (keydown.space)="toggleDropdown($event)" tabindex="0" data-testid="permission.button.dropdown">▼</div>
            @if (showDropdown) {
            <div class="dropdown-list" data-testid="permission.list.dropdown">
              @for (instance of filteredInstances; track instance.id) {
              <div class="dropdown-item" (click)="selectInstance(instance)" (keydown.enter)="selectInstance(instance)"
                (keydown.space)="selectInstance(instance)" tabindex="0"
                attr.data-testid="permission.item.instance.{{instance.id}}">
                {{ instance.instanceName }}
              </div>
              }
              @if (filteredInstances.length === 0) {
              <div class="dropdown-item no-results" data-testid="permission.text.noresults">
                No instances found
              </div>
              }
            </div>
            }
          </div>
          @if (validationError) {
          <div class="validation-error" data-testid="permission.text.validationerror">
            {{ validationError }}
          </div>
          }
        </div>
      </div>
      <div class="permissions-col" data-testid="permission.col.permissions">
        <div class="permissions-slider-container" data-testid="permission.container.slider">
          <div class="slider-container" data-testid="permission.container.sliderinner">
            <div class="slider-bar" data-testid="permission.container.sliderbar">
              <div class="slider-fill" [style.width]="getSliderFillWidth(permissionLevel)"
                data-testid="permission.element.sliderfill"></div>
              <div class="slider-dots" data-testid="permission.container.sliderdots">
                <div class="slider-dot" [class.active]="permissionLevel >= 0" data-testid="permission.dot.viewer">
                </div>
                <div class="slider-dot" [class.active]="permissionLevel >= 1" data-testid="permission.dot.editor">
                </div>
                <div class="slider-dot" [class.active]="permissionLevel >= 2" data-testid="permission.dot.admin">
                </div>
              </div>
              <input type="range" min="0" max="2" step="1" [value]="permissionLevel"
                (input)="updatePermissionLevelFromEvent($event)" class="permission-slider"
                data-testid="permission.slider.permission" />
            </div>
          </div>
        </div>
        <div class="actions" data-testid="permission.container.actions">
          <button class="cancel-btn" (click)="cancelAddInstance()" data-testid="permission.button.cancel">
            <img src="assets/icons/dismiss.svg" title="Cancel" class="row-icon button-padding" alt="type" />
          </button>
          <button class="confirm-btn" (click)="confirmAddInstance()" data-testid="permission.button.confirm">
            <img src="assets/icons/tick.svg" title="Confirm" class="row-icon button-padding" alt="type" />
          </button>
        </div>
      </div>
      }
    </div>

    <!-- DELETED INSTANCES AT TOP - THIS IS THE NEW SECTION -->
    @for (deletedInstance of deletedInstances; track deletedInstance.instanceId; let j = $index) {
    <div class="instance-row" attr.data-testid="permission.row.deleted.{{j}}">
      <div class="instance-col" attr.data-testid="permission.col.deletedname.{{j}}">
        <!-- Red change indicator icon for deleted -->
        <div class="change-indicator deleted" attr.data-testid="permission.indicator.deleted.{{j}}">
          <!-- <div class="pin-arrow-icon"></div> -->
          <img src="assets/icons/right-arrow.svg" title="Deleted" class="change-indicator-icon" alt="type" />
        </div>

        <span class="instance-name-strikethrough" attr.data-testid="permission.name.deleted.{{j}}">
          {{ deletedInstance.instanceName }}
        </span>
      </div>

      <div class="permissions-col deleted-permissions" attr.data-testid="permission.col.deletedpermissions.{{j}}">
        <div class="deleted-permission-label" attr.data-testid="permission.label.deletedpermission.{{j}}">
          {{ deletedInstance.instanceName }} - {{ getChangeInfo(deletedInstance.instanceId)?.originalPermission }}
          (Deleted)
        </div>
        <div class="revert-actions" attr.data-testid="permission.container.revertactions.{{j}}">
          <button class="revert-btn" (click)="revertDelete(deletedInstance.instanceId)"
            attr.data-testid="permission.button.revert.{{j}}" title="Undo delete">
            <mat-icon>undo</mat-icon>
            <span>Revert</span>
          </button>
        </div>
      </div>
    </div>
    }

    @if (selectedUser && !isLoading && userInstances.length === 0 && !isAddingInstance && deletedInstances.length === 0)
    {
    <div class="no-instances" data-testid="permission.container.noinstances">
      <p data-testid="permission.text.noinstances">No instances assigned to this user</p>
    </div>
    }

    <!-- ACTIVE INSTANCES AT BOTTOM - MOVED DOWN -->
    @for (userInstance of userInstances; track userInstance.instanceId; let i = $index) {
    @if (!isInstanceDeleted(userInstance.instanceId)) {
    <div class="instance-row" [class.instance-added]="isInstanceAdded(userInstance.instanceId)"
      [class.instance-updated]="isInstanceUpdated(userInstance.instanceId)"
      attr.data-testid="permission.row.instance.{{i}}">

      <div class="instance-col" attr.data-testid="permission.col.instancename.{{i}}">
        <!-- Change indicator icon -->
        @if (isInstanceAdded(userInstance.instanceId)) {
        <div class="change-indicator added" attr.data-testid="permission.indicator.added.{{i}}">
          <!-- <div class="pin-arrow-icon"></div> -->
          <img src="assets/icons/right-arrow.svg" class="change-indicator-icon" alt="type" />
        </div>
        }
        @if (isInstanceUpdated(userInstance.instanceId)) {
        <div class="change-indicator updated" attr.data-testid="permission.indicator.updated.{{i}}">
          <!-- <div class="grid">
            <div class="icon-box"><div class="icon" style="mask-image: var(--icon-pinarrow); -webkit-mask-image: var(--icon-pinarrow);"></div>pinarrow</div>
            </div> -->
          <!-- <div class="pin-arrow-icon"></div> -->
          <img src="assets/icons/right-arrow.svg" class="change-indicator-icon" alt="type" />
          <div class="change-tooltip">
            {{ getChangeInfo(userInstance.instanceId)?.originalPermission }} → {{
            getChangeInfo(userInstance.instanceId)?.newPermission }}
          </div>
        </div>
        }

        <span
          [class.instance-name-bold]="isInstanceAdded(userInstance.instanceId) ||isInstanceUpdated(userInstance.instanceId) "
          attr.data-testid="permission.name.instance.{{i}}">
          {{ userInstance.instanceName }}
        </span>
      </div>

      <div class="permissions-col" attr.data-testid="permission.col.instancepermissions.{{i}}">
        <div class="permissions-slider-container" attr.data-testid="permission.container.instanceslider.{{i}}">
          <div class="slider-container" attr.data-testid="permission.container.instancesliderinner.{{i}}">
            <div class="slider-bar" attr.data-testid="permission.container.instancesliderbar.{{i}}">
              <div class="slider-fill" [style.width]="getSliderFillWidth(getPermissionLevelForInstance(userInstance))"
                attr.data-testid="permission.element.instancesliderfill.{{i}}">
              </div>
              <div class="slider-dots" attr.data-testid="permission.container.instancesliderdots.{{i}}">
                <div class="slider-dot" [class.active]="getPermissionLevelForInstance(userInstance) >= 0"
                  attr.data-testid="permission.dot.instanceviewer.{{i}}"></div>
                <div class="slider-dot" [class.active]="getPermissionLevelForInstance(userInstance) >= 1"
                  attr.data-testid="permission.dot.instanceeditor.{{i}}"></div>
                <div class="slider-dot" [class.active]="getPermissionLevelForInstance(userInstance) >= 2"
                  attr.data-testid="permission.dot.instanceadmin.{{i}}"></div>
              </div>
              <input type="range" min="0" max="2" step="1" [value]="getPermissionLevelForInstance(userInstance)"
                (input)="updateInstancePermission(userInstance, $event)" class="permission-slider"
                attr.data-testid="permission.slider.instance.{{i}}" />
            </div>
          </div>
        </div>
        <div class="delete-actions" attr.data-testid="permission.container.deleteactions.{{i}}">
          <button class="delete-btn" (click)="deleteInstance(i)"
            attr.data-testid="permission.button.deleteinstance.{{i}}" title="Delete instance">
            <mat-icon attr.data-testid="permission.icon.delete.{{i}}">delete</mat-icon>
          </button>
        </div>
      </div>
    </div>
    }
    }
  </div>
</div>
