@media (max-width: 576px) {
  .slider-permission-labels {
    height: 50px;
  }

  .permission-label {
    font-size: 1px;
    width: 45px;
  }

  .permission-label:nth-child(1) {
    left: -3px;
  }

  .permission-label:nth-child(3) {
    right: -3px;
  }
}

.header {
  margin-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;

  .header-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
}

.permission-manager{
  .note-indicator{
    .add-indicator-icon{
      $green-filter: invert(50%) sepia(99%) saturate(220%) hue-rotate(80deg) brightness(95%) contrast(90%);

      img {
        filter: $green-filter;
        height: 20px;
      }

      &:hover img {
        filter: $green-filter brightness(120%);
      }
    }
  .delete-indicator-icon {
    $red-filter: invert(30%) sepia(100%) saturate(500%) hue-rotate(320deg) brightness(125%) contrast(90%);

    img {
      filter: $red-filter;
      height: 20px;
    }

    &:hover img {
      filter: $red-filter brightness(130%);
    }
  }
  text-align: right;
}
}

.instances-section {
  background-color: #fff;
  padding: 20px;
}

.instances-header {
  display: flex;
  justify-content: space-between;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  margin-bottom: 10px;
}

.permissions-col-header {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.permissions-col {
  flex: 2;
  display: flex;
  align-items: center;
}

.permissions-label {
  flex: 1;
  color: #0068b4;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
}

.save-button-container {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  height: 36px; /* Reduced height to align with row headers */
}

.permission-labels {
  display: flex;
  margin-bottom: 15px;
}

.permissions-slider-wrapper {
  flex: 2;
  position: relative;
  overflow: visible;
}

.slider-permission-labels {
  display: flex;
  justify-content: space-between;
  width: 45%;
  margin: 0 auto;
  position: relative;
  padding-bottom: 15px;
}

.permission-label {
  font-size: 14px;
  text-align: center;
  width: 50px;
}

.dot-row-instance {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  width: 100%;
  position: absolute;
  top: -10px;
  z-index: 5;
}

.add-instance-button {
  padding: 10px 0;

  button {
    padding: 0 8px;

    .button-content {
      display: inline-flex;
      align-items: center;
      white-space: nowrap;

      span {
        color: #0068b4;
        font-size: 16px;
        font-weight: bold;
        line-height: 1;
        display: inline-flex;
        align-items: center;
      }
    }
  }
}

.instance-row,
.add-instance-row {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f5f5f5;
}

.add-instance-row.highlight {
  background-color: #f0f7ff;
  border-radius: 4px;
  box-shadow: 0 0 0 1px #0068b4;
  padding: 12px 8px;
  margin: 50px -8px 8px -8px;
}



.instance-col {
  flex: 1;
  padding-right: 20px;
  display: flex;
  align-items: center;
  gap: 8px;

  // Change indicator styles
  .change-indicator {
    display: flex;
    align-items: center;
    margin-right: 8px;
    position: relative;
    &.added {
      $green-filter: invert(50%) sepia(99%) saturate(220%) hue-rotate(80deg) brightness(95%) contrast(90%);

      img {
        filter: $green-filter;
      }

      &:hover img {
        filter: $green-filter brightness(120%);
      }
    }
    &.updated {
      $green-filter: invert(50%) sepia(99%) saturate(220%) hue-rotate(80deg) brightness(95%) contrast(90%);

      img {
        filter: $green-filter;
      }

      &:hover img {
        filter: $green-filter brightness(120%);
      }
      // TODO: Below to be used if finalized to have yellow arrow
      // $yellow-filter: invert(30%) sepia(100%) saturate(500%) hue-rotate(320deg) brightness(195%) contrast(690%);

      // img {
      //   filter: $yellow-filter;
      // }

      // &:hover img {
      //   filter: $yellow-filter brightness(120%);
      // }
    }

    &.deleted {
      $red-filter: invert(30%) sepia(100%) saturate(500%) hue-rotate(320deg) brightness(125%) contrast(90%);

      img {
        filter: $red-filter;
      }

      &:hover img {
        filter: $red-filter brightness(130%);
      }
    }

    // Tooltip for updated instances
    .change-tooltip {
      position: absolute;
      left: 20px;
      top: -35px;
      background: #333;
      color: white;
      padding: 6px 8px;
      border-radius: 4px;
      font-size: 12px;
      white-space: nowrap;
      opacity: 0;
      pointer-events: none;
      transition: opacity 0.3s ease;
      z-index: 10;

      &::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 10px;
        border: 4px solid transparent;
        border-top-color: #333;
      }
    }

    &:hover .change-tooltip {
      opacity: 1;
    }
  }

  // Bold text for added instances
  .instance-name-bold {
    font-weight: 600;
  }

  // Strikethrough text for deleted instances
  .instance-name-strikethrough {
    text-decoration: line-through;
    color: #999;
  }
}

.permissions-slider-container {
  flex: 1;
  position: relative;
  padding: 10px 100px 0px 0px;
}

.slider-container {
  width: 45%;
  margin: 0 auto;
}

.slider-bar {
  position: relative;
  height: 4px;
  border-radius: 2px;
}

.slider-fill {
  position: absolute;
  height: 100%;
  background-color: #0068b4;
  border-radius: 2px;
  transition: width 0.3s;
  z-index: 1;
}

.permission-slider {
  position: absolute;
  top: -7px;
  left: 0;
  width: 100%;
  height: 18px;
  appearance: none;
  -webkit-appearance: none;
  margin: 0;
  background: transparent;
  z-index: 3;

  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #0068b4;
    cursor: pointer;
    border: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }

  &::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #0068b4;
    cursor: pointer;
    border: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }
}

.slider-dots {
  display: flex;
  justify-content: space-between;
  position: absolute;
  width: 100%;
  top: -3px;
  z-index: 2;
}

.slider-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #e0e0e0;
  transition: background-color 0.3s;

  &.active {
    background-color: #0068b4;
  }
}

.actions {
  margin-left: 15px;
  display: flex;
  gap: 8px; // Increased gap for larger buttons
  height: 100%;
  align-items: center;
  min-height: 48px; // Ensure container can accommodate larger buttons
}

.delete-actions {
  display: flex;
  gap: 5px;
  margin-left: 50px;
}

// Revert button styles for deleted instances
.revert-actions {
  display: flex;
  align-items: center;
  margin-left: 0;

  .revert-btn {
    background: #f44336;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;

    &:hover {
      background: #d32f2f;
    }

    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
  }
}

// Styles for deleted permissions display
.deleted-permissions {
  justify-content: space-between;

  .deleted-permission-label {
    color: #666;
    font-style: italic;
    flex: 1;
  }
}

.confirm-btn,
.cancel-btn {
  background-color: #f0f7ff;
}

.delete-btn {
  background-color: white;
}

.confirm-btn,
.cancel-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;  // Increased from 32px for better scaling
  height: 48px; // Fixed height instead of 100% for consistent sizing
  border-radius: 8px; // Squared with rounded edges
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;

  // Scale the icons inside the buttons
  img {
    width: 20px;  // Explicit icon size for better scaling
    height: 20px;
    transition: transform 0.2s ease;
  }

  // Hover effect with slight scale
  &:hover {
    transform: scale(1.05);

    img {
      transform: scale(1.1);
    }
  }

  // Active state
  &:active {
    transform: scale(0.95);
  }
}

.delete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s;
}

.confirm-btn{
  $green-filter: invert(50%) sepia(99%) saturate(220%) hue-rotate(80deg) brightness(95%) contrast(90%);

  img {
    filter: $green-filter;
  }

  &:hover img {
    filter: $green-filter brightness(120%);
  }
}

.cancel-btn {
  $red-filter: invert(30%) sepia(100%) saturate(500%) hue-rotate(320deg) brightness(95%) contrast(90%);

  img {
    filter: $red-filter;
  }

  &:hover img {
    filter: $red-filter brightness(130%);
  }
}

.dropdown-cancel-btn {
  width: 14px;
  $grey-filter: invert(38%) sepia(0%) saturate(0%) hue-rotate(170deg) brightness(95%) contrast(85%);
  filter: $grey-filter;

  &:hover img {
    filter: $grey-filter brightness(120%);
  }
}

.add-instance-icon {
  height: 30px;
  margin-right: 5px;
}

.delete-btn {
  &:hover {
    color: rgba(0, 0, 0, 0.7);
  }
}

.dropdown-container {
  position: relative;
  width: 100%;
}

.search-input {
  width: 100%;
  padding: 8px 0px 8px 12px; /* Added left padding */
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  //padding-right: 60px;
}

.dropdown-arrow {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #757575;
  pointer-events: auto;
  cursor: pointer;
}

.clear-input {
  position: absolute;
  right: 36px;
  top: 50%;
  transform: translateY(-50%);
  color: #757575;
  cursor: pointer;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.dropdown-item {
  padding: 8px 12px;
  cursor: pointer;

  &:hover {
    background-color: #f5f5f5;
  }

  &.no-results {
    font-style: italic;
    color: #757575;
    cursor: default;
  }
}

.no-instances {
  text-align: center;
  color: #757575;
  padding: 20px;
}

.mat-mdc-button>.mat-icon {
  color: #0068b4;
  font-weight: bold;
}

.validation-error {
  color: #f44336;
  font-size: 12px;
  margin-top: 5px;
}

.unsaved-changes-warning {
  margin-top: 16px;
  margin-bottom: 16px;
  padding: 12px 16px;
  background-color: #fff3cd;
  border-left: 4px solid #ffc107;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.warning-message {
  font-weight: 500;
  color: #856404;
}

.warning-actions {
  display: flex;
  gap: 8px;
}

.custom-modal-title {
  font-size: 18px;
  font-weight: bold;
  margin: 0;
}

.custom-modal-subtitle {
  font-size: 14px;
  margin: 4px 0 0 0;
}

.change-indicator-icon{
  height: 20px;
}
.dropdown-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
}
