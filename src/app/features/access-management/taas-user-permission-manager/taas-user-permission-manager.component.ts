import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnChanges,
  SimpleChanges,
  ElementRef,
  HostListener,
  inject,
  OnInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatSliderModule } from '@angular/material/slider';
import {
  User,
  Instance,
  UserInstance,
  Permission,
  SaveUserPermissionsRequest,
} from '../../../core/models/access-management';
import { TaasAccessManagementService } from '../../../core/services/accessManagement/taas-access-management.service';
import {
  TaasButtonComponent,
  ToastService,
  TaasModalDialogueComponent,
  TaasModalLoadingSpinnerComponent,
} from 'utimaco-common-ui-angular';
import { UserRoleService } from '../../../core/services/role/user-role.service';
import { Role } from '../../../core/models/roles.enum';

// Define change types for visual indicators
export interface InstanceChange {
  instanceId: string;
  changeType: 'added' | 'updated' | 'deleted';
  originalPermission?: string | null;
  newPermission?: string | null;
}

@Component({
  selector: 'app-taas-user-permission-manager',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    MatSliderModule,
    TaasModalLoadingSpinnerComponent,
    TaasButtonComponent,
    TaasModalDialogueComponent,
  ],
  templateUrl: './taas-user-permission-manager.component.html',
  styleUrl: './taas-user-permission-manager.component.scss',
})
export class TaasUserPermissionManagerComponent implements OnChanges, OnInit {
  @Input() selectedUser: User | null = null;
  @Input() availableInstances: Instance[] = [];
  @Output() saveChanges = new EventEmitter<UserInstance[]>();
  @Output() hasUnsavedChanges = new EventEmitter<boolean>();
  @Output() discardChanges = new EventEmitter<void>();
  @Output() cancelUserSwitch = new EventEmitter<void>();

  @Input() hasPendingUserSwitch = false;
  @Input() pendingUserId: string | null = null;

  showUnsavedChangesWarning = false;
  warningMessage = '';
  modalTitle = `<div class="custom-modal-title">Caution!</div><div class="custom-modal-subtitle">⚠ You have unsaved changes!</div>`;
  userInstances: UserInstance[] = [];
  availablePermissions: Permission[] = [];
  originalUserInstances: UserInstance[] = []; // store original state from backend
  deletedInstances: UserInstance[] = []; // track deleted instances
  instanceChanges: Map<string, InstanceChange> = new Map(); // track all changes
  isLoading = false;
  isSaving = false;
  isLoadingPermissions = false;
  isDirty = false; // to track if there are unsaved changes

  isAddingInstance = false;
  searchQuery = '';
  showDropdown = false;
  filteredInstances: Instance[] = [];
  selectedNewInstance: Instance | null = null;
  permissionLevel = 0;
  newInstancePermission = 'viewer'; // default to viewer
  validationError = '';
  spinnerContent!: string;
  confirmAddNewInstance = false;

  constructor(
    private elementRef: ElementRef,
    private accessManagementService: TaasAccessManagementService,
    private userRoleService: UserRoleService
  ) {}
  private _toast = inject(ToastService);

  // Permission system properties
  canEditInstance = false;
  canDeleteInstance = false;

  ngOnInit() {
    this.loadPermissions();
    this.initializePermissions();
  }

  /**
   * Initialize role-based permissions for this component
   * Based on the permission system service pattern from Azure instances
   */
  private initializePermissions(): void {
    // Only Admin and Editor can edit instances
    this.canEditInstance = this.userRoleService.hasAnyRole([
      Role.INSTANCE_EDITOR,
      Role.INSTANCE_ADMIN,
      Role.ORG_ADMIN
    ]);

    // Only Admin can delete instances
    this.canDeleteInstance = this.userRoleService.hasAnyRole([
      Role.INSTANCE_ADMIN,
      Role.ORG_ADMIN
    ]);

    console.log('User Permission Manager Permissions:', {
      canEditInstance: this.canEditInstance,
      canDeleteInstance: this.canDeleteInstance
    });
  }

  loadPermissions() {
    this.isLoadingPermissions = true;

    this.accessManagementService.getPermissions().subscribe({
      next: (permissions) => {
        this.availablePermissions = permissions;
        this.isLoadingPermissions = false;
      },
      error: (error) => {
        //console.error('Error loading permissions:', error);
        this._toast.error(
          'Error',
          'Failed to load permissions - ' +
            error +
            '.' +
            'Please try again later.'
        );
        this.isLoadingPermissions = false;
      },
    });
  }

  @HostListener('document:click', ['$event'])
  clickOutside(event: Event) {
    const target = event.target as HTMLElement;

    // Check if the click is outside the dropdown container
    const dropdownContainer = this.elementRef.nativeElement.querySelector(
      '.dropdown-container'
    );

    if (dropdownContainer && !dropdownContainer.contains(target)) {
      this.showDropdown = false;
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['selectedUser'] && this.selectedUser) {
      if (this.isDirty) {
        this.checkUnsavedChanges();
      }
      this.loadUserPermissions();
      this.isAddingInstance = false;
      this.validationError = '';
      this.isDirty = false;
      this.deletedInstances = []; // reset deleted instances for new user
      this.instanceChanges.clear(); // reset change tracking
      this.emitChangeStatus();

      this.showUnsavedChangesWarning = false;
    }

    if (changes['pendingUserId'] && this.pendingUserId !== null) {
      this.showUnsavedChangesWarning = true;
      this.warningMessage =
        'By navigating away from the user page these changes will be lost. Are you sure you want to proceed?';
      // this.warningMessage =
      // 'You have unsaved changes. Do you want to discard them?';
    }
  }

  loadUserPermissions() {
    if (!this.selectedUser) return;

    this.isLoading = true;
    this.userInstances = [];

    this.accessManagementService
      .getUserPermissions(this.selectedUser.id)
      .subscribe({
        next: (permissions) => {
          // store fresh copy in userInstances
          this.userInstances = JSON.parse(JSON.stringify(permissions));
          // store the original state in originalUserInstances with a deep clone
          this.originalUserInstances = JSON.parse(JSON.stringify(permissions));
          this.deletedInstances = []; // reset deleted instances
          this.instanceChanges.clear(); // reset change tracking
          this.isLoading = false;
          this.isDirty = false;
          this.emitChangeStatus();
        },
        error: (error) => {
          //console.error('Error loading user permissions:', error);
          this._toast.error(
            'Error',
            'Failed to load ' +
              this.selectedUser?.firstName +
              ' permissions - ' +
              error +
              '. Please try again later.'
          );
          this.isLoading = false;
        },
      });
  }

  startAddInstance() {
    if (this.isAddingInstance) {
      if (this.selectedNewInstance) {
        this.validationError =
          'Please confirm the current instance selection before adding another.';
      } else {
        this.validationError =
          'Please select an instance or cancel the current addition before adding another.';
      }
      return;
    }

    this.confirmAddNewInstance = false;
    this.isAddingInstance = true;
    this.searchQuery = '';
    this.selectedNewInstance = null;
    this.validationError = '';
    this.filterInstances();
    this.permissionLevel = 0;
    this.updatePermissionLevel();
  }

  updateSearchQuery(event: Event) {
    this.searchQuery = (event.target as HTMLInputElement).value;
    this.filterInstances();
  }

  filterInstances() {
    if (!this.selectedUser) return;

    const assignedInstanceIds = this.userInstances
      .filter(
        (ui) =>
          !this.instanceChanges.has(ui.instanceId) ||
          this.instanceChanges.get(ui.instanceId)?.changeType !== 'deleted'
      )
      .map((ui) => ui.instanceId);

    this.filteredInstances = this.availableInstances
      .filter((instance) => !assignedInstanceIds.includes(instance.id))
      .filter((instance) =>
        instance.instanceName
          .toLowerCase()
          .includes(this.searchQuery.toLowerCase())
      );
  }

  toggleDropdown(event: Event) {
    event.stopPropagation();
    this.showDropdown = !this.showDropdown;
    if (this.showDropdown) {
      this.filterInstances();
    }
  }

  clearSearchQuery(event: Event) {
    event.stopPropagation();
    this.searchQuery = '';
    this.selectedNewInstance = null;
    this.validationError = '';
  }

  clearSearchQueryAndShowDropdown(event: Event) {
    event.stopPropagation();
    this.searchQuery = '';
    this.selectedNewInstance = null;
    this.validationError = '';
    this.showDropdown = true;
    this.filterInstances();
  }

  selectInstance(instance: Instance) {
    this.selectedNewInstance = instance;
    this.searchQuery = instance.instanceName;
    this.showDropdown = false;
    this.validationError = '';
  }

  updatePermissionLevelFromEvent(event: Event) {
    this.permissionLevel = parseInt((event.target as HTMLInputElement).value);
    this.updatePermissionLevel();
  }

  updatePermissionLevel() {
    switch (this.permissionLevel) {
      case 0:
        this.newInstancePermission = 'Viewer';
        break;
      case 1:
        this.newInstancePermission = 'Editor';
        break;
      case 2:
        this.newInstancePermission = 'Admin';
        break;
      default:
        this.newInstancePermission = 'Viewer';
    }
  }

  updateInstancePermission(instance: UserInstance, event: Event) {
    // Check permission before allowing edit
    if (!this.canEditInstance) {
      this._toast.warning(
        'Permission Denied',
        'You need Editor or Admin permission to edit instance permissions'
      );
      return;
    }

    const level = parseInt((event.target as HTMLInputElement).value);
    //const originalPermission = instance.permissionName;

    this.setPermissionLevelForInstance(instance, level);
    const originalInstance = this.originalUserInstances.find(
      (orig) => orig.instanceId === instance.instanceId
    );

    if (originalInstance) {
      if (originalInstance.permissionName !== instance.permissionName) {
        // below is an update to an existing instance
        this.instanceChanges.set(instance.instanceId, {
          instanceId: instance.instanceId,
          changeType: 'updated',
          originalPermission: originalInstance.permissionName,
          newPermission: instance.permissionName,
        });
      } else {
        // Permission was changed back to original, remove from changes
        this.instanceChanges.delete(instance.instanceId);
      }
    }

    this.checkForChanges();
  }

  async confirmAddInstance() {
    if (!this.selectedUser) return;
    // Validation: Check if an instance is selected
    if (!this.selectedNewInstance) {
      this.validationError = 'Please select an instance';
      return;
    }

    const permissionId =
      await this.accessManagementService.getPermissionIdByName(
        this.newInstancePermission
      );

    const newInstance: UserInstance = {
      instanceId: this.selectedNewInstance.id,
      instanceName: this.selectedNewInstance.instanceName,
      permissionName: this.newInstancePermission,
      permissionId: permissionId,
    };

    // Check if this instance was previously deleted and is being re-added
    const wasDeleted =
      this.instanceChanges.has(this.selectedNewInstance.id) &&
      this.instanceChanges.get(this.selectedNewInstance.id)?.changeType ===
        'deleted';

    if (wasDeleted) {
      // Remove from deleted instances and change tracking
      this.deletedInstances = this.deletedInstances.filter(
        (del) => del.instanceId !== this.selectedNewInstance!.id
      );
      this.instanceChanges.delete(this.selectedNewInstance.id);

      // Find and restore the original instance
      const originalInstance = this.originalUserInstances.find(
        (orig) => orig.instanceId === this.selectedNewInstance!.id
      );
      if (originalInstance) {
        // If permission is same as original, no change needed
        if (originalInstance.permissionName === this.newInstancePermission) {
          // Just restore without marking as changed
          this.userInstances.unshift(originalInstance);
        } else {
          // Mark as updated since permission changed
          this.userInstances.unshift(newInstance);
          this.instanceChanges.set(this.selectedNewInstance.id, {
            instanceId: this.selectedNewInstance.id,
            changeType: 'updated',
            originalPermission: originalInstance.permissionName,
            newPermission: this.newInstancePermission,
          });
        }
      }
    } else {
      // This is a completely new instance
      this.userInstances.unshift(newInstance);
      this.instanceChanges.set(this.selectedNewInstance.id, {
        instanceId: this.selectedNewInstance.id,
        changeType: 'added',
        newPermission: this.newInstancePermission,
      });
    }

    this.isAddingInstance = false;
    this.selectedNewInstance = null;
    this.validationError = '';

    // Ensure changes are tracked properly
    this.checkForChanges();

    // this.isDirty = true;
    // this.emitChangeStatus();
    this.confirmAddNewInstance = true;
  }

  cancelAddInstance() {
    this.isAddingInstance = false;
    this.selectedNewInstance = null;
    this.validationError = '';

    this.searchQuery = '';
    this.showDropdown = false;
  }

  deleteInstance(index: number) {
    // Check permission before allowing delete
    if (!this.canDeleteInstance) {
      this._toast.warning(
        'Permission Denied',
        'You need Admin permission to delete instances'
      );
      return;
    }

    const instanceToDelete = this.userInstances[index];
    const originalInstance = this.originalUserInstances.find(
      (orig) => orig.instanceId === instanceToDelete.instanceId
    );

    if (originalInstance) {
      this.instanceChanges.set(instanceToDelete.instanceId, {
        instanceId: instanceToDelete.instanceId,
        changeType: 'deleted',
        originalPermission: originalInstance.permissionName, // Use original permission, not current
      });

      const deletedInstance: UserInstance = {
        ...originalInstance, // Use original instance data
        permissionName: null,
        permissionId: -1,
      };
      this.deletedInstances.push(deletedInstance);

      this.userInstances.splice(index, 1);
    } else {
      this.instanceChanges.delete(instanceToDelete.instanceId);
      this.userInstances.splice(index, 1);
    }

    this.checkForChanges();
  }

  revertDelete(instanceId: string) {
    // Find the deleted instance
    const deletedInstance = this.deletedInstances.find(
      (del) => del.instanceId === instanceId
    );

    if (deletedInstance) {
      // Find the original instance
      const originalInstance = this.originalUserInstances.find(
        (orig) => orig.instanceId === instanceId
      );

      if (originalInstance) {
        // Check if this instance was previously updated before deletion
        //const hadPreviousUpdate = this.instanceChanges.has(instanceId);

        // Restore the instance to userInstances with original data
        this.userInstances.push({ ...originalInstance });

        // Remove from deleted instances
        this.deletedInstances = this.deletedInstances.filter(
          (del) => del.instanceId !== instanceId
        );
        this.instanceChanges.delete(instanceId);

        this.checkForChanges();
      }
    }
  }

  getPermissionLevelForInstance(instance: UserInstance): number {
    switch (instance.permissionName) {
      case 'Admin':
        return 2;
      case 'Editor':
        return 1;
      case 'Viewer':
      default:
        return 0;
    }
  }

  async setPermissionLevelForInstance(instance: UserInstance, level: number) {
    let permissionName: string;

    switch (level) {
      case 2:
        permissionName = 'Admin';
        break;
      case 1:
        permissionName = 'Editor';
        break;
      case 0:
      default:
        permissionName = 'Viewer';
        break;
    }
    instance.permissionName = permissionName;
    instance.permissionId =
      await this.accessManagementService.getPermissionIdByName(permissionName);
  }

  async onSave() {
    if (!this.selectedUser) return;

    if (this.isAddingInstance) {
      if (!this.selectedNewInstance) {
        this.validationError = 'Please select an instance';
        return;
      }
      // Auto-confirm the adding instance if saving
      await this.confirmAddInstance();
    }
    this.isSaving = true;

    // Get only the changed permissions instead of all permissions
    const changedPermissions = this.getChangedPermissions();

    const saveRequest: SaveUserPermissionsRequest = {
      userId: this.selectedUser.id,
      userName: this.selectedUser.firstName + ' ' + this.selectedUser.lastName,
      currentPermissions: changedPermissions.current,
      originalPermissions: changedPermissions.original,
    };

    this.accessManagementService.saveUserPermissions(saveRequest).subscribe({
      next: (result) => {
        this.isSaving = false;
        if (result) {
          const name = this.selectedUser?.firstName;
          const nameCheck = name
            ? name.endsWith('s')
              ? `${name}'`
              : `${name}'s`
            : '';
          const message = name
            ? `${nameCheck} permissions saved successfully.`
            : 'permissions saved successfully.';
          this._toast.success('Success', message);

          this.loadUserPermissions();
          this.saveChanges.emit(this.userInstances);
        }
      },
      error: (error) => {
        //console.error('Error saving user permissions:', error);
        this.isSaving = false;
        this._toast.error(
          'Error',
          'Failed to save permissions - ' +
            error +
            '.' +
            'Please try again later.'
        );
      },
    });
  }

  private getChangedPermissions(): {
    current: UserInstance[];
    original: UserInstance[];
  } {
    const changedCurrent: UserInstance[] = [];
    const changedOriginal: UserInstance[] = [];

    // Handle deleted instances - always use original data
    this.deletedInstances.forEach((deletedInstance) => {
      changedCurrent.push(deletedInstance);

      const originalInstance = this.originalUserInstances.find(
        (orig) => orig.instanceId === deletedInstance.instanceId
      );
      if (originalInstance) {
        changedOriginal.push(originalInstance);
      }
    });

    // Handle added and updated instances
    this.userInstances.forEach((currentInstance) => {
      const originalInstance = this.originalUserInstances.find(
        (orig) => orig.instanceId === currentInstance.instanceId
      );

      if (!originalInstance) {
        // This is a newly added instance
        changedCurrent.push(currentInstance);
      } else if (
        originalInstance.permissionName !== currentInstance.permissionName ||
        originalInstance.permissionId !== currentInstance.permissionId
      ) {
        // This is an updated instance
        changedCurrent.push(currentInstance);
        changedOriginal.push(originalInstance);
      }
    });

    console.log('Changed permissions only:', {
      current: changedCurrent,
      original: changedOriginal,
    });

    return {
      current: changedCurrent,
      original: changedOriginal,
    };
  }

  getSliderFillWidth(level: number): string {
    const percentage = level * 50;
    return `${percentage}%`;
  }

  confirmDiscardChanges() {
    this.userInstances = JSON.parse(JSON.stringify(this.originalUserInstances));
    this.deletedInstances = [];
    this.instanceChanges.clear(); // Clear change tracking

    this.isDirty = false;
    this.emitChangeStatus();
    this.discardChanges.emit();
    this.showUnsavedChangesWarning = false;
  }

  cancelDiscardChanges() {
    this.cancelUserSwitch.emit();
    this.showUnsavedChangesWarning = false;
  }

  // Helper methods to check change status
  isInstanceAdded(instanceId: string): boolean {
    return (
      this.instanceChanges.has(instanceId) &&
      this.instanceChanges.get(instanceId)?.changeType === 'added'
    );
  }

  isInstanceUpdated(instanceId: string): boolean {
    return (
      this.instanceChanges.has(instanceId) &&
      this.instanceChanges.get(instanceId)?.changeType === 'updated'
    );
  }

  isInstanceDeleted(instanceId: string): boolean {
    return (
      this.instanceChanges.has(instanceId) &&
      this.instanceChanges.get(instanceId)?.changeType === 'deleted'
    );
  }

  getChangeInfo(instanceId: string): InstanceChange | undefined {
    return this.instanceChanges.get(instanceId);
  }

  // to check if there are unsaved changes by comparing with original state
  checkForChanges(): boolean {
    if (this.deletedInstances.length > 0) {
      this.isDirty = true;
    } else if (
      !this.originalUserInstances ||
      this.originalUserInstances.length === 0
    ) {
      this.isDirty = this.userInstances.length > 0;
    } else if (
      this.userInstances.length !== this.originalUserInstances.length
    ) {
      this.isDirty = true;
    } else {
      this.isDirty = false;
      // to check if permissions have changed
      for (let i = 0; i < this.userInstances.length; i++) {
        const current = this.userInstances[i];
        const original = this.originalUserInstances.find(
          (oi) => oi.instanceId === current.instanceId
        );

        if (!original || original.permissionName !== current.permissionName) {
          this.isDirty = true;
          break;
        }
      }
    }

    this.emitChangeStatus();
    if (!this.isDirty) {
      this.showUnsavedChangesWarning = false;
    }
    return this.isDirty;
  }

  emitChangeStatus(): void {
    this.hasUnsavedChanges.emit(this.isDirty);
  }

  checkUnsavedChanges(): boolean {
    return this.isDirty;
  }

  private copyInstancePermissions(instances: UserInstance[]): UserInstance[] {
    // used deep clone to avoid reference issues
    return JSON.parse(JSON.stringify(instances));
  }
}
