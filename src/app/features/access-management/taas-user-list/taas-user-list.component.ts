// taas-user-list.component.ts
import {
  Component,
  Input,
  Output,
  EventEmitter,
  ViewChildren,
  QueryList,
  ElementRef,
  AfterViewInit,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { User } from '../../../core/models/access-management';

@Component({
  selector: 'app-taas-user-list',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './taas-user-list.component.html',
  styleUrl: './taas-user-list.component.scss',
})
export class TaasUserListComponent implements AfterViewInit, OnChanges {
  @Input() users: User[] = [];
  @Input() selectedUserId: string | null = null;
  @Output() userSelected = new EventEmitter<string>();

  @ViewChildren('userItem') userItems!: QueryList<ElementRef<HTMLDivElement>>;

  // Below code is for adjusting the user selected pointer based on which users have multi-line content
  userHeightMap = new Map<string, boolean>();

  ngAfterViewInit() {
    // Check heights after view initialization
    setTimeout(() => this.checkUserItemHeights(), 0);
    this.userItems.changes.subscribe(() => {
      setTimeout(() => this.checkUserItemHeights(), 0);
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    // to check heights when selectedUserId changes
    if (changes['selectedUserId'] && !changes['selectedUserId'].firstChange) {
      setTimeout(() => this.checkUserItemHeights(), 0);
    }
  }

  selectUser(userId: string) {
    this.userSelected.emit(userId);
  }

  private checkUserItemHeights() {
    if (!this.userItems) return;

    this.userItems.forEach((itemRef, index) => {
      const element = itemRef.nativeElement;
      const user = this.users[index];

      if (user && user.id === this.selectedUserId) {
        const originalClasses = element.className;
        element.classList.remove('single-line', 'multi-line');

        const height = element.offsetHeight;
        const isMultiLine = height > 50;

        this.userHeightMap.set(user.id, isMultiLine);
        element.className = originalClasses;
      }
    });
  }

  //to check if a user should have multi-line padding
  isUserMultiLine(userId: string): boolean {
    return this.userHeightMap.get(userId) || false;
  }

  //to check if a user is selected
  isUserSelected(userId: string): boolean {
    return userId === this.selectedUserId;
  }
}
