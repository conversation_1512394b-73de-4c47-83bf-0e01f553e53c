// taas-user-list.component.ts
import {
  Component,
  Input,
  Output,
  EventEmitter,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { User } from '../../../core/models/access-management';

@Component({
  selector: 'app-taas-user-list',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './taas-user-list.component.html',
  styleUrl: './taas-user-list.component.scss',
})
export class TaasUserListComponent {
  @Input() users: User[] = [];
  @Input() selectedUserId: string | null = null;
  @Output() userSelected = new EventEmitter<string>();

  selectUser(userId: string) {
    this.userSelected.emit(userId);
  }
}
