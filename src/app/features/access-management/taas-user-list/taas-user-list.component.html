<div class="user-list" data-testid="userlist.container">
  <div class="user-list-content" data-testid="userlist.content">
    @for (user of users; track user.id) {
    <div #userItem class="user-item" [class.selected]="user.id === selectedUserId"
      (click)="selectUser(user.id)" (keydown.enter)="selectUser(user.id)" (keydown.space)="selectUser(user.id)"
      [attr.data-testid]="'userlist.item.user.' + user.id" tabindex="0" role="button">
      {{ user.firstName }} {{ user.lastName }}
    </div>
    }
  </div>
</div>
