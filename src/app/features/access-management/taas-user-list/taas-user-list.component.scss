.user-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.user-list-header {
  padding: 12px;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.user-list-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
}

.user-list-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden; // Remove horizontal scrollbar
  height: calc(100% - 43px);
  // Remove top padding to eliminate gap
}

.user-item {
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1rem;
  // Fixed height for all users regardless of selection
  padding: 6px;
  min-height: 30px;
  max-height: 32px;
  display: flex;
  align-items: center;
  // Remove left margin/padding to eliminate gap
  margin: 0;
  border: none;
  background-color: transparent;
}

.user-item:hover {
  background-color: #f5f5f5;
}

.user-item.selected {
  position: relative;
  font-weight: bolder;
  color: #0068b4;
  background-color: #b2d0e7!important;
  border-right: none;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    right: -3px;
    width: 0;
    height: 0;
    border-top: 22px solid #f8f9fa;
    border-bottom: 22px solid #f8f9fa;
    border-left: 30px solid #b2d0e7;
    transform: translateY(-50%);
  }

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    width: 0;
    height: 0;
    border-top: 22px solid #f8f9fa;
    border-bottom: 22px solid #f8f9fa;
    border-left: 30px solid #b2d0e7;
    transform: translateY(-50%);
    z-index: 1;
  }
}