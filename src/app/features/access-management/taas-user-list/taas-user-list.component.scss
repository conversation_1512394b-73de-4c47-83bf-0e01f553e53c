.user-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.user-list-header {
  padding: 12px;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.user-list-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
}

.user-list-content {
  flex: 1;
  overflow-y: auto;
  height: calc(100% - 43px);
  padding-top: 20px;
}

.user-item {
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 14px;
  // Default padding
  padding: 10px 16px;
}

.user-item:hover {
  background-color: #f5f5f5;
}

// Conditional padding classes
.user-item.single-line {
  padding: 20px 16px;
}

.user-item.multi-line {
  padding: 10px 16px;
}

.user-item.selected {
  position: relative;
  font-weight: bolder;
  color: #0068b4;
  background-color: #e3f2fd;
  border-right: none;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    right: -3px;
    width: 0;
    height: 0;
    border-top: 30px solid #f8f9fa;
    border-bottom: 30px solid #f8f9fa;
    border-left: 30px solid #e3f2fd;
    transform: translateY(-50%);
  }

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    width: 0;
    height: 0;
    border-top: 30px solid #f8f9fa;
    border-bottom: 30px solid #f8f9fa;
    border-left: 30px solid #e3f2fd;
    transform: translateY(-50%);
    z-index: 1;
  }
}
