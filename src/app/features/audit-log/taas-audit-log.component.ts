import { Component, inject, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
//import { MatDialog } from '@angular/material/dialog';
import { HttpParams } from '@angular/common/http';
import { MatTableDataSource } from '@angular/material/table';
import { MatDatepickerInputEvent } from '@angular/material/datepicker';
//import { Observable } from 'rxjs';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatTableModule } from '@angular/material/table';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatSort, MatSortModule, Sort } from '@angular/material/sort';
import { MatPaginator } from '@angular/material/paginator';
import { MatCardModule } from '@angular/material/card';
import { TaasButtonComponent, TaasModalLoadingSpinnerComponent, ToastService } from 'utimaco-common-ui-angular';
import { TaasAuditLogService } from '../../core/services/audit/taas-audit-log.service';
import { UserRoleService } from '../../core/services/role/user-role.service';
import { Role } from '../../core/models/roles.enum';

export interface usersList {
  userName: string;
  userId: number;
}

export interface functionList {
  functionDetail: string;
  functionId: number;
}

export interface Contact {
  id: number;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  address: string;
}

export interface DateRange {
  startDate: string;
  endDate: string;
}

@Component({
  selector: 'app-taas-audit-log',
  standalone: true,
  imports: [
    MatTableModule,
    MatPaginatorModule,
    MatFormFieldModule,
    MatInputModule,
    CommonModule,
    FormsModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    ReactiveFormsModule,
    MatSortModule,
    MatCardModule,
    TaasButtonComponent,
    TaasModalLoadingSpinnerComponent
  ],
  templateUrl: './taas-audit-log.component.html',
  styleUrl: './taas-audit-log.component.scss',
})
export class TaasAuditLogComponent implements OnInit {
  filterForm!: FormGroup;
  //displayedColumns: string[] = ['auditId','dateTime','userName', 'functionName', 'status','additionalDetails', 'keyName', 'ipAddress'];
  displayedColumns: string[] = [
    'utcDateTime',
    'keyName',
    'action',
    'status',
    'message',
    'userId',
    'requestorIpAddress',
  ];
  dataSource = new MatTableDataSource<Contact>([]);
  users: usersList[] = [];
  functions: functionList[] = [];
  isLoading = false;
  //totalRecords = 0;
  //totalRecords = 1000; // Total number of records to simulate pagination
  logsDataArray: Contact[] = [];
  totalRecords: number = 0;


  @ViewChild(MatSort) sort!: MatSort;
  @ViewChild(MatPaginator) paginator!: MatPaginator;

  sortColumn = 'utcDateTime'; // default sort column
  sortDirection = 'desc'; // default sort direction
  pageIndex: number = 0;
  pageSize: number = 10;
  startDate!: string | Date | null;
  endDate!: string | Date | null;

  // Permission flags
  hasViewerPermission = false;
  hasEditorPermission = false;
  hasAdminPermission = false;
  isOrgAdmin = false;

  private auditLogService = inject(TaasAuditLogService);
  private fb = inject(FormBuilder);
  private _toast = inject(ToastService);
  private userRoleService = inject(UserRoleService);

  // ngAfterViewInit(): void {
  //   console.log('ngAfterViewInit');
  // }


  ngOnInit() {
    this.initializePermissions();
    this.initializeForm();
    this.loadDropdownData();
    this.getAuditLogsByPage();
  }

  /**
   * Initialize user role permissions for audit log access
   * According to the requirements:
   * - All users can see logs of their own actions
   * - Users can see logs for instances they have any permission level for
   * - Org admins can see all logs
   */
  private initializePermissions(): void {
    // Check if user has any instance-level permission
    this.hasViewerPermission = this.userRoleService.hasAnyRole([
      Role.INSTANCE_VIEWER,
      Role.INSTANCE_EDITOR,
      Role.INSTANCE_ADMIN,
      Role.ORG_ADMIN
    ]);

    this.hasEditorPermission = this.userRoleService.hasAnyRole([
      Role.INSTANCE_EDITOR,
      Role.INSTANCE_ADMIN,
      Role.ORG_ADMIN
    ]);

    this.hasAdminPermission = this.userRoleService.hasAnyRole([
      Role.INSTANCE_ADMIN,
      Role.ORG_ADMIN
    ]);

    this.isOrgAdmin = this.userRoleService.hasRole(Role.ORG_ADMIN);
  }

  initializeDatePickerByDefault() {
    this.filterForm.patchValue({
      dateFrom: this.formatDate(this.startDate),
      dateTo: this.formatDate(this.endDate),
    });
  }

  // Initialize the form
  initializeForm() {
    this.filterForm = this.fb.group({
      user: [''],
      function: [''],
      kname: [''],
      dateFrom: [''],
      dateTo: [''],
    });
  }

  // Load dropdown values for users and functions
  loadDropdownData() {
    //TODO: Implement API Requests to fetch below values later.
    // Note: According to audit requirements, users should be able to filter by any user
    // even though they can only see logs for instances they have permission for.
    // The backend will handle the actual filtering based on user permissions.
    this.users = [
      { userId: 1, userName: 'Richard' },
      { userId: 2, userName: 'Alan' },
      { userId: 2, userName: 'admin' },
      // { userId: 3, userName: 'David' },
      // { userId: 4, userName: 'John' },
      // { userId: 5, userName: 'Musk' },
      // { userId: 6, userName: 'Susan' },
    ];
    this.functions = [
      { functionId: 1, functionDetail: 'Add Cloud Instance' },
      { functionId: 2, functionDetail: 'Delete Cloud Instance' },
      { functionId: 3, functionDetail: 'Add Key' },
      { functionId: 4, functionDetail: 'Delete Key' },
      { functionId: 5, functionDetail: 'Edited Key' },
      { functionId: 6, functionDetail: 'Update Cloud Instance' },
      { functionId: 7, functionDetail: 'Test Connection to Cloud Instance' },
      { functionId: 8, functionDetail: 'Upload Key' },
      { functionId: 9, functionDetail: 'Create Client' },
      { functionId: 10, functionDetail: 'GET KMIP Object' },
      { functionId: 11, functionDetail: 'LOCATE KMIP Object' },
      { functionId: 12, functionDetail: 'CREATE KMIP Object' },
      { functionId: 13, functionDetail: 'DESTROY KMIP Object' }
      //{ functionId: 9, functionDetail: 'Login Attempt' },
      //{ functionId: 10, functionDetail: 'Logout' },
      //{ functionId: 11, functionDetail: 'User Created' },
    ];
    // this.http.get<any[]>('/api/users').subscribe(data => this.users = data);
    // this.http.get<any[]>('/api/functions').subscribe(data => this.functions = data);
  }

  //getAuditLogsByPage(page = 0) {
  getAuditLogsByPage() {
    const params = this.createHttpParams();
    //console.log("getAuditLogsByPage-params", params);
    this.isLoading = true;
    // console.log('getAuditLogsByPage-pageindex',this.pageIndex);
    // console.log('getAuditLogsByPage-pagesize',this.pageSize);
    this.auditLogService.getAuditLogs(params).subscribe({
      next: (response) => {
        try {
          if (response?.body?.data) {
            //console.log("data in", response.body);
            this.logsDataArray = response.body.data.auditLogs.content || [];
            //console.log("logsDataArray", this.logsDataArray);

            this.dataSource = new MatTableDataSource<Contact>(
              this.logsDataArray
            );
            this.totalRecords =
              response.body.data.auditLogs.page.totalElements || 0;
            this.startDate = response.body.data.startDate;
            this.endDate = response.body.data.endDate;
            // console.log("totalRecordCount", this.totalRecords);
            // console.log("this.startDate", this.startDate);
            // console.log("endDate", this.endDate);
            this.initializeDatePickerByDefault();
          } else {
            this.logsDataArray = [];
            this.dataSource = new MatTableDataSource<Contact>([]);
            this.totalRecords = 0;
            //console.warn('No data received from the server');
          }
        } catch (error) {
          //console.error('Error processing response:', error);
          this.handleError('Error processing data');
        }
      },
      error: (error) => {
        //console.error('API Error:', error);
        this.handleError(this.getErrorMessage(error));
      },
      complete: () => {
        //console.log('Request completed');
        this.isLoading = false;
      },
    });
  }

  private handleError(message: string) {
    this.isLoading = false;
    this.logsDataArray = [];
    this.dataSource = new MatTableDataSource<Contact>([]);
    this.totalRecords = 0;
    //console.log(message);
    this._toast.error('Error', message);

    //TODO: Use UTI Common Lib toast
  }

  private getErrorMessage(error: any): string {
    // TODO: Infer correct interface for this type
    if (error.status === 0) {
      return 'Unable to connect to the server. Please check your internet connection.';
    } else if (error.status === 404) {
      return 'The requested resource was not found.';
    } else if (error.status === 500) {
      return 'Internal server error. Please try again later.';
    } else {
      return error.message || 'An unexpected error occurred.';
    }
  }

  onSortChange(event: Sort) {
    this.sortColumn = event.active;
    this.sortDirection = event.direction;
    this.pageIndex = 0;
    this.pageSize = 10;
    if (this.paginator) {
      this.paginator.pageIndex = 0;
      this.paginator.pageSize = 10;
    }
    this.getAuditLogsByPage();
  }
  onPageChange(event: PageEvent) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getAuditLogsByPage();
  }
  // Create HTTP params (including filters, sorting, and pagination)
  //createHttpParams(page: number): HttpParams {
  createHttpParams(): HttpParams {
    let params = new HttpParams()

      .set('page', this.pageIndex)
      .set('size', this.pageSize) // Add pagination size
      .set('sortField', this.sortColumn) // Include sorting information
      .set('sortOrder', this.sortDirection);

    const formValue = this.filterForm.value;
    //console.log('formvalue',formValue);

    const userName1 = '<EMAIL>';
    const userName2 = '<EMAIL>';
    if (formValue.user == 'Richard') {
      formValue.user = userName2;
    }
    if (formValue.user == 'Alan') {
      formValue.user = userName1;
    }

    params = params.set('user', formValue.user?.toString() ?? '');
    params = params.set('action', formValue.function?.toString() ?? '');
    params = params.set('keyName', formValue.kname?.toString() ?? '');
    params = params.set(
      'dateFrom',
      this.formatDate(formValue.dateFrom)?.toString() ?? ''
    );
    params = params.set(
      'dateTo',
      this.formatDate(formValue.dateTo)?.toString() ?? ''
    );

    //console.log('entered - params',params);

    return params;
  }

  private formatDate(date: Date | string | null): string {
    if (!date) return '';

    // To format the date as YYYY-MM-DD
    if (typeof date === 'string') {
      return date;
    }
    // console.log('dateFromformat',`${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
      2,
      '0'
    )}-${String(date.getDate()).padStart(2, '0')}`;
  }

  // Handle date change (for validation)
  onDateChange(event: MatDatepickerInputEvent<Date>, isFromDate: boolean) {
    const { dateFrom, dateTo } = this.filterForm.value;
    if (isFromDate && dateTo && event.value && event.value > new Date(dateTo)) {
      this._toast.error(
        'Invalid DateFrom',
        "Date From can't be later than Date To"
      );
      this.filterForm.patchValue({ dateFrom: null });
    }
    if (
      !isFromDate &&
      dateFrom &&
      event.value &&
      event.value < new Date(dateFrom)
    ) {
      this._toast.error(
        'Invalid DateTo',
        "Date To can't be earlier than Date From"
      );
      this.filterForm.patchValue({ dateTo: null });
    }
  }

  // Reset form filters
  resetFilters() {
    this.filterForm.reset();
    const formValue = this.filterForm.value;

    this.pageIndex = 0;
    this.pageSize = 10;
    if (this.paginator) {
      this.paginator.pageIndex = 0;
      this.paginator.pageSize = 10;
    }
    formValue.user = '';
    formValue.function = '';
    formValue.kname = '';
    formValue.dateFrom = '';
    formValue.dateTo = '';

    // Reset component's sort properties
    this.sortColumn = 'utcDateTime';
    this.sortDirection = 'desc';
    // Reset sorting
    if (this.sort) {
      this.sort.active = 'utcDateTime';
      this.sort.direction = 'desc';
    }

    this.isLoading = true;
    this.getAuditLogsByPage();
  }

  onFilterChange() {
    // Reset component's sort properties
    this.sortColumn = 'utcDateTime';
    this.sortDirection = 'desc';
    // Reset sorting
    if (this.sort) {
      this.sort.active = 'utcDateTime';
      this.sort.direction = 'desc';
    }

    this.pageIndex = 0;
    this.pageSize = 10;
    if (this.paginator) {
      this.paginator.pageIndex = 0;
      this.paginator.pageSize = 10;
    }
    this.isLoading = true;
    this.getAuditLogsByPage();
  }
}
