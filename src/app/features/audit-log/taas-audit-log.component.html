<!-- <h4>Audit Logs</h4> -->
<mat-card class="filter-card" data-testid="auditlogs.card.filter">
    <div class="filter-container custom-elevation">
        <mat-card-content>
            <form [formGroup]="filterForm" data-testid="auditlogs.form.filter">
                <mat-form-field appearance="outline" class="filter-field" data-testid="auditlogs.formfield.user">
                    <!-- TODO: add i18 support -->
                    <mat-label>User</mat-label>
                    <input matInput formControlName="user" data-testid="auditlogs.input.user" />
                </mat-form-field>

                <mat-form-field appearance="outline" class="filter-field" data-testid="auditlogs.formfield.operation">
                    <!-- TODO: add i18 support-->
                    <mat-label>Operation Type</mat-label>
                    <mat-select formControlName="function" data-testid="auditlogs.select.function">
                        @for (fn of functions; track fn.functionDetail) {
                        <mat-option [value]="fn.functionDetail" attr.data-testid="auditlogs.option.function.{{fn.functionDetail}}">
                            {{ fn.functionDetail }}
                        </mat-option>
                        }
                    </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline" class="filter-field" data-testid="auditlogs.formfield.keyname">
                    <mat-label>Key Name</mat-label>
                    <input matInput formControlName="kname" data-testid="auditlogs.input.keyname" />
                </mat-form-field>

                <mat-form-field appearance="outline" class="filter-field" data-testid="auditlogs.formfield.datefrom">
                    <mat-label>From</mat-label>
                    <input matInput [matDatepicker]="pickerFrom" formControlName="dateFrom"
                        (dateChange)="onDateChange($event, true)" data-testid="auditlogs.input.datefrom" />
                    <mat-datepicker-toggle matSuffix [for]="pickerFrom" data-testid="auditlogs.toggle.datefrom"></mat-datepicker-toggle>
                    <mat-datepicker #pickerFrom data-testid="auditlogs.datepicker.from"></mat-datepicker>
                </mat-form-field>

                <mat-form-field appearance="outline" class="filter-field" data-testid="auditlogs.formfield.dateto">
                    <mat-label>To</mat-label>
                    <input matInput [matDatepicker]="pickerTo" formControlName="dateTo"
                        (dateChange)="onDateChange($event, false)" data-testid="auditlogs.input.dateto" />
                    <mat-datepicker-toggle matSuffix [for]="pickerTo" data-testid="auditlogs.toggle.dateto"></mat-datepicker-toggle>
                    <mat-datepicker #pickerTo data-testid="auditlogs.datepicker.to"></mat-datepicker>
                </mat-form-field>

                <div class="button-row" data-testid="auditlogs.container.buttons">
                    <!-- TODO: add i18 support by using content projection instead of buttonText attribute -->
                    <lib-taas-button buttonText="Apply Filter" buttonType="primary"
                        (buttonPressedEvent)="onFilterChange()" data-testid="auditlogs.button.applyfilter"></lib-taas-button>
                    <lib-taas-button buttonText="Reset Filter" buttonType="primary"
                        (buttonPressedEvent)="resetFilters()" data-testid="auditlogs.button.resetfilter"></lib-taas-button>
                </div>
            </form>
        </mat-card-content>
    </div>
</mat-card>

<div tabindex="0" data-testid="auditlogs.container.table">
    <lib-taas-modal-loading-spinner uniqueId="auditLogLoadingSpinner" width="300px" height="300px" [spinnerVisible]="isLoading"></lib-taas-modal-loading-spinner>
    @if(!isLoading && logsDataArray.length === 0){
        <div class="no-data-message" data-testid="auditlogs.message.nodata">
          <!--TODO: add i18 support -->
            No audit records found for the past 30 days.
        </div>
    }
    <!--TODO: replace with COmmon UI Lib Sortable table-->
    <table mat-table [dataSource]="dataSource" (matSortChange)="onSortChange($event)" matSort data-testid="auditlogs.table.logs">
        <ng-container matColumnDef="userId">
            <th mat-header-cell *matHeaderCellDef class="header-bold flex-column" mat-sort-header data-testid="auditlogs.header.cell.userId">User</th>
            <td mat-cell *matCellDef="let row; let i = index" attr.data-testid="auditlogs.row.{{i}}.cell.userId">{{ row.userId }}</td>
        </ng-container>

        <ng-container matColumnDef="action">
            <th mat-header-cell *matHeaderCellDef class="header-bold flex-column" mat-sort-header data-testid="auditlogs.header.cell.action">Operation</th>
            <td mat-cell *matCellDef="let row; let i = index" attr.data-testid="auditlogs.row.{{i}}.cell.action">{{ row.action }}</td>
        </ng-container>

        <ng-container matColumnDef="keyName">
            <th mat-header-cell *matHeaderCellDef class="header-bold flex-column" mat-sort-header data-testid="auditlogs.header.cell.keyName">Key Name</th>
            <td mat-cell *matCellDef="let row; let i = index" attr.data-testid="auditlogs.row.{{i}}.cell.keyName">{{ row.keyName }}</td>
        </ng-container>

        <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef class="header-bold flex-column" mat-sort-header data-testid="auditlogs.header.cell.status">Status</th>
            <td mat-cell *matCellDef="let row; let i = index" attr.data-testid="auditlogs.row.{{i}}.cell.status">{{ row.status }}</td>
        </ng-container>

        <ng-container matColumnDef="utcDateTime">
            <th mat-header-cell *matHeaderCellDef class="header-bold flex-column" mat-sort-header data-testid="auditlogs.header.cell.utcDateTime">Date & Time</th>
            <td mat-cell *matCellDef="let row; let i = index" attr.data-testid="auditlogs.row.{{i}}.cell.utcDateTime">{{ row.utcDateTime | date:'yyyy-MM-dd HH:mm:ss' }}</td>
        </ng-container>

        <ng-container matColumnDef="message">
            <th mat-header-cell *matHeaderCellDef class="header-bold flex-column" mat-sort-header data-testid="auditlogs.header.cell.message">Message</th>
            <td mat-cell *matCellDef="let row; let i = index" attr.data-testid="auditlogs.row.{{i}}.cell.message">{{ row.message }}</td>
        </ng-container>

        <ng-container matColumnDef="requestorIpAddress">
            <th mat-header-cell *matHeaderCellDef class="header-bold flex-column" mat-sort-header data-testid="auditlogs.header.cell.requestorIpAddress">IP Address</th>
            <td mat-cell *matCellDef="let row; let i = index" attr.data-testid="auditlogs.row.{{i}}.cell.requestorIpAddress">{{ row.requestorIpAddress }}</td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns" data-testid="auditlogs.row.header"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns; let i = index" attr.data-testid="auditlogs.row.{{i}}"></tr>
    </table>

    <mat-paginator
        (page)="onPageChange($event)"
        [pageIndex]="pageIndex"
        [pageSizeOptions]="[10, 20, 30]"
        [length]="totalRecords"
        showFirstLastButtons
        aria-label="Select page of periodic elements"
        data-testid="auditlogs.paginator">
    </mat-paginator>
</div>
