.role-visibility-notice {
  background-color: #f0f8ff;
  border-left: 4px solid #2196f3;
  padding: 12px 20px;
  margin-bottom: 20px;
  margin-top: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  p {
    margin: 0;
    color: #0c5494;
    font-size: 14px;
  }
}

.filter-field{
    padding-top: 10px;
    width: 300px;
    margin: 5px;
  
  }
  .filter-card{
    margin-top: 10px;
    margin-bottom: 40px;
    //border: 1px solid black; 
    border-radius: 8px; 
  }
  form{
   // padding-top: 10px;
    padding-left: 10px;
  }
  
  
  .mat-mdc-card, .md-card, .mat-mdc-card-content {
    border-radius: 8px!important;
  }
  
  .button-row{
    direction:rtl;
    float: inline-end;
    margin-bottom: 20px;
    margin-right: 20px;
    margin-top: 10px;
  }
  
  
  lib-taas-button{
     padding-left: 10px;
  }
  .header-bold {
    font-weight: bold;
  }
  
  .mat-elevation-z7{
    border-radius: 8px; 
    //overflow: hidden; 
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.16), 0px 1px 3px rgba(0, 0, 0, 0.23);
  }
  .custom-elevation1{
    border-radius: 8px;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2), 0px 2px 5px rgba(0, 0, 0, 0.25);
  }
  .custom-elevation {
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1), 0px 2px 4px rgba(0, 0, 0, 0.12);
  border-radius: 8px; 
  transition: box-shadow 0.3s ease-in-out; 
  }
  
  .custom-elevation:hover {
    box-shadow: 0px 6px 14px rgba(0, 0, 0, 0.2), 0px 4px 6px rgba(0, 0, 0, 0.15); 
  }
  
  .mat-table {
    box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.1), 0px 1px 3px rgba(0, 0, 0, 0.12);
    border-radius: 8px; 
  }
  
  .mat-table th, .mat-table td {
    border-top: 1px solid #e0e0e0; 
    border-bottom: 1px solid #e0e0e0;
  }
  
  // .mat-table tr:hover {
  //   background-color: red;
  // }
  
  
  // .mat-table {
  //   border-radius: 8px; 
  // }
  
  .mat-elevation-z8{
    border-radius: 8px; 
    overflow: hidden; 
    overflow-x: auto;
  }
  
  
  // .mat-elevation-z8 {
  //   overflow-x: auto;
  // }
  
  // .mat-table {
  //   width: 100%;
  //   min-width: 1200px; /* Adjust based on your total column width */
  // }
  
  // .mat-table-responsive {
  //   display: block;
  //   overflow-x: auto;
  //   width: 100%;
  // }
  
  .no-data-message{
    padding: 20px;
    text-align: center;
  }

  .spin-width{
    margin: 20px;
  }