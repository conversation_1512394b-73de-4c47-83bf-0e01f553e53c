import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnDestroy,
} from '@angular/core';
import { TaasCloudInstanceService } from '../../core/services/cloud/taas-cloud-instance.service';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { FormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { CommonModule } from '@angular/common';
import { TaasStepperViewComponent } from '../stepper-view/taas-stepper-view.component';
import {
  TaasModalDialogueComponent,
  ToastService,
} from 'utimaco-common-ui-angular';

@Component({
  selector: 'app-taas-upload-key',
  templateUrl: './taas-upload-key.component.html',
  styleUrls: ['./taas-upload-key.component.scss'],
  imports: [
    MatIcon,
    MatIconModule,
    MatFormFieldModule,
    FormsModule,
    CommonModule,
    MatDialogModule,
    TaasStepperViewComponent,
  ],
  standalone: true,
})
export class TaasUploadKeyComponent implements OnInit, OnDestroy {
  constructor(
    private cloudInstanceService: TaasCloudInstanceService,
    public dialog: MatDialog,
    private _toast: ToastService
  ) {}
  @Output() cancel: EventEmitter<any> = new EventEmitter();
  @Input() cloudInstance: any;
  @Input() selectedCloudType: any;
  @Input() modalReference!: TaasModalDialogueComponent;
  selectedKeyType: any = { name: 'Create Key & Upload', id: 1, checked: true };
  titleTemp!: string;

  //eslint: removing for now as I don't see any purpose
  ngOnInit(): void {
    this.titleTemp = this.modalReference.title;
    this.modalReference.title += this.cloudInstance.instanceName;
    this.modalReference.title += `<img src="assets/icons/${this.cloudInstance.cloudType}.svg" class="rowIcon btnPadding" alt="type"/>`;
    this.modalReference.title += ' ' + this.cloudInstance.cloudType;
  }

  ngOnDestroy() {
    this.modalReference.title = this.titleTemp;
  }

  onCancelModal() {
    //cancel event emit
    this.cancel.emit();
  }

  onKeyTypeChange(event: any) {
    this.selectedKeyType = event;
  }
}
