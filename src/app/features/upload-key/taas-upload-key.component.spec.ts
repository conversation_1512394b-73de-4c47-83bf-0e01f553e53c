import { ComponentFixture, TestBed } from '@angular/core/testing';

import { UploadKeyComponent } from './taas-upload-key.component';

describe('UploadKeyComponent', () => {
  let component: UploadKeyComponent;
  let fixture: ComponentFixture<UploadKeyComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ UploadKeyComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(UploadKeyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
