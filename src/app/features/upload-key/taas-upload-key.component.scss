
::ng-deep{
  .rowIcon{
    width:15px;
    height: 15px;
  }
}
.rowIconBig{
    width:20px;
    height: 20px;
    float: inline-end;
    border: none;
}
.btnPadding{
    margin-bottom: 4px;
    margin-left: 2px;
}
.iconFont{
    font-size:20px;
    cursor: pointer;
}
.rowIconBig{
    width:20px;
    height: 20px;
}
.helpFont{
    font-size:22px;
    cursor: pointer;
}

// .custom-modal-overlay {
//     position:fixed;
//     top: 0;
//     left: 0;
//     width: 100%;
//     height: 100%;
//     background-color: rgba(0, 0, 0, 0.5);
//     display: block;
//   }

  .modaltop{
    margin: 80px;
  }

  .modal-content {
    background-color: white;
    border-radius: 8px;
    //padding: 20px;
    // max-width: 500px;
    // width: 100%;
    max-height: 80vh;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    overflow: hidden;
  }

  .modal-header {
    padding: 10px;
    border-bottom: 1px solid #ddd;
  }

  .modal-body-wrapper {
    overflow-y: auto;
    max-height: 60vh;
    padding-right: 10px;
  }


  .modal-body-wrapper::-webkit-scrollbar {
    width: 8px;
  }

  .modal-body-wrapper::-webkit-scrollbar-thumb {
    border-radius: 4px;
  }

  .modal-body-wrapper::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    border-radius: 4px;
  }

  .modal-body-wrapper {
    scrollbar-width: thin;
  }
