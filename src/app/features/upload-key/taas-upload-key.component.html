<!--<div class="modal-content">
  <div class="modal-header d-block">
    @if (selectedCloudType.uploadKeyEnabled) {
    <span i18n="Upload Key: Header">
      Upload to {{ cloudInstance.instanceName }}
      <img
        src="assets/icons/{{ cloudInstance.cloudType }}.svg"
        class="rowIcon btnPadding"
        alt="type"
      />
      {{ " " + cloudInstance.cloudType }}
    </span>
    } @if (!selectedCloudType.uploadKeyEnabled) {
    <span i18n="Upload Key: Text for Create Key">Create Key</span>
    }
  </div>-->
<!-- header block -->
<div>
  <!-- stepper view on modal -->
  <app-taas-stepper-view
    [cloudInstance]="cloudInstance"
    [selectedCloudType]="selectedCloudType"
    (keyTypeChange)="onKeyTypeChange($event)"
    (cancel)="onCancelModal()"
  ></app-taas-stepper-view>
</div>
