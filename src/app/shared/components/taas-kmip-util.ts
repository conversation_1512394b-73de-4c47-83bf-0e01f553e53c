import { AbstractControl } from "@angular/forms";


export function downloadClientCert(csrData: string,clientId:string): void {
  console.log('downloadClient', csrData);
  //const csrData = kmipClient.csr;
  let formattedPEM = '';
  if (
    csrData.includes('-----BEGIN CERTIFICATE-----') &&
    csrData.includes('-----END CERTIFICATE-----')
  ) {
    const contentMatch = csrData.match(
      /-----BEGIN CERTIFICATE-----\s*([\s\S]*?)\s*-----END CERTIFICATE-----/
    );

    if (contentMatch && contentMatch[1]) {
      // removing all whitespace
      const base64Content = contentMatch[1].replace(/[\r\n\s]+/g, '');
      formattedPEM = '-----BEGIN CERTIFICATE-----\n';
      for (let i = 0; i < base64Content.length; i += 64) {
        formattedPEM += base64Content.substring(i, i + 64) + '\n';
      }
      formattedPEM += '-----<PERSON><PERSON> CERTIFICATE-----\n';
    } else {
      formattedPEM = csrData;
    }
  } else {
    const base64Content = csrData.replace(/[\r\n\s]+/g, '');
    formattedPEM = '-----BEGIN CERTIFICATE-----\n';

    // Add base64 content with line breaks every 64 characters
    for (let i = 0; i < base64Content.length; i += 64) {
      formattedPEM += base64Content.substring(i, i + 64) + '\n';
    }
    formattedPEM += '-----END CERTIFICATE-----\n';
  }
  const blob = new Blob([formattedPEM], { type: 'application/x-pem-file' });
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `${clientId}-certificate.pem`;
  document.body.appendChild(a);
  a.click();

  window.URL.revokeObjectURL(url);
  document.body.removeChild(a);
}


export function handleCertificatePaste(
  event: ClipboardEvent,
  formControl: AbstractControl | null
): string | null {
  event.preventDefault();
  const pastedText = event.clipboardData?.getData('text');

  if (pastedText) {
    const strippedCertificate = pastedText.replace(/[\r\n]+/g, '');
    console.log('pastedText', pastedText);
    console.log('strippedCertificate', strippedCertificate);

    if (formControl) {
      formControl.setValue(strippedCertificate);
    }

    return strippedCertificate;
  }

  return null;
}

// onPasteCertificate(event: ClipboardEvent): void {
//   event.preventDefault();
//   const pastedText = event.clipboardData?.getData('text');
//   if (pastedText) {
//     const strippedCertificate = pastedText.replace(/[\r\n]+/g, '');
//     console.log('pastedText', pastedText);
//     console.log('strippedCertificate', strippedCertificate);
//     this.changeCertificateForm.get('newCertificate')?.setValue(strippedCertificate);
//   }
// }
