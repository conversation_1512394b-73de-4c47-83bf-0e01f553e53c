import { FieldKeyTranslations } from './models/field-key-translations';
import { LabelTranslations } from './models/label-translations';
import { ValidationMessages } from './models/validation-messages';

export const VALIDATION_MESSAGES: ValidationMessages = {
  FIELD_REQUIRED: $localize`:Field Required Validation Msg@@field_required:Field Required`,
  VALIDATION_CHECK_MSG: $localize`:Msg for Validation Check@@validation_msg:Value should be less than `,
  CHARACTER: $localize`:Value Comparison text Msg@@character: characters`,
  UNIQUE_CHECK: $localize`:Validate multi-select or value-array input@@uniqueness_validation: Field values must be unique`,
};

export const FIELD_KEY_TRANSLATIONS: FieldKeyTranslations = {
  enabled: $localize`:Enabled field@@field.enabled:Enabled`,
  keyVault: $localize`:KeyVault field@@field.keyVault:Key Vault`,
  activationDate: $localize`:Activatin Date field@@field.activationDate:Activation Date`,
  expirationDate: $localize`:Expiration Date field@@field.expirationDate:Expiration Date`,
  tags: $localize`:Tags field@@field.tags:Tags`,
};

export const LABEL_TRANSLATIONS: LabelTranslations = {
  tenantID: $localize`:Tenant ID label@@field.tenantId:Tenant ID`,
  clientID: $localize`:Client ID label@@field.clientId:Client ID`,
  clientSecret: $localize`:Client Secret label@@field.clientSecret:Client Secret`,
  keyVaults: $localize`:Key Vault label@@field.keyVaults:Key Vaults`,
  // ... other field translations
};
