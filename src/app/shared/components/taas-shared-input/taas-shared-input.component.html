<!-- render text-field -->
<div *ngIf="inputField.type === 'text-field'">
  <input
    class="largeInput"
    [(ngModel)]="selectedValue"
    autocomplete="off"
    [disabled]="disabled"
    (blur)="onValueChange($event, inputField.type)"
    title="{{ inputField.hint }}"
    id="input_{{ inputField.jsonField }}"
    placeholder="{{ getLocalizedPlaceholder(inputField) }}"
  />
  <!-- placeholder="{{inputField.hint}}"> -->
</div>

<!-- render password-field -->
<div *ngIf="inputField.type === 'password-field'" class="inputBorder">
  <input
    type="{{ inputPassword.type }}"
    class="largePasswordInput"
    [(ngModel)]="selectedValue"
    autocomplete="off"
    [disabled]="disabled"
    (blur)="onValueChange($event, inputField.type)"
    title="{{ inputField.hint }}"
    id="input_{{ inputField.jsonField }}"
    placeholder="{{ inputField.hint }}"
  />
  <mat-icon
    class="showPasswordIcon"
    *ngIf="!inputPassword.showPassword"
    (click)="passwordToggle(true)"
    >visibility</mat-icon
  >
  <mat-icon
    class="showPasswordIcon"
    *ngIf="inputPassword.showPassword"
    (click)="passwordToggle(false)"
    >visibility_off</mat-icon
  >
</div>

<!-- render password-field no show on edit -->
<div *ngIf="inputField.type === 'password-field-noshow'" class="inputBorder">
  <input
    type="{{ inputPassword.type }}"
    class="largePasswordInput"
    [(ngModel)]="selectedValue"
    autocomplete="off"
    [disabled]="disabled"
    (blur)="onValueChange($event, inputField.type)"
    title="{{ inputField.hint }}"
    id="input_{{ inputField.jsonField }}"
    placeholder="{{ inputField.hint }}"
  />
  <mat-icon
    class="showPasswordIcon"
    *ngIf="inputPassword.showPassword"
    (click)="passwordToggle(false)"
    >visibility_off</mat-icon
  >
</div>

<!-- render text-area -->
<div *ngIf="inputField.type === 'text-area'">
  <textarea
    class="largeInput"
    [(ngModel)]="selectedValue"
    autocomplete="off"
    [disabled]="disabled"
    (blur)="onValueChange($event, inputField.type)"
    title="{{ inputField.hint }}"
    id="input_{{ inputField.jsonField }}"
    placeholder="{{ getLocalizedPlaceholder(inputField) }}"
  ></textarea>
  <!-- placeholder="{{inputField.hint}}"></textarea> -->
</div>

<!-- render date-picker -->
<div *ngIf="inputField.type === 'date-picker'">
  <input
    type="date"
    class="form-control largeInput"
    placeholder="Select Date"
    [(ngModel)]="selectedValue"
    autocomplete="off"
    [disabled]="disabled"
    (blur)="onValueChange($event, inputField.type)"
    title="{{ inputField.hint }}"
    placeholder="{{ inputField.hint }}"
    id="input_{{ inputField.jsonField }}"
  />
</div>

<!-- render date-time-picker -->
<div
  *ngIf="inputField.type === 'date-time-picker'"
  [ngClass]="{ disabled: disabled }"
>
  <div class="form-control">
    <input
      type="datetime-local"
      class="largeInput"
      [disabled]="disabled"
      [(ngModel)]="timeObject.time"
      autocomplete="off"
      id="input_{{ inputField.jsonField }}"
      (blur)="onDateTimezoneChange($event, inputField.type, 'time')"
      title="{{ inputField.hint }}"
      placeholder="{{ inputField.hint }}"
    />
    <label class="row">
      <span *ngIf="inputField.required">*</span>
      <span i18n="Shared-Input: timezone text"
        >{{ getLocalizedDateTimeLabel(inputField) }} Timezone</span
      >
      <select
        class="largeInput"
        name="timeZone"
        [(ngModel)]="selectedTz"
        (change)="onDateTimezoneChange($event, inputField.type, 'timezone')"
      >
        <option value="">Select</option>
        <option *ngFor="let tz of timezones | keyvalue" [value]="tz.key">
          {{ "(GMT" + tz.value + ") " + tz.key }}
        </option>
      </select>
    </label>
  </div>
</div>

<!-- render name-value-array -->
<div
  *ngIf="inputField.type === 'name-value-array'"
  [ngClass]="{ disabled: disabled }"
>
  <div class="tags-row">
    <div>
      <label>Name</label>
    </div>
    <div class="leftInputPadding">
      <label>Value</label>
    </div>
    <div>
      <mat-icon id="iconAddValArr" class="addNameValue" (click)="addNameValue()"
        >add_circle</mat-icon
      >
    </div>
  </div>
  <div>
    <div *ngFor="let val of selectedValue; let i = index" class="tags-row">
      <div>
        <input
          type="text"
          class="largeInput"
          [disabled]="disabled"
          [(ngModel)]="val.name"
          id="name{{ val }}"
          (blur)="onListValueChange(i, inputField.type)"
        />
      </div>
      <div class="leftInputPadding">
        <input
          type="text"
          class="form-control largeInput"
          [disabled]="disabled"
          [(ngModel)]="val.value"
          id="value{{ val }}"
          (blur)="onListValueChange(i, inputField.type)"
        />
      </div>
      <div>
        <mat-icon
          id="iconRemValArr{{ val }}"
          class="removeNameValue"
          (click)="removeNameValue(val, i, inputField.type)"
          >remove_circle</mat-icon
        >
      </div>
    </div>
  </div>
</div>

<!-- render select dd -->
<div *ngIf="inputField.type === 'select'">
  <select
    class="largeInput"
    *ngIf="!selectedValue || opList.includes(selectedValue)"
    (change)="onValueChange($event, inputField.type)"
    [(ngModel)]="selectedValue"
    [disabled]="disabled"
    id="input_{{ inputField.jsonField }}"
  >
    <option value="">Select</option>
    <option *ngFor="let option of inputField.values" [value]="option.id">
      {{ option.value }}
    </option>
  </select>
  <!-- handle if value is not present in list -->
  <input
    *ngIf="selectedValue && !opList.includes(selectedValue)"
    class="largeInput"
    [(ngModel)]="selectedValue"
    autocomplete="off"
    [disabled]="true"
    title="{{ inputField.hint }}"
    id="input_{{ inputField.jsonField }}"
  />
</div>

<!-- render checkbox -->
<div *ngIf="inputField.type === 'yes-no'">
  <input
    type="checkbox"
    class="form-checkbox"
    (change)="onCheckboxChange($event, inputField.type)"
    [(ngModel)]="selectedValue"
    autocomplete="off"
    [disabled]="disabled"
    id="input_{{ inputField.jsonField }}"
  />
</div>

<!-- render value-array -->
<div *ngIf="inputField.type === 'value-array'">
  <div>
    <div class="leftInputPadding">
      <label class="listLabel"
        ><span *ngIf="inputField.required">*</span>{{ inputLabel }}</label
      >
    </div>
  </div>
  <div>
    <div *ngFor="let val of selectedValue; let i = index">
      <div class="leftInputPadding">
        <textarea
          type="text"
          class="form-control largeTextArea"
          [disabled]="disabled"
          [ngModel]="val"
          id="value{{ val }}"
          (blur)="onValueListChange(i, inputField.type, $event)"
        ></textarea>
      </div>
      <div>
        <mat-icon
          *ngIf="!disableRemove"
          id="iconRemNameVal{{ val }}"
          class="removeNameValue"
          (click)="removeValue(val, i, inputField.type, 'old')"
          >remove_circle</mat-icon
        >
      </div>
    </div>
    <div *ngFor="let val of listNewValue; let i = index">
      <div class="leftInputPadding">
        <textarea
          type="text"
          class="form-control largeTextArea"
          [ngModel]="val"
          id="value{{ val }}"
          (blur)="onValueListChange(i, inputField.type, $event)"
        ></textarea>
      </div>
      <div>
        <mat-icon
          id="iconRemSelVal{{ val }}"
          class="removeNameValue"
          (click)="removeValue(val, i, inputField.type, 'new')"
          >remove_circle</mat-icon
        >
      </div>
    </div>
  </div>

  <div>
    <div class="leftInputPadding"></div>
    <div>
      <mat-icon
        *ngIf="!disableAdd"
        id="iconAddNameVal"
        class="addNameValue"
        (click)="addValue()"
        >add_circle</mat-icon
      >
    </div>
  </div>
</div>

<!-- name-value_editable-array -->
<div *ngIf="inputField.type === 'name-value_editable-array'">
  <div>
    <div>
      <label class="listLabel">Key</label>
    </div>
    <div class="leftInputPadding">
      <label class="listLabel">Value</label>
    </div>
  </div>
  <div>
    <div *ngFor="let val of selectedValue; let i = index">
      <div>
        <input
          type="text"
          class="form-control largeInput"
          [disabled]="true"
          id="nname{{ val }}"
          [(ngModel)]="val.name"
          id="name{{ val }}"
        />
      </div>
      <div class="leftInputPadding">
        <input
          type="text"
          class="form-control largeInput"
          [disabled]="disabled"
          id="nvalue{{ val }}"
          [(ngModel)]="val.value"
          id="value{{ val }}"
          (blur)="onValueListChange(i, inputField.type, $event, 'value', 'old')"
        />
      </div>
      <div>
        <mat-icon
          *ngIf="!disableRemove"
          id="iconRemNameVal{{ val }}"
          class="removeNameValue"
          (click)="removeValue(val, i, inputField.type, 'old')"
          >remove_circle</mat-icon
        >
      </div>
    </div>
    <div *ngFor="let val of listNewValue; let i = index">
      <div>
        <input
          type="text"
          class="form-control largeInput"
          [(ngModel)]="val.name"
          id="mname{{ val }}"
          (blur)="onValueListChange(i, inputField.type, $event, 'name')"
        />
      </div>
      <div class="leftInputPadding">
        <input
          type="text"
          class="form-control largeInput"
          [(ngModel)]="val.value"
          id="mvalue{{ val }}"
          (blur)="onValueListChange(i, inputField.type, $event, 'value')"
        />
      </div>
      <div>
        <mat-icon
          id="iconRemSelVal{{ val }}"
          class="removeNameValue"
          (click)="removeValue(val, i, inputField.type, 'new')"
          >remove_circle</mat-icon
        >
      </div>
    </div>
  </div>

  <div>
    <div class="leftInputPadding"></div>
    <div>
      <mat-icon
        *ngIf="!disableAdd"
        id="iconAddNameVal"
        class="addNameValue"
        (click)="addValue(inputField.type)"
        >add_circle</mat-icon
      >
    </div>
  </div>
</div>

<!-- multiselect dropdown -->
<div *ngIf="inputField.type === 'multi-select'">
  <div>
    <div class="leftInputPadding">
      <label class="listLabel"
        ><span *ngIf="inputField.required">*</span>{{ inputField.label }}</label
      >
    </div>
  </div>
  <div>
    <div *ngFor="let val of selectedValue; let i = index">
      <div class="leftInputPadding">
        <!-- <input type="text" class="form-control largeTextArea" [disabled]="disabled" [ngModel]="val" id="value{{val}}"  (blur)="onValueListChange(i,inputField.type,$event)"/> -->
        <select
          class="largeInput"
          [ngModel]="val"
          [disabled]="true"
          id="input_{{ inputField.jsonField }}"
        >
          <option value="">Select</option>
          <option *ngFor="let option of inputField.values" [value]="option.id">
            {{ option.value }}
          </option>
        </select>
      </div>
      <div>
        <mat-icon
          *ngIf="!disableRemove"
          id="iconRemNameVal{{ val }}"
          class="removeNameValue"
          (click)="removeValue(val, i, inputField.type, 'old')"
          >remove_circle</mat-icon
        >
      </div>
    </div>
    <div *ngFor="let val of listNewValue; let i = index">
      <div class="leftInputPadding">
        <select
          class="largeInput"
          (change)="onValueListChange(i, inputField.type, $event)"
          [ngModel]="val"
          [disabled]="disabled"
          id="input_{{ inputField.jsonField }}"
        >
          <option value="">Select</option>
          <option *ngFor="let option of inputField.values" [value]="option.id">
            {{ option.value }}
          </option>
        </select>
      </div>
      <div>
        <mat-icon
          *ngIf="!disableRemove"
          id="iconRemSelVal{{ val }}"
          class="removeNameValue"
          (click)="removeValue(val, i, inputField.type, 'new')"
          >remove_circle</mat-icon
        >
      </div>
    </div>
  </div>

  <div>
    <div class="leftInputPadding"></div>
    <div>
      <mat-icon
        *ngIf="!disableAdd"
        id="iconAddNameVal"
        class="addNameValue"
        (click)="addValue()"
        >add_circle</mat-icon
      >
    </div>
  </div>
</div>

<!-- numeric -->
<div *ngIf="inputField.type === 'numeric'">
  <input
    class="largeInput"
    type="number"
    id="numeric{{ inputField.jsonField }}"
    min="{{
      inputField.min > 0 && inputField.max > inputField.min ? inputField.min : 0
    }}"
    max="{{
      inputField.max > 0 && inputField.max > inputField.min
        ? inputField.max
        : 99999
    }}"
    step="1"
    [(ngModel)]="selectedValue"
    autocomplete="off"
    [disabled]="disabled"
    title="{{ inputField.hint }}"
    (blur)="onValueChange($event, inputField.type)"
    placeholder="{{ inputField.hint }}"
    [disabled]="disabled"
  />
</div>

<!-- render grid-view -->
<div *ngIf="inputField.type === 'grid-view'" [ngClass]="{ disabled: disabled }">
  <div *ngIf="!disabled">
    <div>
      <label class="listLabel"
        ><span *ngIf="inputField.required">*</span>{{ inputField.label }}</label
      >
    </div>
    <div class="height30">
      <mat-icon
        id="iconAddValArr"
        class="addNameValue"
        (click)="onGridItemAdd(inputField)"
        >add_circle</mat-icon
      >
    </div>
  </div>
  <div>
    <div *ngFor="let key of listingKeys; let i = index" class="{{ key.class }}">
      <label>{{ key.label }}</label>
    </div>
  </div>
  <div *ngFor="let val of selectedValue; let i = index">
    <div *ngFor="let key of listingKeys; let i = index" class="{{ key.class }}">
      <label>{{ val[key.key] }}</label>
    </div>
  </div>
</div>
