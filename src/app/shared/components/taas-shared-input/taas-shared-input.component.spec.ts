import { ComponentFixture, TestBed } from '@angular/core/testing';

import { TaasSharedInputComponent } from './taas-shared-input.component';

describe('TaasSharedInputComponent', () => {
  let component: TaasSharedInputComponent;
  let fixture: ComponentFixture<TaasSharedInputComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TaasSharedInputComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(TaasSharedInputComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
