.largeInput,.largeInput:focus-visible{
    width:100%;
    height: 30px;
    border-top: none;
    border-left: none;
    border-right: none;
    border-bottom: 1px solid #648787;
    color: #212529;
    cursor: text;
}
.largeTextArea,.largeTextArea:focus-visible{
    width:100%;
    border-top: none;
    border-left: none;
    border-right: none;
    border-bottom: 1px solid #648787;
    color: #212529;
    cursor: text;
    font-size: inherit;
}
.largePasswordInput{
    width:90%;
    border: none;
    // border-bottom: 1px solid #648787;
    color: #212529;
    cursor: text;
    font-size: inherit;
}
.showPasswordIcon{
    height: 31px;
    width: 10%;
    display: inline;
    // border-bottom: 1px solid #648787;
}
.inputBorder{
    border-bottom: 1px solid #648787;
}
.leftInputPadding{
    padding-left: 0px;
    padding-right:30px;
}
.addNameValue{
    color:#0068b4;;
    border: none;
}
.removeNameValue{
    color:red;
    border: none;
}
label {
    color: #666;
    font-size: 12px;
    margin-bottom: 0px;
    padding-top: 0.5em;
}
input,select{
    font-size: inherit;
}
.listLabel{
    font-size: 13px;
}
.height30{
    height: 30px;
}
.form-checkbox {
    vertical-align: middle;
    width: 15px;
    height: 15px;
}

.tags-row {
  display: flex;
  gap: 20px;
  align-items: center;
}

.tags-row > div:first-child,
.tags-row > div:nth-child(2) {
  flex: 3;
}

ng-moment-timezone-picker {
    .wrapper{
        width: 100%!important;
        height: 30px!important;
    }
    .ng-select .ng-select-container{
        min-height: 0px;
        border-top: 0.5em solid transparent !important;
        padding: 0em!important;
        .ng-value-container {
            padding-top: 0px;
            border-top: solid;
        }
    }
    .ng-placeholder{
        top: 12px!important;
        display: none!important;
    }
    .ng-value-container{
        padding-left: 10px!important;
    }
    .ng-arrow-wrapper{
        margin-right: 10px;
    }
}

ng-moment-timezone-picker .ng-select .ng-select-container .ng-value-container .ng-input>input{
    cursor:text;
}
input[type=checkbox],input[type=checkbox],app-taas-shared-input mat-icon{
    cursor: pointer;
}