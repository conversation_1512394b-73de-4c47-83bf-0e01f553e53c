import { Component, Input, Output, EventEmitter, inject} from '@angular/core';
import { MatIcon } from '@angular/material/icon';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule } from '@angular/forms';
import { ReactiveFormsModule} from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { ToastService } from 'utimaco-common-ui-angular';
import { TIMEZONES } from './TIMEZONES';
@Component({
  selector: 'app-taas-shared-input',
  standalone: true,
  imports: [MatIcon,MatIconModule,CommonModule,MatFormFieldModule,FormsModule,
    MatInputModule,ReactiveFormsModule
  ],
  templateUrl: './taas-shared-input.component.html',
  styleUrl: './taas-shared-input.component.scss'
})
export class TaasSharedInputComponent  {
  @Input() set value(v:any){
    if(this.inputField.type==='date-time-picker' && v && this.isInit){
      // set date object and convert time to local timezone
      this.setDateTimePicker(v);
    } else if(this.inputField.type==='select' && !v && this.isInit){
      // set select input type
      this.selectedValue='';
    } else if((this.inputField.type==='value-array' || this.inputField.type==='multi-select') && this.isInit){
      // set value-array and multi-select input type
      this.setMultiSelect(v);
    } else if(this.inputField.type==='name-value_editable-array' && this.isInit){
      // set name-value_editable-array input type
      this.setNameValueArr(v);
    } else if(this.inputField.type==='grid-view' && this.isInit){
      // set name-value_editable-array input type
      this.setGridView(v);
    } else if(this.isInit) {
      // set other input types
      this.setOtherInputs(v);
    }
  }
  @Input() disabled:boolean=false;
  @Input() disableAdd:boolean=false;
  @Input() disableRemove:boolean=false;
  @Input() inputLabel:string='';
  @Input() maxLength:string='';
  @Input() inputField:any={label:null,jsonField:null,type:null,values:null};
  @Output() update:EventEmitter<any>= new EventEmitter();
  @Input() listingKeys:any=[];
  timeObject:any={time:null,timezone:null};// time-timezone data object
  selectedValue:any;// input value
  listNewValue:any=[];// input value
  opList:any=[];
  isInit=true;// set to false on 1st initialization
  selectedTimezone:any=null;// timezone input data object
  inputPassword:any={showPassword: false, type: 'password'};// show password text toggler
  timezones: Map<string, string> = TIMEZONES;
  tzNames: string[] =[];
  offsetTmz: any=[];
 selectedTz: any ;
  constructor() {
    this.selectedTz = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const offsetval = this.timezones.get(this.selectedTz);
    this.timeObject.timezone=offsetval;
  }

 private _toast = inject(ToastService);
  getLocalizedPlaceholder(inputField:any){
    //console.log('inputField',inputField);
    if(inputField.jsonField=="keyVaults"){
      //console.log('inputField',inputField.jsonField);
      return $localize`:SharedInput-AddCloudInstance@@placeholderText.keyVaults:Specify comma separated values for multiple key vault`;
    }else{
      return inputField.hint;
    }
  }

  getLocalizedDateTimeLabel(inputField:any){
    if(inputField.jsonField=="activationDate"){
      return $localize`:SharedInput-ReUploadKey@@activationDate_timezone:Activation Date`;
    }else if(inputField.jsonField=="expirationDate"){
      return $localize`:SharedInput-ReUploadKey@@expirationDate_timezone:Expiration Date`;
    }
    else{
      return inputField.label;
    }
  }

  // ngOnInit(): void {
  // }

  // set DateTimePicker input
  setDateTimePicker(v:any){
    this.isInit=false;
    this.selectedValue=v;// set input value
    if(this.selectedValue && this.selectedTimezone && this.selectedTimezone.nameValue){
      this.timeObject.time=this.convertTZ(this.selectedValue);
      this.timeObject.timezone=this.getLocalTimezone();
      const newEvent={ target:{value:this.timeObject.time+this.timeObject.timezone}};
      this.onValueChange(newEvent,this.inputField.type);
    } else {
      if(Array.isArray(this.selectedValue) && this.selectedValue.length == 2){
        this.timeObject.time=this.selectedValue[0].replace('Z','');
      }else{
        this.timeObject.time=this.selectedValue.replace('Z','');
      }
    }
  }

  // set multiselect input
  setMultiSelect(v:any){
    this.isInit=false;
    this.selectedValue=v ? v.split(',').map((d: string)=>d.trim()) : [];
    this.listNewValue=[];
    if(this.selectedValue.length===0) {
      this.listNewValue.push('');
    }
  }

  // set gridview input
  setGridView(v:any){
    this.isInit=false;
    this.listingKeys=[{key:'keyVersion',label:'Version', class:'col-3 col-sm-3 col-md-3 col-lg-3 col-xl-3'},
    {key:'keyState',label:'Key State', class:'col-3 col-sm-4 col-md-4 col-lg-4 col-xl-4 pl-0'},
    {key:'creationDate',label:'Creation Date', class:'col-6 col-sm-5 col-md-5 col-lg-5 col-xl-5 pl-0'}];
    this.selectedValue=v ? v : [];
    this.listNewValue=[];
  }

  // set other input types
  setOtherInputs(v: any){
    this.selectedValue=v;
    if(this.inputField.type==='select' && v){
      this.opList=[];
      this.opList=this.inputField.values.map((d: { id: any; })=>d.id);
    }
  }

  // set name-value_editable array
  setNameValueArr(v:any){
    this.isInit=false;
    this.selectedValue=v ? v : [];
    this.listNewValue=[];
  }

  // emit update event
  onValueChange(event:any,type:any){
    this.update.emit({event:event,type:type});
  }

  // set data object on update time or timezone
  onDateTimezoneChange(event:any,type:any,set:any){
    if(set==='time'){
      this.timeObject.time=event.target.value+":00";
    } else if(set==='timezone'){
      const offsetval = this.timezones.get(this.selectedTz);
      this.selectedTimezone=event;
      this.timeObject.timezone=offsetval;
    }
    if(this.timeObject.time && this.timeObject.timezone) {
      const newEvent={ target:{value:[this.timeObject.time,this.timeObject.timezone]}};
      this.onValueChange(newEvent,type);
    }
  }

  // convert time from UTC to local and string format
  convertTZ(date:any) {
    const dt= new Date(date);
    const yy=dt.getFullYear();
    const mm=dt.getMonth() + 1;
    const dd=dt.getDate();
    const hrs=dt.getHours();
    const mins=dt.getMinutes();
    return yy+'-'+
    (mm > 9 ? mm : '0'+mm)+'-'+
    (dd > 9 ? dd : '0'+dd)+'T'+
    (hrs > 9 ? hrs : '0'+hrs)+':'+
    (mins > 9 ? mins : '0'+mins);
  }

  // fetch local time zone
  getLocalTimezone(){
    const offset= new Date().getTimezoneOffset();
    const h=Math.trunc(offset/60);
    const m=Math.abs(offset % 60);
    const hh=(h > 9 ? h : '0'+Math.abs(h));
    const mm=(m > 9 ? m : '0'+m);
    const timezone= h > 0 ? '-'+(hh)+':'+mm : '+'+(hh)+':'+mm;
    return timezone;
  }

  // add value in name-value-array
  addNameValue(){
    const newKey={name:'',value:''};
    if(!this.selectedValue) {
      this.selectedValue=[];
    }
    this.selectedValue.push(newKey);
  }

  // remove value from name-value-array
  removeNameValue(value:any,i:any,type:any){
    this.selectedValue=this.selectedValue.filter((item: any) => item !== value);
    const newEvent={ target:{value:this.selectedValue,selectedIndex:i}};
    this.onValueChange(newEvent,type);
  }

  // name-value-array value change
  onListValueChange(i:any,type:any){
    const newEvent={ target:{value:this.selectedValue,selectedIndex:i}};
    this.onValueChange(newEvent,type);
  }

  // checkbox value change event
  onCheckboxChange(event:any,type:any){
    const newEvent={ target:{value:event.target.checked}};
    this.onValueChange(newEvent,type);
  }

  // add value in value-array
  addValue(type=''){
    if(this.maxLength && ((this.selectedValue.length+this.listNewValue.length)>=this.maxLength)) {
      this._toast.error('Error','The maximum number of supported '+this.inputField.label+'s reached.');
      return;
    }
    if(!this.listNewValue) {
      this.listNewValue=[];
    }
    if(type==='name-value_editable-array' && !(this.listNewValue.filter((d: { name: string; })=>(d.name.trim()==='')).length)){
      this.listNewValue.push({ name:'', value:''});
    }
    if(type!=='name-value_editable-array' && !this.listNewValue.includes('')){
      this.listNewValue.push('');
    }
  }

  // remove value from value-array
  removeValue(value:any,i:any,type:any,vtype:any){
    switch(vtype){
      case 'old':
        if(type==='name-value_editable-array'){
          this.selectedValue=this.selectedValue.filter((item: { name: any; }) => item.name !== value.name);
        }
        else {
          this.selectedValue=this.selectedValue.filter((item: any) => item !== value);
        }
        break;
      case 'new':
        if(type==='name-value_editable-array'){
          this.listNewValue=this.listNewValue.filter((item: { name: any; }) => item.name !== value.name);
        } else {
          this.listNewValue=this.listNewValue.filter((item: any) => item !== value);
        }
        break;
    }
    const newEvent={ target:{value:[this.selectedValue,this.listNewValue],selectedIndex:i}};
    this.onValueChange(newEvent,type);
  }

  // value-array value change
  onValueListChange(i:any,type:any,event:any,idName='',vtype='new'){
    if(type==='name-value_editable-array'){
      if(vtype==='old') {
        this.selectedValue[i][idName]=event.target.value;
      } else {
        this.listNewValue[i][idName]=event.target.value;
      }
    } else {
      this.listNewValue[i]=event.target.value;
    }
    const newEvent={ target:{value:[this.selectedValue,this.listNewValue],selectedIndex:i}};
    this.onValueChange(newEvent,type);
  }

  // toggle show protected fields type
  passwordToggle(value: any){
    this.inputPassword.showPassword=value;
    if(value){
      this.inputPassword.type='text';
    } else {
      this.inputPassword.type='password';
    }
  }

  // trigger event on grid item add
  onGridItemAdd(field: { type: any; }){
    const newEvent={ target:{value:null}};
    this.onValueChange(newEvent,field.type);
  }
}

