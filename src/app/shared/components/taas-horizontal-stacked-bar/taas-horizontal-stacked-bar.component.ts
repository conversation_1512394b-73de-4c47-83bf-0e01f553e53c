import { Component } from '@angular/core';
import { NgChartsModule } from 'ng2-charts';
import { ChartConfiguration } from 'chart.js';
import { CommonModule } from '@angular/common';


@Component({
  selector: 'app-taas-horizontal-stacked-bar',
  standalone: true,
  imports: [NgChartsModule, CommonModule],
  templateUrl: './taas-horizontal-stacked-bar.component.html',
  styleUrl: './taas-horizontal-stacked-bar.component.scss'
})
export class TaasHorizontalStackedBarComponent  {
  public barChartOptions: ChartConfiguration<'bar'>['options'] = {
    responsive: true,
    maintainAspectRatio: false,
    indexAxis: 'y',
    scales: {
      x: {
        stacked: true,
        display: false,
        min: 0,
        max: 125  // Slightly larger than total to ensure bar doesn't fill width
      },
      y: {
        stacked: true,
        display: false
      }
    },
    plugins: {
      legend: {
        //display: false
        display: true,
        position: 'top'
      },
      tooltip: {
        enabled: true,
        callbacks: {
          label: function(context) {
            return `${context.dataset.label}: ${context.raw}`;
          }
        }
      }
    },
    layout: {
      padding: 0
    }
  };

  public barChartData: ChartConfiguration<'bar'>['data'] = {
    labels: [''],
    datasets: [
      {
        data: [100],
        label: 'Compliant',
        backgroundColor: '#60B044',  // Green
        barThickness: 30  // Controls the height of the bar
      },
      {
        data: [5],
        label: 'Risk',
        backgroundColor: '#BD2C00',  // Red
        barThickness: 30
      },
      {
        data: [20],
        label: 'Pending',
        backgroundColor: '#6E7B8D',  // Grey
        barThickness: 30
      }
    ]
  };

  //ngOnInit(): void {}
}

// implements OnInit {
//   public barChartOptions: ChartConfiguration<'bar'>['options'] = {
//     responsive: true,
//     indexAxis: 'y',  // This makes the bars horizontal
//     scales: {
//       x: {
//         stacked: true,
//         grid: {
//           display: false
//         }
//       },
//       y: {
//         stacked: true,
//         grid: {
//           display: false
//         }
//       }
//     },
//     plugins: {
//       legend: {
//         display: true,
//         position: 'top'
//       },
//       title: {
//         display: true,
//         text: 'Horizontal Stacked Bar Chart'
//       }
//     }
//   };

//   public barChartData: ChartConfiguration<'bar'>['data'] = {
//     labels: ['Category 1'],
//     //labels: ['Category 1', 'Category 2', 'Category 3', 'Category 4'],
//     datasets: [
//       {
//         data: [65, 59, 80, 81],
//         label: 'Compliant',
//         backgroundColor: 'rgba(8, 167, 16, 0.8)',
//       },
//       {
//         data: [28, 48, 40, 19],
//         label: 'Risk',
//         backgroundColor: 'rgba(243, 8, 8, 0.8)',
//       },
//       {
//         data: [30, 20, 30, 40],
//         label: 'Pending',
//         backgroundColor: 'rgba(108, 114, 114, 0.8)',
//       }
//     ]
//   };

//   ngOnInit(): void {
//     // You can load your data here
//   }
// }
