import { ComponentFixture, TestBed } from '@angular/core/testing';

import { TaasHorizontalStackedBarComponent } from './taas-horizontal-stacked-bar.component';

describe('TaasHorizontalStackedBarComponent', () => {
  let component: TaasHorizontalStackedBarComponent;
  let fixture: ComponentFixture<TaasHorizontalStackedBarComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TaasHorizontalStackedBarComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(TaasHorizontalStackedBarComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
