import { Routes } from '@angular/router';
import { RoleAuthGuard } from './core/guards/role-auth.guard';
import {
  BASE_USER_ROLES,
  ADMIN_ROLES,
  ORG_ADMIN_ONLY,
  AZURE_ROLES,
  AWS_ROLES,
  GCP_ROLES,
  KMIP_ROLES,
  REST_ROLES,
  TDE_ROLES,
  AUDIT_LOG_ROLES
} from './core/models/role-mapping.const';

export const routes: Routes = [
  //{ path: '', loadComponent: () => import('./features/homepage/home.component').then(module => module.TaasKmHomeComponent)},
  { path: '', redirectTo: 'cloud-clients/azure', pathMatch: 'full' },
  {
    path: 'dashboard',
    loadComponent: () =>
      import('./features/dashboard/taas-dashboard.component').then(
        (module) => module.TaasDashboardComponent
      ),
    canActivate: [RoleAuthGuard],
    data: {
      requiredRoles: BASE_USER_ROLES
    }
  },
  {
    path: 'cloud-clients',
    children: [
      { path: '', redirectTo: 'azure', pathMatch: 'full' },
      {
        path: 'azure',
        loadComponent: () =>
          import('./features/cloud-listing/taas-cloud-listing.component').then(
            (module) => module.TaasCloudListingComponent
          ),
        canActivate: [RoleAuthGuard],
        data: {
          requiredRoles: AZURE_ROLES
        }
      },
      {
        path: 'aws',
        loadComponent: () =>
          import(
            './features/cloud-clients/taas-cloud-aws/taas-cloud-aws.component'
          ).then((module) => module.TaasCloudAwsComponent),
        canActivate: [RoleAuthGuard],
        data: {
          requiredRoles: AWS_ROLES
        }
      },
      {
        path: 'gcp',
        loadComponent: () =>
          import(
            './features/cloud-clients/taas-cloud-gcp/taas-cloud-gcp.component'
          ).then((module) => module.TaasCloudGcpComponent),
        canActivate: [RoleAuthGuard],
        data: {
          requiredRoles: GCP_ROLES
        }
      },
    ],
    canActivateChild: [RoleAuthGuard],
    data: {
      requiredRoles: BASE_USER_ROLES
    }
  },
  {
    path: 'managekeys',
    children: [
      {
        path: '',
        loadComponent: () =>
          import(
            './features/manage-cloud-keys/taas-manage-cloud-keys.component'
          ).then((module) => module.TaasManageCloudKeysComponent),
        canActivate: [RoleAuthGuard]
      },
      {
        path: ':id',
        loadComponent: () =>
          import(
            './features/manage-cloud-keys/taas-manage-cloud-keys.component'
          ).then((module) => module.TaasManageCloudKeysComponent),
        canActivate: [RoleAuthGuard]
      },
    ],
    canActivateChild: [RoleAuthGuard],
    data: {
      requiredRoles: BASE_USER_ROLES
    }
  },
  {
    path: 'audit-logs',
    loadComponent: () =>
      import('./features/audit-log/taas-audit-log.component').then(
        (module) => module.TaasAuditLogComponent
      ),
    canActivate: [RoleAuthGuard],
    data: {
      requiredRoles: AUDIT_LOG_ROLES
    }
  },
  {
    path: 'access-management',
    children: [
      {
        path: 'groups',
        loadComponent: () =>
          import(
            './features/access-management/view-group/taas-view-group.component'
          ).then((module) => module.TaasViewGroupComponent),
        canActivate: [RoleAuthGuard],
        data: {
          requiredRoles: ADMIN_ROLES
        }
      },
      {
        path: 'users',
        loadComponent: () =>
          import(
            './features/access-management/view-user/taas-view-user.component'
          ).then((module) => module.TaasViewUserComponent),
        canActivate: [RoleAuthGuard],
        data: {
          requiredRoles: ADMIN_ROLES
        }
      },
    ],
    canActivateChild: [RoleAuthGuard],
    data: {
      requiredRoles: ADMIN_ROLES
    }
  },
  {
    path: 'enterprise-clients',
    children: [
      {
        path: 'kmip',
        children: [
          {
            path: '',
            loadComponent: () =>
              import('./features/enterprise-clients/kmip/taas-kmip.component').then(
                (module) => module.TaasKmipComponent
              ),
            canActivate: [RoleAuthGuard]
          },
          {
            path: ':id',
            loadComponent: () =>
              import('./features/enterprise-clients/kmip/taas-manage-kmip-client/taas-manage-kmip-client.component').then(
                (module) => module.TaasManageKmipClientComponent
              ),
            canActivate: [RoleAuthGuard]
          }
        ],
        canActivateChild: [RoleAuthGuard],
        data: {
          requiredRoles: KMIP_ROLES
        }
      },
      {
        path: 'tde',
        loadComponent: () =>
          import('./features/enterprise-clients/tde/taas-tde.component').then(
            (module) => module.TaasTdeComponent
          ),
        canActivate: [RoleAuthGuard],
        data: {
          requiredRoles: TDE_ROLES
        }
      },
      {
        path: 'rest',
        loadComponent: () =>
          import('./features/enterprise-clients/rest/taas-rest.component').then(
            (module) => module.TaasRestComponent
          ),
        canActivate: [RoleAuthGuard],
        data: {
          requiredRoles: REST_ROLES
        }
      },
    ],
    canActivateChild: [RoleAuthGuard],
    data: {
      requiredRoles: BASE_USER_ROLES
    }
  },
  {
    path: 'settings',
    loadComponent: () =>
      import('./features/setting/taas-setting.component').then(
        (module) => module.TaasSettingComponent
      ),
    canActivate: [RoleAuthGuard],
    data: {
      requiredRoles: ORG_ADMIN_ONLY
    }
  },
];
