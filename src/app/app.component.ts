import { Component, OnInit, inject } from '@angular/core';
import { Router, NavigationEnd, RouterOutlet } from '@angular/router';

import { TaasUserService } from './core/services/taas-user-service.service';
import { TaasMenuItem, TaasUserDetails } from 'utimaco-common-ui-angular';
import { TaasMainLayoutComponent, TaasHiddenVersionInfoComponent, TaasToastrComponent } from 'utimaco-common-ui-angular';
import { KeycloakService } from 'keycloak-angular';
import { RoleBasedMenuService } from './core/services/menu/role-based-menu.service';
import { UserRoleService } from './core/services/role/user-role.service';
import appPackageJson from '../../package.json';
import commonUiPackageJson from 'utimaco-common-ui-angular/package.json';
import { environment } from '@kmaas-environments/environment';
import { TaasBackendHiddenVersionService } from './core/services/taas-backend-hidden-version.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [TaasMainLayoutComponent, TaasHiddenVersionInfoComponent, RouterOutlet,TaasToastrComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})

export class AppComponent implements OnInit {
  private taasUserService = inject(TaasUserService);
  private router = inject(Router);
  private keycloakService = inject(KeycloakService);
  private taasBackendHiddenVersion = inject(TaasBackendHiddenVersionService);
  private roleBasedMenuService = inject(RoleBasedMenuService);
  private userRoleService = inject(UserRoleService);

  currentuser: TaasUserDetails = {
    givenName: "",
    familyName: "",
    email: "",
    initials: ""
  };

  activeRoute: string = "/";
  taasMenuItemsKm: TaasMenuItem[] = [];
  commonUiLibVersion: string = commonUiPackageJson.version;
  appVersion: string = appPackageJson.version;
  backendAppVersion!: string;

  constructor() {
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        if (event.url === '/') {
          this.activeRoute = '/cloud-clients/azure';
        } else {
          this.activeRoute = event.url;
        }
      }
    });
    this.taasUserService.getCurrentUserDetails().then(taasUserDetails => {
      this.currentuser = taasUserDetails;
    });
  }

  ngOnInit() {
    this.taasBackendHiddenVersion.getBackendVersion().subscribe({
      next: (response) => {
        this.backendAppVersion = response?.body?.['data'];
      },
      error: (error) => {
        console.error($localize`:App Component:Error fetching data:`, error);
      }
    });

    // Initialize user roles and subscribe to role-based menu items
    this.userRoleService.initializeUserRoles();
    this.roleBasedMenuService.menuItems$.subscribe(menuItems => {
      this.taasMenuItemsKm = menuItems;
    });
  }

  handleRouteUpdateRequest(path: string) {
    this.router.navigateByUrl(path);
  }

  navigateHome() {
    // TODO: Currently blocking it for POC later allow accessing taas Portal
    // window.open(environment.taasPortalFrontendUrl);
  }

  performLogout() {
    this.keycloakService?.logout();
  }

  handleUserContextMenuEvent(event: string) {
    switch (event) {
      case "logout":
        this.performLogout();
        break;
      case "showOwnProfile":
        window.open(environment.taasKeyCloakProfileUrl);
        // console.log("TODO: show own profile");
        break;
      default:
        // console.log("Unsupported user info context event received: ", event);
        break;
    }
  }
}
