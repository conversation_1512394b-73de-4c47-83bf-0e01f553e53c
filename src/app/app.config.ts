import { ApplicationConfig, importProvidersFrom } from '@angular/core';
import { provideRouter } from '@angular/router';

import { routes } from './app.routes';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { KeycloakAngularModule, KeycloakBearerInterceptor, KeycloakService } from 'keycloak-angular';
import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { APP_INITIALIZER } from '@angular/core';
import { environment } from '@kmaas-environments/environment';
import { taasCustomHeaderInterceptor } from './core/interceptors/taas-custom-header.interceptor';
import { UserRoleService } from './core/services/role/user-role.service';

/**
 * Initialize Keycloak authentication and user roles in the correct sequence
 * 1. First initialize Keycloak and authenticate the user
 * 2. Then initialize the user roles service, which depends on the JWT token from Keycloak
 */
export const initializeKeycloak = (keycloakService: KeycloakService, userRoleService: UserRoleService) => async () => {
  try {
    const keycloakInitDone: boolean = await keycloakService.init({
      config: {
        url: environment.keycloakBaseUrl,
        realm: environment.keycloakRealm,
        clientId: environment.keycloakClientId
      },
      loadUserProfileAtStartUp: true,
      initOptions: {
        onLoad: 'login-required',
        silentCheckSsoRedirectUri:
          window.location.origin + '/assets/silent-check-sso.html',
        checkLoginIframe: false
      },
    });

    if (keycloakInitDone) {
      // Initialize user roles only after Keycloak is initialized
      // This ensures the JWT token is available for HTTP requests
      await userRoleService.initializeUserRoles();
    } else {
      console.error($localize`:App Config \: Keycloak initialization failed: Keycloak initialization failed skipping user role initialization`);
    }

    return keycloakInitDone;
  } catch (error) {
    console.error($localize`:App Config \: Error during initialization: Error during initialization:`, error);
    return false;
  }
}

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes),
    provideAnimationsAsync(),
    KeycloakAngularModule,
    KeycloakService,
    KeycloakBearerInterceptor,
    importProvidersFrom(HttpClientModule),
    {
      provide: HTTP_INTERCEPTORS,
      useClass: taasCustomHeaderInterceptor,
      multi: true
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: KeycloakBearerInterceptor,
      multi: true,
      deps: [KeycloakService]
    },
    {
      provide: APP_INITIALIZER,
      useFactory: initializeKeycloak,
      multi: true,
      deps: [KeycloakService, UserRoleService],
    },
  ]
};
