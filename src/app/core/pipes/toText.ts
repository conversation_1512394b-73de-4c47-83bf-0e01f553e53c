//formats uppercase labels with underscores to norlam labels
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({ name: 'appToText', standalone: true }, )
export class ToText implements PipeTransform {
  transform(str: string | null | undefined): string {
    if (!str) return "";
    //else if(str === "Salesforce CA") return str;
    else return (str.replaceAll('_',' ').split(' ').map((a :string) => a.trim().toLowerCase()).map((a :string) => a[0].toUpperCase() + a.substring(1)).join(" ")).replace('Eskm','ESKM');
  }
}