import { Pipe, PipeTransform } from '@angular/core';

@Pipe({ name: 'appCloudTypeName' , standalone: true})
export class FormatCloudTypeName implements PipeTransform {
    transform(str: string): string {
        if (!str) return "";
        else if (str === "Salesforce-Market-BYOK") return "Salesforce Market-BYOK";
        else if (str === "Salesforce-Cache-Only-Key") return "Salesforce-Cache-Only Key";
        else return str;
    }
}