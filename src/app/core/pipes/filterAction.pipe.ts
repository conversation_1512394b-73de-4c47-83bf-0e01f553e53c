import { Pipe, PipeTransform } from '@angular/core';
import { ActionItem } from '../models/action-item';
import { Key } from '../models/key';
import { CloudType } from '../models/cloud-type';
@Pipe({ name: 'appFilterAction', standalone: true })
export class FilterAction implements PipeTransform {
  transform(items: ActionItem[], key: Key, cloudType: CloudType): ActionItem[] {
    //null handling
    if (!items) return [];

    //filter values
    if (cloudType.name.includes('-DKE') && !cloudType.uploadKeyEnabled) {
      return items.filter(it => it.action !== 'Upload');
    }
    else if (cloudType.name.includes('-XKS') && !cloudType.uploadKeyEnabled) {
      return items.filter(it =>
        it.action !== 'Edit' &&
        it.action !== 'Upload' &&
        it.action !== 'New Version'
      );
    }
    else if (
      cloudType.name.includes('Salesforce-BYOK') &&
      key.status !== 'Uploaded' &&
      key.status !== '-'
    ) {
      return items.filter(it =>
        it.action !== 'Edit' &&
        it.action !== 'New Version'
      );
    }
    else if (
      cloudType.name.includes('Salesforce-BYOK') &&
      (key.status === 'Uploaded' || key.status === '-')
    ) {
      return items.filter(it =>
        it.action !== 'Edit' &&
        it.action !== 'Upload' &&
        it.action !== 'New Version'
      );
    }
    else if (
      (key.status !== 'Not Uploaded' &&
        !(key.status.includes('Error') || key.status.includes('Deleted'))) ||
      !cloudType.uploadKeyEnabled
    ) {
      return items.filter(it =>
        it.action !== 'Upload' &&
        it.action !== 'New Version'
      );
    }
    else {
      return items.filter(it =>
        it.showOnNotUpload &&
        it.action !== 'New Version'
      );
    }
  }
}
