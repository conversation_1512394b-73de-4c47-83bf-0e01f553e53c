//pipe to search keys on upload existing key view
import { Pipe, PipeTransform } from '@angular/core';

interface KeyItem {
  keyName?: string;
  eskmKeyName?: string;
}

function getStringValue(item: any): string {
  if (typeof item === 'string') {
    return item.toLocaleLowerCase();
  }
  return '';
}

@Pipe({ name: 'appSearchKey' ,standalone:true})
export class SearchKeyPipe implements PipeTransform {
  transform(items: KeyItem[], searchText: string): KeyItem[] {
    //null handling
    if (!items) {
      return [];
    }
    if (!searchText) {
      return items;
    }
    searchText = searchText.toLocaleLowerCase();
    //return filtered data
    return items.filter(it => {
      //to fix eslint: if(it.hasOwnProperty('keyName'))
      if(it.keyName && Object.prototype.hasOwnProperty.call(it, 'keyName')) {
        return it.keyName.toLocaleLowerCase().includes(searchText);
      }
      //else if(it.hasOwnProperty('eskmKeyName'))
      else if(it.eskmKeyName && Object.prototype.hasOwnProperty.call(it, 'eskmKeyName')) {
        return it.eskmKeyName.toLocaleLowerCase().includes(searchText);
      }
      else {
        return getStringValue(it).includes(searchText);
      }
    });
  }
}