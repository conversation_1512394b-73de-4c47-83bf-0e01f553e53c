import { Injectable, inject } from '@angular/core';
import { CanActivate, CanActivateChild, ActivatedRouteSnapshot, Router, UrlTree } from '@angular/router';
import { UserRoleService } from '../services/role/user-role.service';
import { Role } from '../models/roles.enum';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class RoleAuthGuard implements CanActivate, CanActivateChild {

  private userRoleService = inject(UserRoleService);
  private router = inject(Router);

  canActivate(
    route: ActivatedRouteSnapshot
  ): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    return this.checkRoleAccess(route);
  }

  canActivateChild(
    childRoute: ActivatedRouteSnapshot
  ): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    return this.checkRoleAccess(childRoute);
  }

  private checkRoleAccess(route: ActivatedRouteSnapshot): boolean | UrlTree {
    // Get required roles from route data
    const requiredRoles: Role[] = this.getRequiredRoles(route);

    // If no required roles or user has at least one of the required roles, allow access
    if (requiredRoles.length === 0 || this.userRoleService.hasAnyRole(requiredRoles)) {
      return true;
    }

    // If access is denied, redirect to the default route (cloud-clients/azure)
    // TODO: might want to redirect to a "no access" page
    // or find the first accessible route for the user
    return this.router.parseUrl('/cloud-clients/azure');
  }

  /**
   * Get required roles from route data, checking current route and parent routes
   */
  private getRequiredRoles(route: ActivatedRouteSnapshot): Role[] {
    // Check current route data first
    if (route.data?.['requiredRoles']) {
      return route.data['requiredRoles'] as Role[];
    }

    // If no roles found in current route, check parent routes
    let parent = route.parent;
    while (parent) {
      if (parent.data?.['requiredRoles']) {
        return parent.data['requiredRoles'] as Role[];
      }
      parent = parent.parent;
    }

    // No required roles found
    return [];
  }
}