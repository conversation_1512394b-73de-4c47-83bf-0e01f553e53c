import { Injectable, inject } from '@angular/core';
import { BehaviorSubject, Observable, firstValueFrom } from 'rxjs';
import { TaasAccessManagementService } from '../accessManagement/taas-access-management.service';
import { Role } from '../../models/roles.enum';
import { Permission } from '../../models/access-management';
import { KeycloakService } from 'keycloak-angular';
import { ROLE_MAPPING } from '../../models/role-mapping.const';

/**
 * Service for managing user roles and permissions.
 * This service combines roles from JWT token and access management service.
 */
@Injectable({
  providedIn: 'root'
})
export class UserRoleService {
  private userRolesSubject: BehaviorSubject<Role[]> = new BehaviorSubject<Role[]>([]);
  public userRoles$: Observable<Role[]> = this.userRolesSubject.asObservable();
  
  private accessManagementService = inject(TaasAccessManagementService);
  // TODO: KeycloakService is deprecated and should be replaced with the new keycloak-angular API in a future update.
  private keycloakService = inject(KeycloakService);

  /**
   * Initialize the user roles on application startup
  */
  public async initializeUserRoles(): Promise<void> {
    try {
      // get roles from access management service with await because we need to assure that the data is retrieved
      const serviceRoles = await this.getRolesFromAccessManagement();
      const jwtRoles = this.extractRolesFromJwt();
      const combinedRoles = [...new Set([...serviceRoles, ...jwtRoles])];
      
      // Update the roles subject
      this.userRolesSubject.next(combinedRoles);
      
      console.log('=== USER ROLES INITIALIZED ===');
      console.log(`Total roles assigned: ${combinedRoles.length}`);
      console.log('Roles from JWT token:', jwtRoles);
      console.log('Roles from access management service:', serviceRoles);
      console.log('Combined user roles:', combinedRoles);
      console.log('=== END USER ROLES ===');
    } catch (error) {
      console.error('Failed to initialize user roles:', error);
      
      // If access management service failed, try to at least get JWT roles
      const jwtRoles = this.extractRolesFromJwt();
      this.userRolesSubject.next(jwtRoles);
      
      // If we couldn't get any roles, initialize with empty array
      if (jwtRoles.length === 0) {
        console.warn('No roles could be initialized. User will have no permissions.');
      }
    }
  }
  
  /**
   * Extract roles from the JWT token
   */
  private extractRolesFromJwt(): Role[] {
    try {
      // Get the token directly from KeycloakService
      const token = this.keycloakService.getKeycloakInstance().token;
      if (!token) {
        console.log('No JWT token found in Keycloak');
        return [];
      }
      
      // Extract the payload from the token
      const payload = this.decodeJwtPayload(token);
      
      // Extract roles from the payload according to the TaasJwtTokenDetails structure
      const allRoles: string[] = [];
      
      // Get realm roles from realm_access.roles (primary source for Keycloak roles)
      if (payload.realm_access && Array.isArray(payload.realm_access.roles)) {
        allRoles.push(...payload.realm_access.roles);
      }

      // Get client-specific roles from resource_access
      if (payload.resource_access) {
        // Iterate through all clients in resource_access
        for (const clientId in payload.resource_access) {
          const client = payload.resource_access[clientId];
          if (client && Array.isArray(client.roles)) {
            allRoles.push(...client.roles);
          }
        }
      }
      
      // check other common role fields for backward compatibility
      if (Array.isArray(payload.roles)) {
        allRoles.push(...payload.roles);
      }
      
      if (Array.isArray(payload.authorities)) {
        allRoles.push(...payload.authorities);
      }
      
      if (Array.isArray(payload.permissions)) {
        allRoles.push(...payload.permissions);
      }
      
      return this.mapJwtRolesToRoles(allRoles);
    } catch (error) {
      console.error('Error extracting roles from JWT:', error);
      return [];
    }
  }

  /**
   * Decode the JWT payload
   * @param token The JWT token
   * @returns The decoded payload
   */
  private decodeJwtPayload(token: string): any {
    // Split the token into header, payload, and signature
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid JWT token format');
    }
    
    // Decode the payload (second part)
    const payload = parts[1];
    const base64 = payload.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    
    return JSON.parse(jsonPayload);
  }

  private mapJwtRolesToRoles(jwtRoles: string[]): Role[] {
    return this.mapStringsToRoles(jwtRoles, 'JWT');
  }

  /**
   * Get roles from the access management service
   */
  private async getRolesFromAccessManagement(): Promise<Role[]> {
    try {
      // Get permissions from the access management service
      console.log('Fetching permissions from access management service...');
      const permissions = await firstValueFrom(this.accessManagementService.getPermissions());
      
      // Log the raw permissions response
      console.log('Access management permissions response:', permissions);
      
      if (!permissions || permissions.length === 0) {
        console.warn('No permissions returned from access management service');
        return [];
      }
      
      // Map the permissions to roles
      return this.mapPermissionsToRoles(permissions);
    } catch (error) {
      console.error('Error fetching permissions from access management service:', error);
      return [];
    }
  }
  
  /**
   * Map permissions from the access management service to roles
   */
  private mapPermissionsToRoles(permissions: Permission[]): Role[] {
    const permissionNames = permissions.map(p => p.permissionName);
    return this.mapStringsToRoles(permissionNames, 'Permissions');
  }

  /**
   * A generic, reusable function to map an array of strings to Role enums.
   * @param stringsToMap An array of strings (from JWT, permissions, etc.).
   * @param sourceType A string indicating the source for logging purposes (e.g., 'JWT', 'Permissions').
   * @returns An array of Role enum values.
   */
  private mapStringsToRoles(stringsToMap: string[], sourceType: string): Role[] {
    const roles: Role[] = [];
    
    // Create a new object from the imported constant to avoid modifying it.
    const roleMapping = { ...ROLE_MAPPING };

    // Add role_ prefix variants (common in keycloak) dynamically.
    Object.keys(ROLE_MAPPING).forEach(key => {
      roleMapping[`role_${key}`] = ROLE_MAPPING[key];
    });

    console.log(`Processing ${sourceType} roles using flexible mapping...`);

    stringsToMap.forEach(stringToMap => {
      const roleLower = stringToMap.toLowerCase();

      if (roleMapping[roleLower]) {
        console.log(`Mapped ${sourceType} role '${stringToMap}' to enum role '${roleMapping[roleLower]}'`);
        roles.push(roleMapping[roleLower]);
      } else {
        // Try partial matching for roles that might have prefixes/suffixes
        let matched = false;
        for (const knownRole in roleMapping) {
          if (roleLower.includes(knownRole)) {
            console.log(`Partial match: ${sourceType} role '${stringToMap}' contains '${knownRole}', mapping to '${roleMapping[knownRole]}'`);
            roles.push(roleMapping[knownRole]);
            matched = true;
            break;
          }
        }
        if (!matched) {
          console.log(`No mapping found for ${sourceType} role: '${stringToMap}'`);
        }
      }
    });

    // Remove duplicates
    const uniqueRoles = [...new Set(roles)];
    console.log(`Final mapped roles from ${sourceType}:`, uniqueRoles);

    return uniqueRoles;
  }
  
  /**
   * Check if the current user has a specific role
   * @param role The role to check
   * @returns True if the user has the role, false otherwise
   */
  public hasRole(role: Role): boolean {
    const currentRoles = this.userRolesSubject.getValue();
    const hasRole = currentRoles.includes(role);
    
    // Log the role check result
    // console.log(`Role check: ${role} - ${hasRole ? 'GRANTED' : 'DENIED'}`);
    
    return hasRole;
  }
  
  /**
   * Check if the current user has any of the specified roles
   * @param roles The roles to check
   * @returns True if the user has any of the roles, false otherwise
   */
  public hasAnyRole(roles: Role[]): boolean {
    const currentRoles = this.userRolesSubject.getValue();
    const hasAnyRole = roles.some(role => currentRoles.includes(role));
    
    // Log the roles check result with details
    // console.log(`Role check (any of): [${roles.join(', ')}] - ${hasAnyRole ? 'GRANTED' : 'DENIED'}`);
    // console.log(`User has roles: [${currentRoles.join(', ')}]`);
    
    // If access is granted, log which specific role(s) matched
    if (hasAnyRole) {
      const matchedRoles = roles.filter(role => currentRoles.includes(role));
      console.log(`Matched role(s): [${matchedRoles.join(', ')}]`);
    }
    
    return hasAnyRole;
  }
  
  /**
   * Check if the current user has all of the specified roles
   * @param roles The roles to check
   * @returns True if the user has all of the roles, false otherwise
   */
  public hasAllRoles(roles: Role[]): boolean {
    const currentRoles = this.userRolesSubject.getValue();
    return roles.every(role => currentRoles.includes(role));
  }
  
  /**
   * Get all roles for the current user
   * @returns Array of roles
   */
  public getUserRoles(): Role[] {
    return this.userRolesSubject.getValue();
  }

  /**
   * Check if the current user can edit instances
   * Centralized logic for instance editing permissions
   * @returns True if user has editor or admin permissions
   */
  public canEditInstances(): boolean {
    return this.hasAnyRole([
      Role.INSTANCE_EDITOR,
      Role.INSTANCE_ADMIN,
      Role.ORG_ADMIN
    ]);
  }

  /**
   * Check if the current user can delete instances
   * Centralized logic for instance deletion permissions
   * @returns True if user has admin permissions
   */
  public canDeleteInstances(): boolean {
    return this.hasAnyRole([
      Role.INSTANCE_ADMIN,
      Role.ORG_ADMIN
    ]);
  }

  /**
   * Check if the current user can create instances of a specific type
   * @param instanceType The type of instance (e.g., 'AZURE', 'KMIP', 'AWS')
   * @returns True if user has creation permissions for that instance type
   */
  public canCreateInstanceType(instanceType: string): boolean {
    const creatorRoles: Role[] = [];

    switch (instanceType.toUpperCase()) {
      case 'AZURE':
        creatorRoles.push(Role.AZURE_INSTANCE_CREATOR);
        break;
      case 'KMIP':
        creatorRoles.push(Role.KMIP_INSTANCE_CREATOR);
        break;
      case 'AWS':
        creatorRoles.push(Role.AWS_INSTANCE_CREATOR);
        break;
      case 'GCP':
        creatorRoles.push(Role.GCP_INSTANCE_CREATOR);
        break;
      case 'KMS':
        creatorRoles.push(Role.KMS_INSTANCE_CREATOR);
        break;
      case 'P11':
        creatorRoles.push(Role.P11_INSTANCE_CREATOR);
        break;
      case 'REST':
        creatorRoles.push(Role.REST_INSTANCE_CREATOR);
        break;
    }

    // Add org admin as they can create any instance type
    creatorRoles.push(Role.ORG_ADMIN);

    return this.hasAnyRole(creatorRoles);
  }

  /**
   * Check if the current user can manage keys
   * Centralized logic for key management permissions
   * @returns True if user has at least viewer permissions
   */
  public canManageKeys(): boolean {
    return this.hasAnyRole([
      Role.INSTANCE_VIEWER,
      Role.INSTANCE_EDITOR,
      Role.INSTANCE_ADMIN,
      Role.ORG_ADMIN
    ]);
  }

  /**
   * Check if the current user has any instance-level permissions
   * @returns True if user has any instance permission level
   */
  public hasAnyInstancePermissions(): boolean {
    return this.hasAnyRole([
      Role.INSTANCE_VIEWER,
      Role.INSTANCE_EDITOR,
      Role.INSTANCE_ADMIN,
      Role.ORG_ADMIN
    ]);
  }
}