import { Injectable } from '@angular/core';
import { KeycloakService } from 'keycloak-angular';
import { TaasUserDetails } from 'utimaco-common-ui-angular';
import { TaasJwtTokenDetails } from '../models/taas-jwt-token-details';
//import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class TaasUserService {
  private username: string | null = null;
  //private sfdc_organization_id: string | null=null;

  // private tokenDataSubject = new BehaviorSubject<any>(null);
  // tokenData$ = this.tokenDataSubject.asObservable();

  constructor(private keycloakService: KeycloakService) { }
  
  async getCurrentUserDetails(): Promise<TaasUserDetails> {
    const tokenString = await this.keycloakService.getToken();
    //console.log(tokenString);
    const tokenObject: TaasJwtTokenDetails = JSON.parse(atob(tokenString.split('.')[1]));
    const userDetails: TaasUserDetails = {
      givenName: tokenObject.given_name,
      familyName: tokenObject.family_name,
      email: tokenObject.email,
      initials: tokenObject.given_name.trim().substring(0, 1) + tokenObject.family_name.trim().substring(0, 1),
    }
    //this.tokenDataSubject.next(tokenObject['sfdc-organisation-id']);
    //console.log(tokenObject['sfdc-organisation-id']);
    //this.sfdc_organization_id=tokenObject['sdfc-organisation-id'];
    this.username=userDetails.givenName;
    return userDetails;
  }



  // getSfdcOrgID(): Observable<any> {
  //   return this.tokenData$;
  // }
  
  // Get the username
  getUsername(): string | null {
    return this.username;
  }

}
