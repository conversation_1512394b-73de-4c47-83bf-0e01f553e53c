import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { environment } from '@kmaas-environments/environment';
import { catchError, Observable, of, from, switchMap } from 'rxjs';
import { TaasUserService } from '../../services/taas-user-service.service';
import { UserRoleService } from '../../services/role/user-role.service';


interface KmipClientData {
  clientId: string;
  password: string;
  certificate: string;
}

// export interface ClientCertificate {
//   clientId: string;
//   createdBy: string;
//   creationDate: string;
//   lastModified: string;
//   certificateStartDate: string;
//   certificateExpirationDate: string;
// }

export interface ClientDetails {
  clientId: string;
  userId: string;
  creationDate: string;
  lastModified: string;
  certStartDate: string;
  certExpirationDate: string;
  certificate:string;
}

// export interface KmipClientResponse {
//   success: boolean;
//   message: string;
//   data?: any;
//   status?: number;
//   body?: {
//     data: ClientDetails[];
//   };
//   error?: {
//     message: string;
//     code: number;
//   };
// }

export interface KmipClientResponse {
  body: any;
  success: boolean;
  message: string;
  data: ClientDetails;
  code?: string;
  timestamp?: string;
  tid?: string;

}

export interface UpdateRequest {
  clientId: string;
  newCertificate: string;
  newPassword: string;
}

export interface ErrorResponse {
  success: boolean;
  data: null;
  code: string;
  message: string;
  timestamp: string;
  tid: string;
}

export interface UpdateResponse {
  success: boolean;
  data?: any;
  message?: string;
}

@Injectable({
  providedIn: 'root'
})
export class TaasKmipClientService {
  // Use inject() instead of constructor injection
  private http = inject(HttpClient);
  private taasUserService = inject(TaasUserService);
  private userRoleService = inject(UserRoleService);
  
  baseUrl = environment.ekmaasBaseUrl;
  javaApiUrl = this.baseUrl + "/api/v1/";


  registerClient(clientData:KmipClientData): Observable<any> {
    return this.http.post<any>(this.javaApiUrl + "kmip/clients", clientData).pipe(
      catchError(error => {
        return of(error);
        // Rethrow the error so it can be handled by the subscriber
        //throw error;
      })
    );
  }

  getClientDetails(clientId: string): Observable<KmipClientResponse> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    return this.http.get<KmipClientResponse>(`${this.javaApiUrl}kmip/clients/${clientId}`,
      {
        headers: headers,
        observe: 'response'
      }
    ).pipe(catchError(err => {
      return of(err);
    }));
  }

  updateKmipClient(request: UpdateRequest): Observable<UpdateResponse> {
    console.log(request);
    if(request.newCertificate)
    {
      return this.http.put<UpdateResponse>(`${this.javaApiUrl}kmip/clients/${request.clientId}`, {
        certificate: request.newCertificate
      });
    }
    return this.http.put<UpdateResponse>(`${this.javaApiUrl}kmip/clients/${request.clientId}`, {
      password: request.newPassword
    });

    }

    // updateClientPassword(request: PasswordUpdateRequest): Observable<UpdateResponse> {
    //   return this.http.put<UpdateResponse>(`${this.javaApiUrl}kmip/clients/${request.clientId}`, {
    //     password: request.newPassword
    //   });
    // }

  // getKmipObjects(clientId: string): Observable<KmipObjectsTableData[]> {
  //   return this.http.get<KmipObjectsTableData[]>(`${this.javaApiUrl}kmip/clients/${clientId}`);
  // }


  // registerClient(clientId: string, password: string, cert: string): Observable<any> {
  //   const clientData = {
  //     clientId: clientId,
  //     password: password,
  //     certificate: cert
  //   };

  //   return this.http.post<any>(this.javaApiUrl + "kmip/clients", clientData).pipe(
  //     catchError(error => {
  //       return of(error);
  //     })
  //   );
  // }

  // getkmipClientsData(): Observable<any> {
  //   return this.http.get(
  //     this.javaApiUrl + "kmip/clients", { observe: 'response' }
  //   ).pipe(catchError(err => {
  //     return of(err);
  //   }));
  // }

  getkmipClientsData(): Observable<any> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    // Use RxJS streams to add user context to request
    return from(this.addUserContextToRequest()).pipe(
      switchMap(userContext => {
        // Build query params with user context for backend filtering
        let url = this.javaApiUrl + "kmip/clients";
        
        // Add user context as query parameters
        // This allows the backend to filter based on user permissions
        if (userContext) {
          url += `?requestingUserId=${encodeURIComponent(userContext.userId)}`;
          
          // Add roles as comma-separated list
          if (userContext.roles && userContext.roles.length) {
            url += `&requestingUserRoles=${encodeURIComponent(userContext.roles.join(','))}`;
          }
        }
        
        return this.http.get(
          url,
          {
            headers: headers,
            observe: 'response'
          }
        );
      }),
      catchError(err => {
        return of(err);
      })
    );
  }
  
  /**
   * Gets current user context (ID and roles) for backend filtering
   * @returns Promise with user context object
   */
  private async addUserContextToRequest(): Promise<any> {
    try {
      const currentUser = await this.taasUserService.getCurrentUserDetails();
      const userRoles = this.userRoleService.getUserRoles();
      
      return {
        userId: currentUser.email,
        roles: userRoles
      };
    } catch (error) {
      console.error('Error getting user context for KMIP request:', error);
      return null;
    }
  }
}
