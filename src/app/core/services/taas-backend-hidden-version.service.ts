// import { HttpClient, HttpHeaders } from '@angular/common/http';
// import { Injectable } from '@angular/core';
// import { environment } from '@kmaas-environments/environment';
// import { TaasUserService } from './taas-user-service.service';
// import { catchError, Observable, of } from 'rxjs';


import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { catchError, Observable, of } from 'rxjs';
import { HttpResponse } from '@angular/common/http';

interface AppVersionResponse {
  // TODO: Infer correct interface for this type
  [key: string]: any;
}
import { environment } from '../../../environments/environment';


@Injectable({
  providedIn: 'root'
})
export class TaasBackendHiddenVersionService {

    private baseUrl = environment.ekmaasBaseUrl;
    private javaApiUrl = this.baseUrl + "/api/v1/";
  
    constructor(private http: HttpClient) {}
  
    getBackendVersion(): Observable<HttpResponse<AppVersionResponse>> {
      //console.log("getBackendVersion");
      return this.http.get<AppVersionResponse>(
        this.javaApiUrl + "setting/appversion",
        { observe: 'response' }
      ).pipe(
        catchError(err => of(err))
      );
    }
}
