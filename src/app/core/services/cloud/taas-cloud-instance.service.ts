import { Injectable } from '@angular/core';
import { Observable, Subject, of } from 'rxjs';
import { HttpClient } from '@angular/common/http';
//import { AuthService } from '../auth/auth.service';
import { catchError } from 'rxjs/operators';
import { environment } from '@kmaas-environments/environment';


@Injectable({
  providedIn: 'root'
})
export class TaasCloudInstanceService {

  //customerId = "EKMaaS-POC-0013X00002nrvr4QAA";
  
  baseUrl = environment.ekmaasBaseUrl;
  constructor(private http: HttpClient) {
  }

  javaApiUrl = this.baseUrl + "/api/v1/";
  cloudInstanceList = new Subject<any>();
  cloudInstanceListObservable: Observable<any> = this.cloudInstanceList.asObservable();
  manageKeysList = new Subject<any>();
  manageKeysListObservable: Observable<any> = this.manageKeysList.asObservable();
  keyVersionList = new Subject<any>();
  keyVersionListObservable: Observable<any> = this.keyVersionList.asObservable();
  stepperNext = new Subject<any>();
  stepperNextObservable: Observable<any> = this.stepperNext.asObservable();

  //cloud keys change event
  cloudInstanceChanged() {
    //this.cloudInstanceList.next();
    this.cloudInstanceList.next(true);
  }
  //key change event
  manageKeysChanged() {
    //this.manageKeysList.next();
    this.manageKeysList.next(true);
  }
  keyVersionChanged(field: any) {
    this.keyVersionList.next(field);
  }

  //fetch cloud providers support call
  getSupportedCloudProviders(): Observable<any> {
    //if(!this.authService.isLoggedIn()) return of({status:400,error:{ errorMessage:'Your session has timed out. Please login again'}});
    return this.http.get(
      //this.javaApiUrl+"cloud/support", { observe: 'response' }
      this.javaApiUrl + "cloud/providers", { observe: 'response' }
    ).pipe(catchError(err => {
      return of(err);
    }));
  }
  //get settings data
  getSettings() {
    // const ekmaasHeaders = new HttpHeaders();
    // ekmaasHeaders.set('X-Customer-ID', this.customerId);
    //if(!this.authService.isLoggedIn()) return of({status:400,error:{ errorMessage:'Your session has timed out. Please login again'}});
    return this.http.get(
      this.javaApiUrl + "cloud/settings", { observe: 'response' }
    ).pipe(catchError(err => {
      return of(err);
    }));
  }
  //fetch cloud instance data call
  getCloudDashboardData(): Observable<any> {
    //if(!this.authService.isLoggedIn()) return of({status:400,error:{ errorMessage:'User not logged in'}});
    return this.http.get(
      //this.javaApiUrl+"cloud/instance",{ observe: 'response' }
      this.javaApiUrl + "cloud/instances", { observe: 'response' }
    ).pipe(catchError(err => {
      return of(err);
    }));
  }
  //fetch cloud keys call
  //fetch cloud keys call
  getCloudKeysData(id: number): Observable<any> {
    //if(!this.authService.isLoggedIn()) return of({status:400,error:{ errorMessage:'Your session has timed out. Please login again'}});
    return this.http.get(
      this.javaApiUrl + "cloud/instances/" + id + "/keys", { observe: 'response' }
    ).pipe(catchError(err => {
      return of(err);
    }));
  }
  //fetch cloud instance call
  getCloudInstance(cloudInstanceId: number): Observable<any> {
    //if(!this.authService.isLoggedIn()) return of({status:400,error:{ errorMessage:'Your session has timed out. Please login again'}});
    return this.http.get(
      //this.javaApiUrl+"cloud/instance/"+cloudInstanceId, { observe: 'response' }
      this.javaApiUrl + "cloud/instances/" + cloudInstanceId, { observe: 'response'}
    ).pipe(catchError(err => {
      return of(err);
    }));
  }

  getCustomKeyAttributes(cloudInstanceId: any, cloudKey: any, fetchCloudKeyDetails = false, fetchESKMKeyDetails = false) {
    //if(!this.authService.isLoggedIn()) return of({status:400,error:{ errorMessage:'Your session has timed out. Please login again'}});
    if (fetchESKMKeyDetails)
      return this.http.get(
        this.javaApiUrl + "cloud/instance/" + cloudInstanceId + "/eskm-key/" + cloudKey.keyId, { observe: 'response' }
      ).pipe(catchError(err => {
        return of(err);
      }));
    if (fetchCloudKeyDetails) {
      let data = {};
      data = JSON.parse(JSON.stringify(cloudKey.keyAttributes));
      // try { data=JSON.parse(JSON.stringify(cloudKey.keyAttributes)); }
      // catch(e){}
      return this.http.post(
        this.javaApiUrl + "cloud/instance/" + cloudInstanceId + "/cloud-key/" + cloudKey.keyId, data, { observe: 'response'}
      ).pipe(catchError(err => {
        return of(err);
      }));
    }
    return of({ status: 400, body: null });
  }

  //add new cloud instance call
  addCloudInstance(cloudInstance: any, testConfig: boolean) {
    //if(!this.authService.isLoggedIn()) return of({status:400,error:{ errorMessage:'Your session has timed out. Please login again'}});
    const newCloudInstance = testConfig ? {
      instanceName: cloudInstance.instanceName,
      cloudType: cloudInstance.cloudType,
      configFields: JSON.stringify(cloudInstance.configFields),
      testConfig: testConfig
    } : {
      instanceName: cloudInstance.instanceName,
      cloudType: cloudInstance.cloudType,
      configFields: JSON.stringify(cloudInstance.configFields)
    }
    return this.http.post(
      //this.javaApiUrl+"cloud/instance",newCloudInstance, { observe: 'response' }
      this.javaApiUrl + "cloud/instances", newCloudInstance, { observe: 'response' }
    ).pipe(catchError(err => {
      return of(err);
    }));
  }

  // //fetch algorithms call
  getAlgorithmList(cloudType: any): Observable<any> {
    //if(!this.authService.isLoggedIn()) return of({status:400,error:{ errorMessage:'Your session has timed out. Please login again'}});
    return this.http.get(
      this.javaApiUrl + "cloud/providers/" + cloudType + "/keytype", { observe: 'response'}
    ).pipe(catchError(err => {
      return of(err);
    }));
  }
  // //fetch keys call
  getSearchKeys(excludeCloudInstanceID: any, cloudType: any): Observable<any> {//Search Keys associated with cloud instance
    //if(!this.authService.isLoggedIn()) return of({status:400,error:{ errorMessage:'Your session has timed out. Please login again'}});
    return this.http.get(
      this.javaApiUrl + "cloud/instances/eskm-keys/search?cloudType=" + cloudType, { observe: 'response'}
    ).pipe(catchError(err => {
      return of(err);
    }));
  }

  // //fetch search association call
  getSearchSelectAssociation(cloudInstanceID: any, eskmKeyName: any): Observable<any> {
    //if(!this.authService.isLoggedIn()) return of({status:400,error:{ errorMessage:'Your session has timed out. Please login again'}});
    if (cloudInstanceID) return this.http.get(
      this.javaApiUrl + "cloud/instances/eskm-keys/associations?instanceid=" + cloudInstanceID + "&eskmkeyname=" + eskmKeyName, { observe: 'response' }
    ).pipe(catchError(err => {
      return of(err);
    }));
    else return this.http.get(
      this.javaApiUrl + "cloud/instances/eskm-keys/associations?eskmkeyname=" + eskmKeyName, { observe: 'response'}
    ).pipe(catchError(err => {
      return of(err);
    }));
  }

  //update cloud instance call
  updateCloudInstance(cloudInstance: any, selectedCloudInstanceId: any, testConfig: boolean) {
    //if(!this.authService.isLoggedIn()) return of({status:400,error:{ errorMessage:'Your session has timed out. Please login again'}});
    const updatedCloudInstance = {
      configFields: JSON.stringify(cloudInstance.configFields),
      testConfig: testConfig
    }
    return this.http.put(
      this.javaApiUrl + "cloud/instances/" + selectedCloudInstanceId, updatedCloudInstance, { observe: 'response'}
    ).pipe(catchError(err => {
      return of(err);
    }));
  }
  //delete cloud instance call
  deleteCloudInstance(cloudInstanceId: any) {
    //if(!this.authService.isLoggedIn()) return of({status:400,error:{ errorMessage:'Your session has timed out. Please login again'}});
    return this.http.delete(
      this.javaApiUrl + "cloud/instances/" + cloudInstanceId, { observe: 'response' }
    ).pipe(catchError(err => {
      return of(err);
    }));
  }

  //add new key call
  addNewKey(key: any, instance_id: string) {
    //if(!this.authService.isLoggedIn()) return of({status:400,error:{ errorMessage:'Your session has timed out. Please login again'}});
    return this.http.post(
      this.javaApiUrl + "cloud/instances/" + instance_id + "/keys", key, { observe: 'response' }
    ).pipe(catchError(err => {
      return of(err);
    }));
  }
  //upload key call
  uploadKey(key: any, cloudInstance: any) {
    //if(!this.authService.isLoggedIn()) return of({status:400,error:{ errorMessage:'Your session has timed out. Please login again'}});
    const uploadKey = {
      eskmKeyName: key.keyName,
      eskmKeyOwner: key.keyOwner,
      keyAlgoSize: key.algorithm + '-' + key.keyLength,
      cloudKeyName: key.cloudKeyName,
      keyFields: JSON.stringify(key.keyFields)
    }
    return this.http.post(
      this.javaApiUrl + "cloud/instances/" + cloudInstance.id + "/keys/upload", uploadKey, { observe: 'response' }
    ).pipe(catchError(err => {
      return of(err);
    }));
  }

  //perform key action
  performKeyAction(cloudInstanceId: number, key: any, action: any, onForceDelete = false) {
    //if(!this.authService.isLoggedIn()) return of({status:400,error:{ errorMessage:'Your session has timed out. Please login again'}});
    const keyName = key.name && key.name !== '-' && !onForceDelete ? key.name : (key.keySource.split('(ESKM').length > 0 ? key.keySource.split('(ESKM')[0] : '');
    return this.http.put(
      this.javaApiUrl + "cloud/instances/" + cloudInstanceId + "/keys/" + keyName, action, { observe: 'response'}
    ).pipe(catchError(err => {
      return of(err);
    }));
  }

  // //fetch custom attributes data
  // getCustomKeyAttributes(cloudInstanceId,cloudKey,fetchCloudKeyDetails=false,fetchESKMKeyDetails=false){
  //   //if(!this.authService.isLoggedIn()) return of({status:400,error:{ errorMessage:'Your session has timed out. Please login again'}});
  //   if(fetchESKMKeyDetails)
  //     return this.http.get(
  //       this.javaApiUrl+"cloud/instance/"+cloudInstanceId+"/eskm-key/"+cloudKey.keyId, { observe: 'response' }
  //     ).pipe(catchError(err=> {
  //       return of(err);
  //   }));
  //   if(fetchCloudKeyDetails){
  //     let data={};
  //     try { data=JSON.parse(JSON.stringify(cloudKey.keyAttributes)); }
  //     catch(e){}
  //     return this.http.post(
  //       this.javaApiUrl+"cloud/instance/"+cloudInstanceId+"/cloud-key/"+cloudKey.keyId, data, { observe: 'response' }
  //     ).pipe(catchError(err=> {
  //       return of(err);
  //   }));}
  // }

  //update settings data
  // updateSettings(settings){
  //   //if(!this.authService.isLoggedIn()) return of({status:400,error:{ errorMessage:'Your session has timed out. Please login again'}});
  //   return this.http.put(
  //     this.javaApiUrl+"cloud/settings",settings, { observe: 'response' }
  //   ).pipe(catchError(err=> {
  //       return of(err);
  //   }));
  // }
  // //get settings data
  // //get settings data
  getGridListValues(cloudInstanceId: any, cloudKey: any) {
    //if(!this.authService.isLoggedIn()) return of({status:400,error:{ errorMessage:'Your session has timed out. Please login again'}});
    return this.http.get(
      this.javaApiUrl + "cloud/instance/" + cloudInstanceId + "/eskm-key/" + cloudKey.keyId + "/versionlist", { observe: 'response' }
    ).pipe(catchError(err => {
      return of(err);
    }));
    // return of({status:200,error:{errorMessage:''},body:{}})
  }

  // //fetch SF instance call
  // getSfCaCertFordInstance(cloudInstanceId:number):Observable<any> {
  //   //if(!this.authService.isLoggedIn()) return of({status:400,error:{ errorMessage:'Your session has timed out. Please login again'}});
  //   return this.http.get(
  //     this.javaApiUrl+"cloud/instance/"+cloudInstanceId+"/SF-CertList", { observe: 'response' }
  //   ).pipe(catchError(err=> {
  //       return of(err);
  //   }));
  // }

}
