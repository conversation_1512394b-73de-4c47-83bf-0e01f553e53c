import { Injectable, inject } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { TaasMenuItem } from 'utimaco-common-ui-angular';
import { TAAS_MENU_ITEM_LIST_TS } from '../../../taas-menuitems';
import { UserRoleService } from '../role/user-role.service';
import { Role } from '../../models/roles.enum';

/**
 * Service to provide role-based menu items
 * This filters the main navigation menu based on the user's roles
 */
@Injectable({
  providedIn: 'root'
})
export class RoleBasedMenuService {
  private menuItemsSubject: BehaviorSubject<TaasMenuItem[]> = new BehaviorSubject<TaasMenuItem[]>([]);
  public menuItems$: Observable<TaasMenuItem[]> = this.menuItemsSubject.asObservable();

  // Define role requirements for each menu section/link
  private menuItemRoleRequirements: { [menuId: number]: Role[] } = {
    // Dashboard - Accessible to all users with EKMAAS_USER or higher
    0: [Role.EKMAAS_USER, Role.EKMAAS_USER_MANAGER, Role.EKMAAS_SERVICE_ADMIN, Role.ORG_ADMIN],
    
    // Cloud Clients section
    1: [Role.EKMAAS_USER, Role.EKMAAS_USER_MANAGER, Role.EKMAAS_SERVICE_ADMIN, Role.ORG_ADMIN],
    
    // Cloud Clients - Azure
    6: [Role.EKMAAS_USER, Role.EKMAAS_USER_MANAGER, Role.EKMAAS_SERVICE_ADMIN, Role.ORG_ADMIN, Role.AZURE_INSTANCE_CREATOR],
    
    // Cloud Clients - AWS
    7: [Role.EKMAAS_USER, Role.EKMAAS_USER_MANAGER, Role.EKMAAS_SERVICE_ADMIN, Role.ORG_ADMIN, Role.AWS_INSTANCE_CREATOR],
    
    // Cloud Clients - Google
    8: [Role.EKMAAS_USER, Role.EKMAAS_USER_MANAGER, Role.EKMAAS_SERVICE_ADMIN, Role.ORG_ADMIN, Role.GCP_INSTANCE_CREATOR],
    
    // Enterprise Clients section
    2: [Role.EKMAAS_USER, Role.EKMAAS_USER_MANAGER, Role.EKMAAS_SERVICE_ADMIN, Role.ORG_ADMIN],
    
    // Enterprise Clients - KMIP
    9: [Role.EKMAAS_USER, Role.EKMAAS_USER_MANAGER, Role.EKMAAS_SERVICE_ADMIN, Role.ORG_ADMIN, Role.KMIP_INSTANCE_CREATOR],
    
    // Enterprise Clients - TDE
    10: [Role.EKMAAS_USER, Role.EKMAAS_USER_MANAGER, Role.EKMAAS_SERVICE_ADMIN, Role.ORG_ADMIN],
    
    // Enterprise Clients - REST
    11: [Role.EKMAAS_USER, Role.EKMAAS_USER_MANAGER, Role.EKMAAS_SERVICE_ADMIN, Role.ORG_ADMIN, Role.REST_INSTANCE_CREATOR],
    
    // Access Management section - Only for user managers and admins
    3: [Role.EKMAAS_USER_MANAGER, Role.EKMAAS_SERVICE_ADMIN, Role.ORG_ADMIN],
    
    // Access Management - Users
    12: [Role.EKMAAS_USER_MANAGER, Role.EKMAAS_SERVICE_ADMIN, Role.ORG_ADMIN],
    
    // Access Management - Groups
    13: [Role.EKMAAS_USER_MANAGER, Role.EKMAAS_SERVICE_ADMIN, Role.ORG_ADMIN],
    
    // Audit Log - Accessible to users with appropriate permissions
    4: [Role.EKMAAS_USER, Role.EKMAAS_USER_MANAGER, Role.EKMAAS_SERVICE_ADMIN, Role.ORG_ADMIN],
    
    // Settings (Root of Trust) - Only for org admin
    5: [Role.ORG_ADMIN]
  };

  private userRoleService = inject(UserRoleService);

  constructor() {
    // Subscribe to role changes to update menu items
    this.userRoleService.userRoles$.subscribe(roles => {
      this.updateMenuItems(roles);
    });

    this.updateMenuItems(this.userRoleService.getUserRoles());
  }

  /**
   * Update menu items based on user roles
   */
  private updateMenuItems(userRoles: Role[]): void {
    // Make a deep copy of the original menu items
    const menuItemsCopy = this.deepCopyMenuItems(TAAS_MENU_ITEM_LIST_TS);
    
    // Filter the menu items based on roles
    const filteredMenuItems = this.filterMenuItemsByRole(menuItemsCopy, userRoles);
    
    // Update the subject with filtered items
    this.menuItemsSubject.next(filteredMenuItems);
  }

  /**
   * Deep copy menu items to avoid modifying the original
   */
  private deepCopyMenuItems(menuItems: TaasMenuItem[]): TaasMenuItem[] {
    return JSON.parse(JSON.stringify(menuItems));
  }

  /**
   * Filter menu items based on user roles
   */
  private filterMenuItemsByRole(menuItems: TaasMenuItem[], userRoles: Role[]): TaasMenuItem[] {
    // Filter top-level items
    const filteredItems = menuItems.filter(item => {
      // Check if user has any of the required roles for this item
      const requiredRoles = this.menuItemRoleRequirements[item.id] || [];
      const hasAccess = requiredRoles.length === 0 || 
                        requiredRoles.some(role => userRoles.includes(role));
      
      if (!hasAccess) {
        return false;
      }
      
      // If it's a section with children, filter the children too
      if (item.type === 'section' && item.children && item.children.length > 0) {
        item.children = this.filterMenuItemsByRole(item.children, userRoles);
        
        // If no children left after filtering, hide the section
        if (item.children.length === 0) {
          return false;
        }
      }
      
      return true;
    });
    
    return filteredItems;
  }

  /**
   * Get the current filtered menu items
   */
  public getMenuItems(): TaasMenuItem[] {
    return this.menuItemsSubject.getValue();
  }
}