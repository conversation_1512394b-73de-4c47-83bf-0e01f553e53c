import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { catchError, Observable, of } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { UserRoleService } from '../role/user-role.service';
import { TaasUserService } from '../taas-user-service.service';

 
@Injectable({
  providedIn: 'root'
})
export class TaasAuditLogService {

  baseUrl = environment.ekmaasBaseUrl;
  javaApiUrl= this.baseUrl +"/api/v1/";
  private userRoleService = inject(UserRoleService);
  private taasUserService = inject(TaasUserService);
  private http = inject(HttpClient);

   //baseUrl="https://iad1-devekmaas-vhsm03.us.utimaco.cloud:443";
  //  baseUrl = "https://ams1-preprod-ekmaas-vm04.us.utimaco.cloud:443"
  // javaApiUrl= this.baseUrl +"/api/v1/";
 
  //  baseUrl="http://localhost:8083";
  //javaApiUrl= this.baseUrl +"/api/v1/";

  getAuditLogs(params: HttpParams): Observable<any> {
   // console.log("entered log service");
    // Convert HttpParams to plain object
    const paramsObject:any = {};
    params.keys().forEach(key => {
      paramsObject[key] = params.get(key);
    });
    
    // Add role-based parameters to the request
    // The backend will handle role-based filtering.
    // The frontend will just send the user's identity and roles.
    this.addRoleBasedParameters(paramsObject);
 
    return this.http.post(
      `${this.javaApiUrl}log/auditlogs`,
      paramsObject,
      {
        observe: 'response'
       }
    ).pipe(
      catchError(err => {
        //console.error('Error fetching audit logs:', err);
        return of(err);
      })
    );
  }
  
  /**
   * Add role-based parameters to the audit log request
   * This allows the backend to properly filter logs based on the user's role
   * @param params The parameters object to modify
   */
  private async addRoleBasedParameters(params: any): Promise<void> {
    const currentUser = await this.taasUserService.getCurrentUserDetails();
    const userRoles = this.userRoleService.getUserRoles();

    // Send the current user's ID and their roles to the backend.
    params['requestingUserId'] = currentUser.email;
    params['requestingUserRoles'] = userRoles;
  }
}