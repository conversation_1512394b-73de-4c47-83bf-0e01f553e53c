import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import {
  Observable,
  map,
  catchError,
  shareReplay,
  firstValueFrom,
} from 'rxjs';
import {
  User,
  Instance,
  UserInstance,
  Permission,
  SaveUserPermissionsRequest,
  ApiResponse,
  ApiInstance,
} from '../../models/access-management';
import { environment } from '../../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class TaasAccessManagementService {
  baseUrl = environment.ekmaasBaseUrl;
  javaApiUrl = this.baseUrl + '/api/v1/';

  constructor(private http: HttpClient) {}

  // getPermissions(): Observable<Permission[]> {
  //   return this.http.get<Permission[]>(`${this.javaApiUrl}permissions/values`);
  // }

  getUsers(): Observable<User[]> {
    return this.http
      .get<ApiResponse<User[]>>(`${this.javaApiUrl}access-management/users`)
      .pipe(map((response: { data: any }) => response.data));
  }

  getAvailableInstances(): Observable<Instance[]> {
    return this.http
      .get<ApiResponse<ApiInstance[]>>(`${this.javaApiUrl}instances/`)
      .pipe(
        map((response: ApiResponse<ApiInstance[]>) =>
          response.data.map((instance) => ({
            id: instance.id.toString(),
            instanceName: instance.instanceName,
            instanceType: instance.instanceType.instanceTypeName,
          }))
        )
      );
  }

  // New method to fetch instances with type parameter (like Azure, KMIP)
  getAvailableInstancesByType(instanceType: string): Observable<Instance[]> {
    return this.http
      .get<ApiResponse<ApiInstance[]>>(`${this.javaApiUrl}instances/?type=${instanceType}`)
      .pipe(
        map((response: ApiResponse<ApiInstance[]>) =>
          response.data.map((instance) => ({
            id: instance.id.toString(),
            instanceName: instance.instanceName,
            instanceType: instance.instanceType.instanceTypeName,
          }))
        )
      );
  }
  getUserPermissions(userId: string): Observable<UserInstance[]> {
    return this.http
      .get<ApiResponse<UserInstance[]>>(
        `${this.javaApiUrl}permissions/user/${userId}`
      )
      .pipe(
        map((response: { data: any }) => {
          return response.data.map((userInstance: UserInstance) => ({
            ...userInstance,
            instanceId: userInstance.instanceId.toString(),
          }));
        })
      );
  }

  // New method to get user permissions with instance type filtering
  getUserPermissionsByType(userId: string, instanceType: string): Observable<UserInstance[]> {
    return this.http
      .get<ApiResponse<UserInstance[]>>(
        `${this.javaApiUrl}permissions/user/${userId}?type=${instanceType}`
      )
      .pipe(
        map((response: { data: any }) => {
          return response.data.map((userInstance: UserInstance) => ({
            ...userInstance,
            instanceId: userInstance.instanceId.toString(),
          }));
        })
      );
  }

  saveUserPermissions(
    request: SaveUserPermissionsRequest
  ): Observable<boolean> {
    console.log('API: Saving permissions with request:', request);
    console.log('Current permissions:', request.currentPermissions);
    console.log('Original permissions:', request.originalPermissions);

    return this.http
      .put<ApiResponse<boolean>>(
        `${this.javaApiUrl}permissions/user/${request.userId}`,
        request
      )
      .pipe(
        map((response: ApiResponse<boolean>) => {
          console.log('API Response:', response);
          return response.success === 'true' || response.data === true;
        }),
        catchError((error) => {
          console.error('Error saving user permissions:', error);
          throw error;
        })
      );
  }

  getPermissions(): Observable<Permission[]> {
    return this.http.get<{data: Permission[]}>(`${this.javaApiUrl}permissions/values`)
      .pipe(
        map(response => response.data)
      );
  }

  private permissions$ = this.getPermissions().pipe(shareReplay(1));

  async getPermissionIdByName(permissionName: string): Promise<number> {
    const permissions = await firstValueFrom(this.permissions$);
    const permission = permissions.find(p => {
        return p.permissionName.toLowerCase() === permissionName.toLowerCase();
    });
    if (permission) {
        return permission.id;
    } else {
        return this.getDefaultPermissionId(permissionName.toLowerCase());
    }
}

  private getDefaultPermissionId(permissionName: string): number {
    switch (permissionName) {
      case 'admin':
        return 3;
      case 'editor':
        return 2;
      case 'viewer':
      default:
        return 1;
    }
  }
}
