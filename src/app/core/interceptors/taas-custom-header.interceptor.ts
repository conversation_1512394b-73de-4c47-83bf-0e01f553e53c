// import { HttpInterceptorFn } from '@angular/common/http';

// export const taasCustomHeaderInterceptor: HttpInterceptorFn = (req, next) => {
//   return next(req);
// };
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { KeycloakService } from 'keycloak-angular';
import { HttpRequestPayload } from './models/http-request-payload';
import { HttpEventResponse } from './models/http-event-response';
import { JwtPayload } from './models/jwt-payload';  

@Injectable()
export class taasCustomHeaderInterceptor implements HttpInterceptor {
  constructor(private keycloakService: KeycloakService) {}

    intercept(request: HttpRequest<HttpRequestPayload>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<HttpEventResponse>> {

    const token = this.keycloakService.getKeycloakInstance().token;
    //console.log("token",token);
    if (token) {
      const tokenInfo = this.parseJwt(token);
      // console.log("tokenInfo",tokenInfo);
      // console.log("tokenInfo-sfdc",tokenInfo['sfdc-organisation-id']);
      const modifiedRequest = request.clone({
        setHeaders: {
          'X-Customer-Id': tokenInfo['organizationId'],
          // 'User-ID': tokenInfo.sub || '',
          // 'User-Role': tokenInfo.realm_access?.roles?.join(',') || '',
        }
      });

      return next.handle(modifiedRequest);
    }
    return next.handle(request);
  }

  private parseJwt(token: string): JwtPayload {

    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      return JSON.parse(window.atob(base64));
    } catch (error) {
      console.error('Error parsing JWT token:', error);
      return { 'sfdc-organisation-id': 'default' }; // Provide a default value
    }
  }
}