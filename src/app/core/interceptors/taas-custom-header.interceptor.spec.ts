import { TestBed } from '@angular/core/testing';
import { HTTP_INTERCEPTORS, HttpRequest, HttpHandler, HttpEvent, HttpResponse } from '@angular/common/http';
import { of, Observable } from 'rxjs';

import { taasCustomHeaderInterceptor } from './taas-custom-header.interceptor';
import { KeycloakService } from 'keycloak-angular';
import { HttpRequestPayload } from './models/http-request-payload';

describe('taasCustomHeaderInterceptor', () => {
  let interceptor: taasCustomHeaderInterceptor;
  let keycloakServiceSpy: jasmine.SpyObj<KeycloakService>;

  beforeEach(() => {
    keycloakServiceSpy = jasmine.createSpyObj('KeycloakService', ['getKeycloakInstance']);
    // Mock the token property using a getter
    Object.defineProperty(keycloakServiceSpy, 'getKeycloakInstance', {
      get: () => ({ token: 'test-token' }),
    });

    TestBed.configureTestingModule({
      providers: [
        taasCustomHeaderInterceptor,
        { provide: KeycloakService, useValue: keycloakServiceSpy },
        {
          provide: HTTP_INTERCEPTORS,
          useClass: taasCustomHeaderInterceptor,
          multi: true,
        },
      ],
    });
    interceptor = TestBed.inject(taasCustomHeaderInterceptor);
  });

  it('should be created', () => {
    expect(interceptor).toBeTruthy();
  });

  it('should add the X-Customer-Id header', () => {
    const req = new HttpRequest<HttpRequestPayload>('GET', '/test');
    const next: HttpHandler = {
      handle: (request: HttpRequest<any>): Observable<HttpEvent<any>> => {
        expect(request.headers.has('X-Customer-Id')).toBe(true);
        return of(new HttpResponse()); // Return a valid HttpEvent
      },
    };

    interceptor.intercept(req, next).subscribe();
  });
});
