import { Role } from './roles.enum';

/**
 * A flexible mapping of various role string formats to the Role enum.
 * This helps in consistently interpreting roles from different sources (JWT, APIs).
 */
export const ROLE_MAPPING: { [key: string]: Role } = {
  // Core organization roles with various formats
  'org-admin': Role.ORG_ADMIN,
  'org_admin': Role.ORG_ADMIN,
  'orgadmin': Role.ORG_ADMIN,
  'admin': Role.ORG_ADMIN,

  'ekmaas-user-manager': Role.EKMAAS_USER_MANAGER,
  'ekmaas_user_manager': Role.EKMAAS_USER_MANAGER,
  'user-manager': Role.EKMAAS_USER_MANAGER,
  'user_manager': Role.EKMAAS_USER_MANAGER,

  'ekmaas-user': Role.EKMAAS_USER,
  'ekmaas_user': Role.EKMAAS_USER,
  'user': Role.EKMAAS_USER,

  'ekmaas-service-admin': Role.EKMAAS_SERVICE_ADMIN,
  'ekmaas_service_admin': Role.EKMAAS_SERVICE_ADMIN,
  'service-admin': Role.EKMAAS_SERVICE_ADMIN,
  'service_admin': Role.EKMAAS_SERVICE_ADMIN,

  // Instance creator roles
  'azure-instance-creator': Role.AZURE_INSTANCE_CREATOR,
  'azure_instance_creator': Role.AZURE_INSTANCE_CREATOR,
  'azureinstancecreator': Role.AZURE_INSTANCE_CREATOR,

  'aws-instance-creator': Role.AWS_INSTANCE_CREATOR,
  'aws_instance_creator': Role.AWS_INSTANCE_CREATOR,
  'awsinstancecreator': Role.AWS_INSTANCE_CREATOR,

  'gcp-instance-creator': Role.GCP_INSTANCE_CREATOR,
  'gcp_instance_creator': Role.GCP_INSTANCE_CREATOR,
  'gcpinstancecreator': Role.GCP_INSTANCE_CREATOR,

  'kmip-instance-creator': Role.KMIP_INSTANCE_CREATOR,
  'kmip_instance_creator': Role.KMIP_INSTANCE_CREATOR,
  'kmipinstancecreator': Role.KMIP_INSTANCE_CREATOR,

  'kms-instance-creator': Role.KMS_INSTANCE_CREATOR,
  'kms_instance_creator': Role.KMS_INSTANCE_CREATOR,
  'kmsinstancecreator': Role.KMS_INSTANCE_CREATOR,

  'p11-instance-creator': Role.P11_INSTANCE_CREATOR,
  'p11_instance_creator': Role.P11_INSTANCE_CREATOR,
  'p11instancecreator': Role.P11_INSTANCE_CREATOR,

  'rest-instance-creator': Role.REST_INSTANCE_CREATOR,
  'rest_instance_creator': Role.REST_INSTANCE_CREATOR,
  'restinstancecreator': Role.REST_INSTANCE_CREATOR,

  // Instance-specific permission levels
  'instance-viewer': Role.INSTANCE_VIEWER,
  'instance_viewer': Role.INSTANCE_VIEWER,
  'viewer': Role.INSTANCE_VIEWER,

  'instance-editor': Role.INSTANCE_EDITOR,
  'instance_editor': Role.INSTANCE_EDITOR,
  'editor': Role.INSTANCE_EDITOR,

  'instance-admin': Role.INSTANCE_ADMIN,
  'instance_admin': Role.INSTANCE_ADMIN,
  'instanceadmin': Role.INSTANCE_ADMIN
};

/**
 * Common role groupings used throughout the application.
 * This helps maintain consistency and reduces duplication in role definitions.
 */

// Base roles that most users have access to
export const BASE_USER_ROLES = [
  Role.EKMAAS_USER,
  Role.EKMAAS_USER_MANAGER,
  Role.EKMAAS_SERVICE_ADMIN,
  Role.ORG_ADMIN
] as const;

// Administrative roles (excluding basic users)
export const ADMIN_ROLES = [
  Role.EKMAAS_USER_MANAGER,
  Role.EKMAAS_SERVICE_ADMIN,
  Role.ORG_ADMIN
] as const;

// Only organization admin
export const ORG_ADMIN_ONLY = [Role.ORG_ADMIN] as const;

// Cloud provider specific role combinations
export const AZURE_ROLES = [...BASE_USER_ROLES, Role.AZURE_INSTANCE_CREATOR] as const;
export const AWS_ROLES = [...BASE_USER_ROLES, Role.AWS_INSTANCE_CREATOR] as const;
export const GCP_ROLES = [...BASE_USER_ROLES, Role.GCP_INSTANCE_CREATOR] as const;

// Enterprise client specific role combinations
export const KMIP_ROLES = [...BASE_USER_ROLES, Role.KMIP_INSTANCE_CREATOR] as const;
export const REST_ROLES = [...BASE_USER_ROLES, Role.REST_INSTANCE_CREATOR] as const;

// TDE uses base roles without additional creator role
export const TDE_ROLES = BASE_USER_ROLES;

// Audit log access - users with any instance permission level or admin roles
export const AUDIT_LOG_ROLES = [
  ...BASE_USER_ROLES,
  Role.INSTANCE_VIEWER,
  Role.INSTANCE_EDITOR,
  Role.INSTANCE_ADMIN
] as const;