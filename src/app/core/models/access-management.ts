// export interface User {
//   id: number;
//   name: string;
//   // No instances array here - we'll fetch permissions separately
// }

export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
}

export interface InstanceType {
  id: number;
  instanceTypeName: string;
}

export interface ApiInstance {
  id: string;
  instanceName: string;
  instanceType: InstanceType;
  creator: string;
  organizationId: string;
  creationDate: string;
  lastModified: string;
}

export interface Instance {
  id: string;
  instanceName: string;
  instanceType: string;
}

export interface Permission {
  id: number;
  permissionName: string; // 'viewer', 'editor', or 'admin'
}

// export interface UserInstance {
//   instanceId: string;
//   instanceName: string;
//   permissions: string; // 'viewer', 'editor', or 'admin'
//   permissionId: number; // Added permission ID for backend operations
// }


export interface UserInstance {
  instanceId: string;
  instanceName: string;
  permissionName: string | null; // 'viewer', 'editor', 'admin', or null for deleted
  permissionId: number; // -1 for deleted instances
  instanceGroup?: boolean;
  //instance_type:string; // for future task
}

// New interface for the save request payload
export interface SaveUserPermissionsRequest {
  userId: string;
  userName: string;
  currentPermissions: UserInstance[];
  originalPermissions: UserInstance[];
}

export interface ApiResponse<T> {
  success: string;
  data: T;
  message: string;
  code: string;
  tid: string;
  ts: string;
}

// export interface InstancesRecord {
//   fields: { [key: string]: string };
// }
