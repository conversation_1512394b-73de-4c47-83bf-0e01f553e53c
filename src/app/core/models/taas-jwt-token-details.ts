export interface TaasJwtTokenDetails {
    acr: string,
    "allowed-origins": Array<string>
    aud: Array<string>
    auth_time: number
    azp: string
    email: string
    email_verified: boolean
    exp: number
    family_name: string
    given_name: string
    iat: number
    iss: string
    jti: string
    name: string
    nonce: string
    preferred_username: string
    realm_access: { roles: Array<string> }
    resource_access: object
    scope: string
    "organizationId": string
    session_state: string
    sid: string
    sub: string
    typ: string
}