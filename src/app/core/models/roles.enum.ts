/**
 * Enum representing all possible user roles in the EKMaaS system.
 * This provides a central location to manage all role types. Combining API and JWT roles.
 */
export enum Role {
  // Core organization roles
  ORG_ADMIN = 'org-admin',
  EKMAAS_USER_MANAGER = 'ekmaas-user-manager',
  EKMAAS_USER = 'ekmaas-user',
  EKMAAS_SERVICE_ADMIN = 'ekmaas-service-admin',
  
  // Instance creator roles (global)
  AZURE_INSTANCE_CREATOR = 'azure-instance-creator',
  AWS_INSTANCE_CREATOR = 'aws-instance-creator',
  GCP_INSTANCE_CREATOR = 'gcp-instance-creator',
  KMIP_INSTANCE_CREATOR = 'kmip-instance-creator',
  KMS_INSTANCE_CREATOR = 'kms-instance-creator',
  P11_INSTANCE_CREATOR = 'p11-instance-creator',
  REST_INSTANCE_CREATOR = 'rest-instance-creator',
  
  // Instance-specific permission levels (applied to ekmaas-user)
  INSTANCE_VIEWER = 'instance-viewer',
  INSTANCE_EDITOR = 'instance-editor',
  INSTANCE_ADMIN = 'instance-admin',
  
  // Default role when no specific role is assigned
  NONE = 'none'
}