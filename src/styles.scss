// Custom Theming for Angular Material
// For more information: https://material.angular.io/guide/theming
@use '@angular/material' as mat;
// Plus imports for other components in your app.
@use "../node_modules/utimaco-common-ui-angular/assets/global_style_variables.scss" as taas;

// Include the common styles for Angular Material. We include this here so that you only
// have to load a single css file for Angular Material in your app.
// Be sure that you only ever include this mixin once!
@include mat.core();

// Define the palettes for your theme using the Material Design palettes available in palette.scss
// (imported above). For each palette, you can optionally specify a default, lighter, and darker
// hue. Available color palettes: https://material.io/design/color/
$taas-portal-primary: mat.define-palette(taas.$md-utimacoportal-primary, 500);
$taas-portal-accent: mat.define-palette(taas.$md-utimacoportal-secondary, 500);

// The warn palette is optional (defaults to red).
$utimaco-portal-mock-warn: mat.define-palette(mat.$red-palette);

// Create the theme object. A theme consists of configurations for individual
// theming systems such as "color" or "typography".
$taas-portal-theme: mat.define-light-theme((color: (primary: $taas-portal-primary,
        accent: $taas-portal-accent,
        warn: $utimaco-portal-mock-warn,
      ),
      typography: mat.define-typography-config(),
      density: 0));

// Include theme styles for core and each component used in your app.
// Alternatively, you can import and @include the theme mixins for each component
// that you are using.
@include mat.all-component-themes($taas-portal-theme);
/* You can add global styles to this file, and also import other style files */

.mat-mdc-card,
.md-card,
.mat-mdc-card-content {
  border-radius: 0px !important;
}

mat-error {
    color: rgba(244, 67, 54, 0.8) !important;
    font-size: 12px;
    margin-top: 4px;
    display: block;
  }

button, .mdc-button {
  border-radius: 3px !important;
}

html, body { height: 100%; }
body { margin: 0; font-family: Roboto, "Helvetica Neue", sans-serif; }

.full-width {
    width: 100%;
}

.half-width {
    width: 50%;
}

.align-center {
    text-align: center;
}

.margin-center {
    margin: 0 auto !important;
}

button {
    outline: none !important;
}

.mat-dialog-container {
    box-shadow: none !important;
}

@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: local('Roboto'),
        url('./assets/Roboto-Regular.ttf') format('truetype');
}

@font-face {
    font-family: 'Material Icons';
    font-style: normal;
    font-weight: 400;
    src: url('./assets/MaterialIcons-Regular.eot'); /* For IE6-8 */
    src: local('Material Icons'),
        local('MaterialIcons-Regular'),
        url('./assets/MaterialIcons-Regular.woff2') format('woff2'),
        url('./assets/MaterialIcons-Regular.woff') format('woff'),
        url('./assets/MaterialIcons-Regular.ttf') format('truetype');
}

.material-icons {
    font-family: 'Material Icons';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;

    /* Support for all WebKit browsers. */
    -webkit-font-smoothing: antialiased;

    /* Support for Safari and Chrome. */
    text-rendering: optimizeLegibility;

    /* Support for Firefox. */
    -moz-osx-font-smoothing: grayscale;

    /* Support for IE. */
    font-feature-settings: 'liga';
}

.mat-step-icon {
    border-radius: 50%;
    color:#fff;
}
.mat-step-icon-state-edit{
    background-color: #0068b4!important;
    border: 1px solid;
    padding: 15px;
    box-shadow: 0px 0px 5px 2px #0068b4!important;
}
.mat-step-icon-selected{
    background-color: #0068b4!important;
    border: 1px solid;
    padding: 15px;
    box-shadow: 0px 0px 5px 2px #41a2b0!important;
}
.mat-step-icon.mat-step-icon-state-number{
    background-color: #c3c3c3;
    border: 1px solid;
    padding: 15px;
    box-shadow: 0px 0px 5px 2px #c3c3c3;
}
.mat-step-label{
    min-width: 0px!important;
}
// .mat-error{
//     color:red;
// }
app-taas-stepper-view{
    font-size:smaller;
}
app-taas-stepper-view .mat-horizontal-content-container{
    padding-bottom: 0px;
}
app-taas-stepper-view .mat-horizontal-stepper-header-container{
    max-width: 85%;
    margin: auto;
}
app-taas-stepper-view .uploadDisabled .mat-horizontal-stepper-header-container{
    max-width: 60%!important;
    margin: auto;
}
app-taas-stepper-view mat-radio-group{
    width: 100%;
}
.primeng-table {
    .ui-column-icon.pi{
        padding-bottom: 5px;
    }
    .ui-sortable-column-icon.pi-sort-alt::before {
        content: "\f0dc";
        font-family: "Font Awesome\ 5 Free";
        font-weight: 900;
    }
    .ui-sortable-column-icon.pi-sort-amount-down::before {
        content: "\f0d7";
        font-family: "Font Awesome\ 5 Free";
        font-weight: 900;
    }
    .ui-sortable-column-icon.pi-sort-amount-up-alt::before {
        content: "\f0d8";
        font-family: "Font Awesome\ 5 Free";
        font-weight: 900;
    }
    .p-dropdown-label.p-inputtext{
        padding-top: 5px;
    }
    thead,tbody{
        tr{
            th,td{
                border:none!important;
            }
        }
    }
    .p-paginator .ui-paginator{
        border:none!important;
    }
}

.modal-header{
    //background-color: #02787b;
    background-color: #0068b4;
    color: #fff;
    font-size: 15px;
    font-weight: 500;
    padding: 0.8rem 0.8rem!important;
}
.modal-content{
    border-radius: 0.5rem!important;
}
ng-moment-timezone-picker {
    .wrapper{
        width: 100%!important;
        height: 30px!important;
    }
    .ng-select .ng-select-container{
        min-height: 0px;
        border-top: 0.5em solid transparent !important;
        padding: 0em!important;
        .ng-value-container {
            padding-top: 0px;
            border-top: none;
        }
    }
    .ng-placeholder{
        top: 12px!important;
        display: none!important;
    }
    .ng-value-container{
        padding-left: 10px!important;
    }
    .ng-arrow-wrapper{
        margin-right: 10px;
    }
}

//help menu z=index fixes
.help-dialog{
    z-index: 1000!important;
}
.modal-backdrop{
    z-index: 998!important;
}
#addCloudInstanceModal,#editCloudInstanceModal,#uploadKeyModal,#reuploadKeyModal,#cloudSettingsModal{
    z-index: 999!important;
}
body .ui-table .ui-table-tbody>tr>td{
    word-wrap: break-word;
}
//remove table selection shadows
body .ui-table .ui-table-tbody > tr:focus + tr > td {
    box-shadow: none!important;
}
body .ui-table .ui-table-tbody > tr:focus > td {
    box-shadow: none!important;
}
body .ui-table .ui-table-tbody > tr:focus > td:first-child {
    box-shadow: none!important;
}
body .ui-table .ui-table-tbody > tr:focus > td:last-child {
    box-shadow: none!important;
}

#keyDetailsModal{
    app-taas-shared-input mat-icon.mat-icon{
        display: none;
    }
}
app-taas-manage-cloud-keys p-table {
    .ui-table .ui-table-tbody > tr.ui-state-highlight,
    .ui-table .ui-table-tbody > tr:nth-child(2n).ui-state-highlight {
        background-color: inherit;
        color: inherit;
    }
}
app-taas-cloud-listing {
    .primeng-table p-paginator .ui-paginator{
        min-width: 840px;
    }
}
@media (max-width: 992px){
    .hsmContainer.container {
        max-width: 100%;
    }
}
.disabled{
    app-taas-shared-input {
        input.largeInput,select.largeInput {
            background-color: rgba(239, 239, 239, 0.3);
            border-bottom: none;
        }
    }
}
ng-moment-timezone-picker .ng-select .ng-select-container .ng-value-container .ng-input>input{
    cursor:text;
}
input[type=checkbox],input[type=checkbox],app-taas-shared-input mat-icon{
    cursor: pointer;
}
.mat-horizontal-stepper-header{
    pointer-events: none !important;
}
body .ui-table .ui-sortable-column.ui-state-highlight{
    background-color: #f4f4f4!important;
    color: #333!important;
}
body .ui-table .ui-sortable-column.ui-state-highlight .ui-sortable-column-icon{
    color: #848484!important;
}
app-taas-manage-cloud-keys {
    #versionConfirmationModal .modal-dialog{
        padding-top: 50px;
    }
}


// margin-top: 60px;
//  margin-left: 80px;
