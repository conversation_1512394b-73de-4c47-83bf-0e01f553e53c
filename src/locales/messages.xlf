<?xml version="1.0" encoding="UTF-8" ?>
<xliff version="2.0" xmlns="urn:oasis:names:tc:xliff:document:2.0" srcLang="en">
  <file id="ngi18n" original="ng.template">
    <unit id="1076527898293452775">
      <segment>
        <source>Error while trying to fetch cloud instances</source>
      </segment>
    </unit>
    <unit id="4827075895754858675">
      <segment>
        <source>Error while trying to fetch supported cloud types</source>
      </segment>
    </unit>
    <unit id="802560322927792951">
      <segment>
        <source>Error while trying to delete Cloud Instance</source>
      </segment>
    </unit>
    <unit id="1306823280448276106">
      <notes>
        <note category="description">Manage Cloud Keys: label for Cloud Instance</note>
      </notes>
      <segment>
        <source> Cloud Instance: </source>
      </segment>
    </unit>
    <unit id="4154974451532903749">
      <notes>
        <note category="description">CloudListing: Text for No Cloud Instances</note>
      </notes>
      <segment>
        <source> No Cloud Instances Available </source>
      </segment>
    </unit>
    <unit id="2375260419993138758">
      <notes>
        <note category="description">Edit Cloud Instance: label field for Key URI</note>
      </notes>
      <segment>
        <source>URL</source>
      </segment>
    </unit>
    <unit id="2702472467711841394">
      <notes>
        <note category="description">Reupload Key: label for Cloud Key Name</note>
      </notes>
      <segment>
        <source>*Cloud Key Name</source>
      </segment>
    </unit>
    <unit id="3013382452583924970">
      <notes>
        <note category="description">Reupload Key: label for EKMaas Key Name</note>
      </notes>
      <segment>
        <source>*EKMaaS Key Name</source>
      </segment>
    </unit>
    <unit id="3243919419794524214">
      <notes>
        <note category="description">Manage Cloud Keys: button text for Create key</note>
      </notes>
      <segment>
        <source>Create Key</source>
      </segment>
    </unit>
    <unit id="3660013413509868257">
      <notes>
        <note category="description">Stepper-View: label for Select key</note>
      </notes>
      <segment>
        <source>Select Key</source>
      </segment>
    </unit>
    <unit id="3768927257183755959">
      <notes>
        <note category="description">AddKmipClient:button label for confirm</note>
      </notes>
      <segment>
        <source>Save</source>
      </segment>
    </unit>
    <unit id="4359018163043204777">
      <segment>
        <source>Key Name</source>
      </segment>
    </unit>
    <unit id="4397712718833981257">
      <notes>
        <note category="description">Shared-Input: timezone text</note>
      </notes>
      <segment>
        <source><ph id="0" equiv="INTERPOLATION" disp="{{ getLocalizedDateTimeLabel(inputField) }}"/> Timezone</source>
      </segment>
    </unit>
    <unit id="4630944461097377180">
      <notes>
        <note category="description">Manage Cloud Keys: button text for Create/Upload key</note>
      </notes>
      <segment>
        <source>Create/Upload Key</source>
      </segment>
    </unit>
    <unit id="4694447476013124795">
      <notes>
        <note category="description">Manage Cloud Keys: label for Key URI</note>
      </notes>
      <segment>
        <source>Key URI</source>
      </segment>
    </unit>
    <unit id="4824444844546691624">
      <segment>
        <source>Algorithm</source>
      </segment>
    </unit>
    <unit id="1924075649164405038">
      <notes>
        <note category="description">CloudListing: Button text for Manage Keys</note>
      </notes>
      <segment>
        <source> Manage Keys </source>
      </segment>
    </unit>
    <unit id="5252961089687283406">
      <notes>
        <note category="description">Manage Cloud Keys: No keys available text</note>
      </notes>
      <segment>
        <source>No Keys Available</source>
      </segment>
    </unit>
    <unit id="4207916966377787111">
      <segment>
        <source>Created</source>
      </segment>
    </unit>
    <unit id="6377618342030058654">
      <notes>
        <note category="description">Stepper-View: label for key name</note>
      </notes>
      <segment>
        <source>*Key Name</source>
      </segment>
    </unit>
    <unit id="6905322870062458300">
      <segment>
        <source>Instance Name</source>
      </segment>
    </unit>
    <unit id="121196129628862893">
      <notes>
        <note category="description">Stepper-View:button text for Upload</note>
      </notes>
      <segment>
        <source> Upload</source>
      </segment>
    </unit>
    <unit id="7000398097814012645">
      <notes>
        <note category="description">Stepper-View: label for key owner</note>
      </notes>
      <segment>
        <source>Key Owner</source>
      </segment>
    </unit>
    <unit id="7022070615528435141">
      <notes>
        <note category="description">KmipClientListing: Button text for Delete Kmip Client</note>
      </notes>
      <segment>
        <source>Delete</source>
      </segment>
    </unit>
    <unit id="7267599172607716807">
      <segment>
        <source>Cloud Type</source>
      </segment>
    </unit>
    <unit id="7642895943748781278">
      <notes>
        <note category="description">Manage Cloud Keys: label for DKE Key URL</note>
      </notes>
      <segment>
        <source>DKE Key URL</source>
      </segment>
    </unit>
    <unit id="7650820226512704357">
      <notes>
        <note category="description">Manage Cloud Keys: label for Key Algorithm</note>
      </notes>
      <segment>
        <source>Key Algorithm</source>
      </segment>
    </unit>
    <unit id="6141915193625957942">
      <notes>
        <note category="description">AddCloudInstance: label field Cloud Type</note>
      </notes>
      <segment>
        <source>* Cloud Type</source>
      </segment>
    </unit>
    <unit id="688560883283893567">
      <notes>
        <note category="description">Manage Cloud Keys: button text for Close</note>
      </notes>
      <segment>
        <source> Close</source>
      </segment>
    </unit>
    <unit id="939617489946732670">
      <notes>
        <note category="description">AddCloudInstance: label field Instance Name</note>
      </notes>
      <segment>
        <source>* Instance Name</source>
      </segment>
    </unit>
    <unit id="8514015417905907775">
      <notes>
        <note category="description">Stepper-View-FinalUpload: label for EKMaaS Key Name</note>
      </notes>
      <segment>
        <source>EKMaaS Key Name</source>
      </segment>
    </unit>
    <unit id="8111132081545684874">
      <notes>
        <note category="description">CloudListing: Button text for Delete Instance</note>
      </notes>
      <segment>
        <source> Delete Instance </source>
      </segment>
    </unit>
    <unit id="8953033926734869941">
      <notes>
        <note category="description">Manage Cloud Keys-KeyDetails: label for Key Name</note>
      </notes>
      <segment>
        <source>Name</source>
      </segment>
    </unit>
    <unit id="8988290600780497522">
      <notes>
        <note category="description">Stepper-View: label for algorithm</note>
      </notes>
      <segment>
        <source>*Algorithm</source>
      </segment>
    </unit>
    <unit id="9216117865911519658">
      <segment>
        <source>Action</source>
      </segment>
    </unit>
    <unit id="4134736808256671237">
      <notes>
        <note category="description">Edit Cloud Instance: text for not editable Instance</note>
      </notes>
      <segment>
        <source> Note: This instance is not editable. </source>
      </segment>
    </unit>
    <unit id="activationDate_timezone">
      <notes>
        <note category="description">SharedInput-ReUploadKey</note>
      </notes>
      <segment>
        <source>Activation Date</source>
      </segment>
    </unit>
    <unit id="addBtnText.add">
      <notes>
        <note category="description">AddCloudInstance-Add</note>
      </notes>
      <segment>
        <source>Add</source>
      </segment>
    </unit>
    <unit id="addBtnText.verify">
      <notes>
        <note category="description">AddCloudInstance-Verify</note>
      </notes>
      <segment>
        <source>Verify</source>
      </segment>
    </unit>
    <unit id="alert_header">
      <notes>
        <note category="description">ReUpload Key-Header for confirmation box</note>
      </notes>
      <segment>
        <source>Alert!</source>
      </segment>
    </unit>
    <unit id="character">
      <notes>
        <note category="description">Value Comparison text Msg</note>
      </notes>
      <segment>
        <source> characters</source>
      </segment>
    </unit>
    <unit id="confirmation_msg">
      <notes>
        <note category="description">ReUpload Key-Confirmation box content</note>
      </notes>
      <segment>
        <source>Are you sure you want to create a new version for </source>
      </segment>
    </unit>
    <unit id="confirmation_success_msg">
      <notes>
        <note category="description">ReUpload Key-Confirmation Header</note>
      </notes>
      <segment>
        <source>Success!</source>
      </segment>
    </unit>
    <unit id="create_upload_label">
      <notes>
        <note category="description">Stepper-View-Create key Name for radio button in stepper view</note>
      </notes>
      <segment>
        <source>Create Key &amp; Upload</source>
      </segment>
    </unit>
    <unit id="delete_alert">
      <notes>
        <note category="description">Manage Cloud Key-Header for confirmation box</note>
      </notes>
      <segment>
        <source>Alert!</source>
      </segment>
    </unit>
    <unit id="delete_instance_warn">
      <notes>
        <note category="description">EditCloudInstance-delete instance warn</note>
      </notes>
      <segment>
        <source>Warning: Instance will be deleted</source>
      </segment>
    </unit>
    <unit id="deleteActiveKeyConfirmation">
      <notes>
        <note category="meaning">Manage Cloud Keys-Confirmation message for key deletion</note>
      </notes>
      <segment>
        <source>Are you sure you want to delete key &lt;span class=&quot;no-break&quot;&gt;(<ph id="0" equiv="INTERPOLATION" disp="eskmKeyName"/>)&lt;/span&gt; from EKMaaS?</source>
      </segment>
    </unit>
    <unit id="deleteFromEKMaaS">
      <notes>
        <note category="meaning">Manage Cloud Keys-Message for delete from EKMaaS option</note>
      </notes>
      <segment>
        <source>Delete from EKMaaS</source>
      </segment>
    </unit>
    <unit id="deleteKeyConfirmation">
      <notes>
        <note category="meaning">Manage Cloud Keys-Confirmation message for key deletion</note>
      </notes>
      <segment>
        <source>Are you sure you want to delete key &lt;span class=&quot;no-break&quot;&gt;(<ph id="0" equiv="INTERPOLATION" disp="key.name"/>)&lt;/span&gt; from &lt;span class=&quot;no-break&quot;&gt;<ph id="1" equiv="INTERPOLATION_1" disp="this.selectedCloudInstance.instanceName"/> (<ph id="2" equiv="INTERPOLATION_2" disp="this.selectedCloudType.name"/>)&lt;/span&gt;?</source>
      </segment>
    </unit>
    <unit id="deleteNoStatusKeyConfirmation">
      <notes>
        <note category="meaning">Manage Cloud Keys-Confirmation message for key deletion</note>
      </notes>
      <segment>
        <source>Are you sure you want to delete key &lt;span class=&quot;no-break&quot;&gt;(<ph id="0" equiv="INTERPOLATION" disp="!key.name &amp;&amp; this.selectedCloudType.name.toLowerCase().includes(&apos;salesforce-byok&apos;) ? &apos;Version - &apos; + key.keyAttributes[&apos;version&apos;] : key.name"/>)&lt;/span&gt; from &lt;span class=&quot;no-break&quot;&gt;<ph id="1" equiv="INTERPOLATION_1" disp="this.selectedCloudInstance.instanceName"/> (<ph id="2" equiv="INTERPOLATION_2" disp="this.selectedCloudType.name"/>)&lt;/span&gt;?</source>
      </segment>
    </unit>
    <unit id="deleteNotUploadedKeyConfirmation">
      <notes>
        <note category="meaning">Manage Cloud Keys-Confirmation message for key deletion</note>
      </notes>
      <segment>
        <source>Are you sure you want to delete key &lt;span class=&quot;no-break&quot;&gt;(<ph id="0" equiv="INTERPOLATION" disp="eskmKeyName"/>)&lt;/span&gt; from EKMaaS?</source>
      </segment>
    </unit>
    <unit id="edit_label">
      <notes>
        <note category="description">Manage Cloud Keys-dropdown label for Edit</note>
      </notes>
      <segment>
        <source>Edit</source>
      </segment>
    </unit>
    <unit id="editCloudInstanceButton.text">
      <notes>
        <note category="description">EditCloudInstance-button text</note>
      </notes>
      <segment>
        <source>Verify</source>
      </segment>
    </unit>
    <unit id="editKeyPrefix">
      <notes>
        <note category="description">Manage Cloud Keys</note>
      </notes>
      <segment>
        <source>Edit key </source>
      </segment>
    </unit>
    <unit id="editKeySuffix">
      <notes>
        <note category="description">Manage Cloud Keys</note>
      </notes>
      <segment>
        <source> on </source>
      </segment>
    </unit>
    <unit id="expirationDate_timezone">
      <notes>
        <note category="description">SharedInput-ReUploadKey</note>
      </notes>
      <segment>
        <source>Expiration Date</source>
      </segment>
    </unit>
    <unit id="field_required">
      <notes>
        <note category="description">Field Required Validation Msg</note>
      </notes>
      <segment>
        <source>Field Required</source>
      </segment>
    </unit>
    <unit id="field.activationDate">
      <notes>
        <note category="description">Activatin Date field</note>
      </notes>
      <segment>
        <source>Activation Date</source>
      </segment>
    </unit>
    <unit id="field.clientId">
      <notes>
        <note category="description">Client ID label</note>
      </notes>
      <segment>
        <source>Client ID</source>
      </segment>
    </unit>
    <unit id="field.clientSecret">
      <notes>
        <note category="description">Client Secret label</note>
      </notes>
      <segment>
        <source>Client Secret</source>
      </segment>
    </unit>
    <unit id="field.enabled">
      <notes>
        <note category="description">Enabled field</note>
      </notes>
      <segment>
        <source>Enabled</source>
      </segment>
    </unit>
    <unit id="field.expirationDate">
      <notes>
        <note category="description">Expiration Date field</note>
      </notes>
      <segment>
        <source>Expiration Date</source>
      </segment>
    </unit>
    <unit id="field.keyVault">
      <notes>
        <note category="description">KeyVault field</note>
      </notes>
      <segment>
        <source>Key Vault</source>
      </segment>
    </unit>
    <unit id="field.keyVaults">
      <notes>
        <note category="description">Key Vault label</note>
      </notes>
      <segment>
        <source>Key Vaults</source>
      </segment>
    </unit>
    <unit id="field.tags">
      <notes>
        <note category="description">Tags field</note>
      </notes>
      <segment>
        <source>Tags</source>
      </segment>
    </unit>
    <unit id="field.tenantId">
      <notes>
        <note category="description">Tenant ID label</note>
      </notes>
      <segment>
        <source>Tenant ID</source>
      </segment>
    </unit>
    <unit id="header">
      <notes>
        <note category="description">Stepper-View-Header for confirmation box</note>
      </notes>
      <segment>
        <source>Alert!</source>
      </segment>
    </unit>
    <unit id="instance_delete_warning">
      <notes>
        <note category="description">Stepper-View-Service Account Validation Msg</note>
      </notes>
      <segment>
        <source>Warning: Instance will be deleted if no service account are present</source>
      </segment>
    </unit>
    <unit id="invalid_date">
      <notes>
        <note category="description">Stepper-View-Invalid Datetime validation Msg</note>
      </notes>
      <segment>
        <source>Invalid datetime value</source>
      </segment>
    </unit>
    <unit id="key_owned_text">
      <notes>
        <note category="description">Stepper-View-Key Form Validity Msg text</note>
      </notes>
      <segment>
        <source>Key is owned by </source>
      </segment>
    </unit>
    <unit id="key_selection_label">
      <notes>
        <note category="description">Stepper-View-Key Selection label for events in stepper view</note>
      </notes>
      <segment>
        <source>Key Selection</source>
      </segment>
    </unit>
    <unit id="key_validation">
      <notes>
        <note category="description">Stepper-View-Msg for Validation Success</note>
      </notes>
      <segment>
        <source>Key created successfully on EKMaaS</source>
      </segment>
    </unit>
    <unit id="key_validation_warning">
      <notes>
        <note category="description">Stepper-View-Warning text</note>
      </notes>
      <segment>
        <source>Warning: </source>
      </segment>
    </unit>
    <unit id="keyAssociation.deleteNote">
      <notes>
        <note category="description">Manage Cloud Keys-Note on deleted keys</note>
      </notes>
      <segment>
        <source>Note: Key will be deleted only from <ph id="0" equiv="INTERPOLATION" disp="this.selectedCloudType.name"/></source>
      </segment>
    </unit>
    <unit id="onVerified.text">
      <notes>
        <note category="description">EditCloudInstance-button text for update</note>
      </notes>
      <segment>
        <source>Update</source>
      </segment>
    </unit>
    <unit id="placeholderText.keyVaults">
      <notes>
        <note category="description">SharedInput-AddCloudInstance</note>
      </notes>
      <segment>
        <source>Specify comma separated values for multiple key vault</source>
      </segment>
    </unit>
    <unit id="select_existing_label">
      <notes>
        <note category="description">Stepper-View-Existing Key Name for radio button in stepper view</note>
      </notes>
      <segment>
        <source>Select Existing Key</source>
      </segment>
    </unit>
    <unit id="select_label">
      <notes>
        <note category="description">Manage Cloud Keys-dropdown label for Select</note>
      </notes>
      <segment>
        <source>Select</source>
      </segment>
    </unit>
    <unit id="success_header">
      <notes>
        <note category="description">Stepper-View-Confirmation Header</note>
      </notes>
      <segment>
        <source>Success!</source>
      </segment>
    </unit>
    <unit id="summary_label">
      <notes>
        <note category="description">Stepper-View-Summary label for events in stepper view</note>
      </notes>
      <segment>
        <source>Summary</source>
      </segment>
    </unit>
    <unit id="uniqueness_validation">
      <notes>
        <note category="description">Validate multi-select or value-array input</note>
      </notes>
      <segment>
        <source> Field values must be unique</source>
      </segment>
    </unit>
    <unit id="updateCloudInstanceButton.text">
      <notes>
        <note category="description">EditCloudInstance-button text for update</note>
      </notes>
      <segment>
        <source>Update</source>
      </segment>
    </unit>
    <unit id="upload_key_label">
      <notes>
        <note category="description">Stepper-View-Upload Key label for events in stepper view</note>
      </notes>
      <segment>
        <source>Upload Key</source>
      </segment>
    </unit>
    <unit id="upload_label">
      <notes>
        <note category="description">Manage Cloud Keys-dropdown label for Upload</note>
      </notes>
      <segment>
        <source>Upload</source>
      </segment>
    </unit>
    <unit id="uploadKeyPrefix">
      <notes>
        <note category="description">Manage Cloud Keys</note>
      </notes>
      <segment>
        <source>Upload key </source>
      </segment>
    </unit>
    <unit id="uploadKeySuffix">
      <notes>
        <note category="description">Manage Cloud Keys</note>
      </notes>
      <segment>
        <source> to </source>
      </segment>
    </unit>
    <unit id="validate_instance">
      <notes>
        <note category="description">AddCloudInstance-New Cloud Instance Validation Msg</note>
      </notes>
      <segment>
        <source>Field can only contain letters, numbers, hyphens, underscores, and periods</source>
      </segment>
    </unit>
    <unit id="validation_msg">
      <notes>
        <note category="description">Msg for Validation Check</note>
      </notes>
      <segment>
        <source>Value should be less than </source>
      </segment>
    </unit>
    <unit id="version_confirmation">
      <notes>
        <note category="description">ReUpload Key-Header for confirmation box</note>
      </notes>
      <segment>
        <source>Alert!</source>
      </segment>
    </unit>
    <unit id="versionkey_alert">
      <notes>
        <note category="description">Manage Cloud Key-Header for confirmation box</note>
      </notes>
      <segment>
        <source>Alert!</source>
      </segment>
    </unit>
    <unit id="warn_update_value_array">
      <notes>
        <note category="description">EditCloudInstance-warning on update inputs</note>
      </notes>
      <segment>
        <source>Warning: Instance will be deleted if no service account are present</source>
      </segment>
    </unit>
    <unit id="Warning">
      <notes>
        <note category="description">Stepper-View-Search Key Warning Msg</note>
      </notes>
      <segment>
        <source>Warning: This key is already uploaded to the cloud instance</source>
      </segment>
    </unit>
    <unit id="1356200968511280526">
      <segment>
        <source>REST</source>
      </segment>
    </unit>
    <unit id="1678559204833619774">
      <segment>
        <source>Audit Log</source>
      </segment>
    </unit>
    <unit id="2535123683532102008">
      <segment>
        <source>Assessment Result</source>
      </segment>
    </unit>
    <unit id="2545495424566277668">
      <notes>
        <note category="description">CloudListing: Button text for Edit Instance</note>
      </notes>
      <segment>
        <source> Edit Instance </source>
      </segment>
    </unit>
    <unit id="3014142150963657456">
      <segment>
        <source>Client</source>
      </segment>
    </unit>
    <unit id="3499507106617527960">
      <segment>
        <source>Azure</source>
      </segment>
    </unit>
    <unit id="3590805807378700861">
      <segment>
        <source>Google</source>
      </segment>
    </unit>
    <unit id="3822084371659290422">
      <segment>
        <source>Utimaco TaaS: Enterprise Key Manager</source>
      </segment>
    </unit>
    <unit id="4089509911694721896">
      <segment>
        <source>Last Updated</source>
      </segment>
    </unit>
    <unit id="4221199921973829385">
      <segment>
        <source>KMIP</source>
      </segment>
    </unit>
    <unit id="4555457172864212828">
      <segment>
        <source>Users</source>
      </segment>
    </unit>
    <unit id="4628738230038299640">
      <segment>
        <source>Assessment Date</source>
      </segment>
    </unit>
    <unit id="4816216590591222133">
      <segment>
        <source>Enabled</source>
      </segment>
    </unit>
    <unit id="5275254013832532154">
      <segment>
        <source>Key Vault</source>
      </segment>
    </unit>
    <unit id="5313216855975199451">
      <notes>
        <note category="description">Manage Cloud Keys: Warning text showing key deltion is irreversible</note>
      </notes>
      <segment>
        <source> ♦ Warning! This action will delete the key from </source>
      </segment>
    </unit>
    <unit id="5611592591303869712">
      <segment>
        <source>Status</source>
      </segment>
    </unit>
    <unit id="5944812089887969249">
      <segment>
        <source>Groups</source>
      </segment>
    </unit>
    <unit id="6004681856910260407">
      <segment>
        <source>Creation Date</source>
      </segment>
    </unit>
    <unit id="6083409672770600534">
      <segment>
        <source>AWS</source>
      </segment>
    </unit>
    <unit id="6460207866734534390">
      <segment>
        <source>Cloud Clients</source>
      </segment>
    </unit>
    <unit id="6570363013146073520">
      <notes>
        <note category="description">main-menu home link</note>
      </notes>
      <segment>
        <source>Dashboard</source>
      </segment>
    </unit>
    <unit id="6785659997897155974">
      <segment>
        <source>Setting</source>
      </segment>
    </unit>
    <unit id="6870744132322796712">
      <segment>
        <source>Enterprise Clients</source>
      </segment>
    </unit>
    <unit id="4179734860143712488">
      <notes>
        <note category="description">Stepper-View:button text for Create</note>
      </notes>
      <segment>
        <source> Create</source>
      </segment>
    </unit>
    <unit id="8086244912297636472">
      <segment>
        <source>TDE</source>
      </segment>
    </unit>
    <unit id="8493363135660887954">
      <segment>
        <source>Access Management</source>
      </segment>
    </unit>
    <unit id="9155608366859514313">
      <segment>
        <source>Source</source>
      </segment>
    </unit>
    <unit id="1200900687640843597">
      <segment>
        <source>Creation By</source>
      </segment>
    </unit>
    <unit id="1431416938026210429">
      <notes>
        <note category="description">AddkmipClient: label field for Password</note>
      </notes>
      <segment>
        <source>Password</source>
      </segment>
    </unit>
    <unit id="165617360560009966">
      <segment>
        <source>Certificate Expiration Date</source>
      </segment>
    </unit>
    <unit id="2161902857876513332">
      <notes>
        <note category="description">AddkmipClient: label field for Password Validation Error</note>
      </notes>
      <segment>
        <source> Password is required and must be at least 12 characters </source>
      </segment>
    </unit>
    <unit id="290160340331958183">
      <notes>
        <note category="description">AddkmipClient: label field for Client Certificate Validation Error</note>
      </notes>
      <segment>
        <source> Client Certificate is required </source>
      </segment>
    </unit>
    <unit id="2925129304308690882">
      <segment>
        <source>Last modified</source>
      </segment>
    </unit>
    <unit id="3026976714330562202">
      <notes>
        <note category="description">AddkmipClient: label field for Client Certificate Note</note>
      </notes>
      <segment>
        <source> Note: Client Id and Common Name in the Client Certificate should match </source>
      </segment>
    </unit>
    <unit id="3241357959735682038">
      <notes>
        <note category="description">AddkmipClient: label field for Confirm Password</note>
      </notes>
      <segment>
        <source>Confirm Password</source>
      </segment>
    </unit>
    <unit id="4314910104023472121">
      <notes>
        <note category="description">AddkmipClient: label field for Password Mismatch Validation Error</note>
      </notes>
      <segment>
        <source> Passwords do not match </source>
      </segment>
    </unit>
    <unit id="4865005494711354510">
      <notes>
        <note category="description">AddkmipClient: label field for Confirm Password Validation Error</note>
      </notes>
      <segment>
        <source> Please confirm your password </source>
      </segment>
    </unit>
    <unit id="5273501066845923426">
      <notes>
        <note category="description">AddkmipClient: label field for Client Id</note>
      </notes>
      <segment>
        <source>Client ID</source>
      </segment>
    </unit>
    <unit id="5295998403339413845">
      <notes>
        <note category="description">AddkmipClient: label field for Client Id Validation Error</note>
      </notes>
      <segment>
        <source> Client ID is required </source>
      </segment>
    </unit>
    <unit id="9030336811208796738">
      <notes>
        <note category="description">AddkmipClient: label field for Client Certificate</note>
      </notes>
      <segment>
        <source>Client Certificate</source>
      </segment>
    </unit>
    <unit id="1221596867309460035">
      <notes>
        <note category="description">UpdatekmipClient: label field for New Password Entry</note>
      </notes>
      <segment>
        <source>New Password</source>
      </segment>
    </unit>
    <unit id="1485410082721015205">
      <notes>
        <note category="description">UpdatekmipClient: Error message for Confirm Password Validation</note>
      </notes>
      <segment>
        <source> Confirm Password is required </source>
      </segment>
    </unit>
    <unit id="5252837360733823238">
      <notes>
        <note category="description">ViewkmipClient: label field for Created By</note>
      </notes>
      <segment>
        <source>Created By</source>
      </segment>
    </unit>
    <unit id="5491349027224642844">
      <notes>
        <note category="description">UpdatekmipClient: label field for New Certificate Entry</note>
      </notes>
      <segment>
        <source>New Certificate</source>
      </segment>
    </unit>
    <unit id="5522236499655154147">
      <notes>
        <note category="description">ViewkmipClient: label field for Certificate Start Date</note>
      </notes>
      <segment>
        <source>Certificate Start Date</source>
      </segment>
    </unit>
    <unit id="6164699983643908587">
      <segment>
        <source>Object Name</source>
      </segment>
    </unit>
    <unit id="6582573664846559511">
      <segment>
        <source>Change Password</source>
      </segment>
    </unit>
    <unit id="682467974069032141">
      <segment>
        <source>Change Client Certificate</source>
      </segment>
    </unit>
    <unit id="7716208024960184784">
      <segment>
        <source>UUID</source>
      </segment>
    </unit>
    <unit id="7731102146667330348">
      <notes>
        <note category="description">UpdatekmipClient: Error message for Password Validation</note>
      </notes>
      <segment>
        <source> Password must be at least 8 characters long </source>
      </segment>
    </unit>
    <unit id="8820965746779312111">
      <notes>
        <note category="description">ViewkmipClient: label field for Last Modified</note>
      </notes>
      <segment>
        <source>Last Modified</source>
      </segment>
    </unit>
    <unit id="9132547751630599796">
      <notes>
        <note category="description">UpdatekmipClient: Error message for Password Validation</note>
      </notes>
      <segment>
        <source> Password is required </source>
      </segment>
    </unit>
    <unit id="4350423050822384120">
      <notes>
        <note category="description">AddCloudInstance:title</note>
      </notes>
      <segment>
        <source> Add Cloud Instance</source>
      </segment>
    </unit>
    <unit id="5668632685575118472">
      <notes>
        <note category="description">AddCloudInstance:Cancel</note>
      </notes>
      <segment>
        <source> Cancel</source>
      </segment>
    </unit>
    <unit id="7754924993990855042">
      <notes>
        <note category="description">AddCloudInstance: option field Select</note>
      </notes>
      <segment>
        <source> Select </source>
      </segment>
    </unit>
    <unit id="8323422358213440128">
      <notes>
        <note category="description">AddCloudInstance: Cancel</note>
      </notes>
      <segment>
        <source>
          <ph id="0" equiv="INTERPOLATION" disp="{{ cancelLocalized }}"/>
        </source>
      </segment>
    </unit>
    <unit id="1593087966609132148">
      <notes>
        <note category="description">Upload Key:title</note>
      </notes>
      <segment>
        <source> Upload to </source>
      </segment>
    </unit>
    <unit id="4406493224727225230">
      <notes>
        <note category="description">Stepper-View:button text for Next</note>
      </notes>
      <segment>
        <source> Next</source>
      </segment>
    </unit>
    <unit id="8828229858764392252">
      <notes>
        <note category="description">DeleteCloudInstance:title</note>
      </notes>
      <segment>
        <source> Delete Cloud Instance</source>
      </segment>
    </unit>
    <unit id="9101591947723213375">
      <notes>
        <note category="description">EditCloudInstance:title</note>
      </notes>
      <segment>
        <source> Edit Cloud Instance</source>
      </segment>
    </unit>
    <unit id="5893739794080693805">
      <notes>
        <note category="description">DeleteCloudInstance:delete message</note>
      </notes>
      <segment>
        <source> Are you sure you want to delete instance </source>
      </segment>
    </unit>
    <unit id="1838419884521058275">
      <notes>
        <note category="description">AddKmipClient: Button text for Add Kmip Client</note>
      </notes>
      <segment>
        <source>Add KMIP Client</source>
      </segment>
    </unit>
    <unit id="6132406321239737282">
      <notes>
        <note category="description">KmipClientListing: Button text for Download Kmip Client Certificate</note>
      </notes>
      <segment>
        <source>Download Client Cert</source>
      </segment>
    </unit>
  </file>
</xliff>
