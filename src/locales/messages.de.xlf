<xliff version="2.0" xmlns="urn:oasis:names:tc:xliff:document:2.0" srcLang="en" trgLang="de">
  <file id="ngi18n" original="ng.template">
    <unit id="4827075895754858675">
      <segment state="initial">
        <source>Error while trying to fetch supported cloud types</source>
        <target>Fehler beim Versuch unterstützte Cloud-Typen abzurufen</target>
      </segment>
    </unit>
    <unit id="1076527898293452775">
      <segment state="initial">
        <source>Error while trying to fetch cloud instances</source>
        <target><PERSON><PERSON> beim <PERSON> der <PERSON>-Instanzen</target>
      </segment>
    </unit>
    <unit id="802560322927792951">
      <segment state="initial">
        <source>Error while trying to delete Cloud Instance</source>
        <target>Fehler beim Versuch die Cloud-Instanz zu löschen</target>
      </segment>
    </unit>
    <unit id="939617489946732670">
      <notes>
        <note category="description">AddCloudInstance: label field Instance Name</note>
      </notes>
      <segment state="initial">
        <source>* Instance Name</source>
        <target>*Instanzname</target>
      </segment>
    </unit>
    <unit id="6141915193625957942">
      <notes>
        <note category="description">AddCloudInstance: label field Cloud Type</note>
      </notes>
      <segment state="initial">
        <source>* Cloud Type</source>
        <target>*Cloud-Typ</target>
      </segment>
    </unit>
    <unit id="field_required">
      <notes>
        <note category="description">Field Required Validation Msg</note>
      </notes>
      <segment state="initial">
        <source>Field Required</source>
        <target>Feld erforderlich</target>
      </segment>
    </unit>
    <unit id="validation_msg">
      <notes>
        <note category="description">Msg for Validation Check</note>
      </notes>
      <segment state="initial">
        <source>Value should be less than </source>
        <target>Der Wert sollte kleiner sein als </target>
      </segment>
    </unit>
    <unit id="character">
      <notes>
        <note category="description">Value Comparison text Msg</note>
      </notes>
      <segment state="initial">
        <source> characters</source>
        <target> Zeichen</target>
      </segment>
    </unit>
    <unit id="uniqueness_validation">
      <notes>
        <note category="description">Validate multi-select or value-array input</note>
      </notes>
      <segment state="initial">
        <source> Field values must be unique</source>
        <target> Feldwerte müssen eindeutig sein</target>
      </segment>
    </unit>
    <unit id="field.tenantId">
      <notes>
        <note category="description">Tenant ID label</note>
      </notes>
      <segment state="initial">
        <source>Tenant ID</source>
        <target>Mandanten-ID</target>
      </segment>
    </unit>
    <unit id="field.clientId">
      <notes>
        <note category="description">Client ID label</note>
      </notes>
      <segment state="initial">
        <source>Client ID</source>
        <target>Kunden-ID</target>
      </segment>
    </unit>
    <unit id="field.clientSecret">
      <notes>
        <note category="description">Client Secret label</note>
      </notes>
      <segment state="initial">
        <source>Client Secret</source>
        <target>Client-Geheimnis</target>
      </segment>
    </unit>
    <unit id="field.keyVaults">
      <notes>
        <note category="description">Key Vault label</note>
      </notes>
      <segment state="initial">
        <source>Key Vaults</source>
        <target>Schlüsseltresore</target>
      </segment>
    </unit>
    <unit id="addBtnText.add">
      <notes>
        <note category="description">AddCloudInstance-Add</note>
      </notes>
      <segment state="initial">
        <source>Add</source>
        <target>Hinzufügen</target>
      </segment>
    </unit>
    <unit id="addBtnText.verify">
      <notes>
        <note category="description">AddCloudInstance-Verify</note>
      </notes>
      <segment state="initial">
        <source>Verify</source>
        <target>Verifizieren</target>
      </segment>
    </unit>
    <unit id="validate_instance">
      <notes>
        <note category="description">AddCloudInstance-New Cloud Instance Validation Msg</note>
      </notes>
      <segment state="initial">
        <source>Field can only contain letters, numbers, hyphens, underscores, and periods</source>
        <target>Das Feld darf nur Buchstaben, Zahlen, Bindestriche, Unterstriche und Punkte enthalten</target>
      </segment>
    </unit>
    <unit id="9216117865911519658">
      <segment state="initial">
        <source>Action</source>
        <target>Aktion</target>
      </segment>
    </unit>
    <unit id="1924075649164405038">
      <notes>
        <note category="description">CloudListing: Button text for Manage Keys</note>
      </notes>
      <segment state="initial">
        <source> Manage Keys </source>
        <target> Schlüssel verwalten </target>
      </segment>
    </unit>
    <unit id="8111132081545684874">
      <notes>
        <note category="description">CloudListing: Button text for Delete Instance</note>
      </notes>
      <segment state="initial">
        <source> Delete Instance </source>
        <target> Instanz löschen </target>
      </segment>
    </unit>
    <unit id="4154974451532903749">
      <notes>
        <note category="description">CloudListing: Text for No Cloud Instances</note>
      </notes>
      <segment state="initial">
        <source> No Cloud Instances Available </source>
        <target> Keine Cloud-Instanzen verfügbar </target>
      </segment>
    </unit>
    <unit id="7022070615528435141">
      <notes>
        <note category="description">KmipClientListing: Button text for Delete Kmip Client</note>
      </notes>
      <segment state="initial">
        <source>Delete</source>
        <target>Löschen</target>
      </segment>
    </unit>
    <unit id="6905322870062458300">
      <segment state="initial">
        <source>Instance Name</source>
        <target>Instanzname</target>
      </segment>
    </unit>
    <unit id="7267599172607716807">
      <segment state="initial">
        <source>Cloud Type</source>
        <target>Cloud-Typ</target>
      </segment>
    </unit>
    <unit id="2375260419993138758">
      <notes>
        <note category="description">Edit Cloud Instance: label field for Key URI</note>
      </notes>
      <segment state="initial">
        <source>URL</source>
        <target>URL</target>
      </segment>
    </unit>
    <unit id="4134736808256671237">
      <notes>
        <note category="description">Edit Cloud Instance: text for not editable Instance</note>
      </notes>
      <segment state="initial">
        <source> Note: This instance is not editable. </source>
        <target> Hinweis: Diese Instanz kann nicht bearbeitet werden. </target>
      </segment>
    </unit>
    <unit id="editCloudInstanceButton.text">
      <notes>
        <note category="description">EditCloudInstance-button text</note>
      </notes>
      <segment state="initial">
        <source>Verify</source>
        <target>Verifizieren</target>
      </segment>
    </unit>
    <unit id="delete_instance_warn">
      <notes>
        <note category="description">EditCloudInstance-delete instance warn</note>
      </notes>
      <segment state="initial">
        <source>Warning: Instance will be deleted</source>
        <target>Warnung: Instanz wird gelöscht</target>
      </segment>
    </unit>
    <unit id="updateCloudInstanceButton.text">
      <notes>
        <note category="description">EditCloudInstance-button text for update</note>
      </notes>
      <segment state="initial">
        <source>Update</source>
        <target>Aktualisieren</target>
      </segment>
    </unit>
    <unit id="warn_update_value_array">
      <notes>
        <note category="description">EditCloudInstance-warning on update inputs</note>
      </notes>
      <segment state="initial">
        <source>Warning: Instance will be deleted if no service account are present</source>
        <target>Warnung: Instanz wird gelöscht, sofern kein Service-Konto vorhanden ist</target>
      </segment>
    </unit>
    <unit id="onVerified.text">
      <notes>
        <note category="description">EditCloudInstance-button text for update</note>
      </notes>
      <segment state="initial">
        <source>Update</source>
        <target>Aktualisieren</target>
      </segment>
    </unit>
    <unit id="1306823280448276106">
      <notes>
        <note category="description">Manage Cloud Keys: label for Cloud Instance</note>
      </notes>
      <segment state="initial">
        <source> Cloud Instance: </source>
        <target> Cloud-Instanz: </target>
      </segment>
    </unit>
    <unit id="4630944461097377180">
      <notes>
        <note category="description">Manage Cloud Keys: button text for Create/Upload key</note>
      </notes>
      <segment state="initial">
        <source>Create/Upload Key</source>
        <target>Schlüssel erstellen/hochladen</target>
      </segment>
    </unit>
    <unit id="3243919419794524214">
      <notes>
        <note category="description">Manage Cloud Keys: button text for Create key</note>
      </notes>
      <segment state="initial">
        <source>Create Key</source>
        <target>Schlüssel erstellen</target>
      </segment>
    </unit>
    <unit id="5252961089687283406">
      <notes>
        <note category="description">Manage Cloud Keys: No keys available text</note>
      </notes>
      <segment state="initial">
        <source>No Keys Available</source>
        <target>Keine Schlüssel vorhanden</target>
      </segment>
    </unit>
    <unit id="8953033926734869941">
      <notes>
        <note category="description">Manage Cloud Keys-KeyDetails: label for Key Name</note>
      </notes>
      <segment state="initial">
        <source>Name</source>
        <target>Name</target>
      </segment>
    </unit>
    <unit id="4694447476013124795">
      <notes>
        <note category="description">Manage Cloud Keys: label for Key URI</note>
      </notes>
      <segment state="initial">
        <source>Key URI</source>
        <target>Schlüssel-URI</target>
      </segment>
    </unit>
    <unit id="7642895943748781278">
      <notes>
        <note category="description">Manage Cloud Keys: label for DKE Key URL</note>
      </notes>
      <segment state="initial">
        <source>DKE Key URL</source>
        <target>DKE Schlüssel-URL</target>
      </segment>
    </unit>
    <unit id="7650820226512704357">
      <notes>
        <note category="description">Manage Cloud Keys: label for Key Algorithm</note>
      </notes>
      <segment state="initial">
        <source>Key Algorithm</source>
        <target>Schlüsselagorithmus</target>
      </segment>
    </unit>
    <unit id="688560883283893567">
      <notes>
        <note category="description">Manage Cloud Keys: button text for Close</note>
      </notes>
      <segment state="initial">
        <source> Close</source>
        <target> Schließen</target>
      </segment>
    </unit>
    <unit id="delete_alert">
      <notes>
        <note category="description">Manage Cloud Key-Header for confirmation box</note>
      </notes>
      <segment state="initial">
        <source>Alert!</source>
        <target>Warnung!</target>
      </segment>
    </unit>
    <unit id="versionkey_alert">
      <notes>
        <note category="description">Manage Cloud Key-Header for confirmation box</note>
      </notes>
      <segment state="initial">
        <source>Alert!</source>
        <target>Warnung!</target>
      </segment>
    </unit>
    <unit id="select_label">
      <notes>
        <note category="description">Manage Cloud Keys-dropdown label for Select</note>
      </notes>
      <segment state="initial">
        <source>Select</source>
        <target>Wählen</target>
      </segment>
    </unit>
    <unit id="edit_label">
      <notes>
        <note category="description">Manage Cloud Keys-dropdown label for Edit</note>
      </notes>
      <segment state="initial">
        <source>Edit</source>
        <target>Bearbeiten</target>
      </segment>
    </unit>
    <unit id="upload_label">
      <notes>
        <note category="description">Manage Cloud Keys-dropdown label for Upload</note>
      </notes>
      <segment state="initial">
        <source>Upload</source>
        <target>Hochladen</target>
      </segment>
    </unit>
    <unit id="field.enabled">
      <notes>
        <note category="description">Enabled field</note>
      </notes>
      <segment state="initial">
        <source>Enabled</source>
        <target>Aktiviert</target>
      </segment>
    </unit>
    <unit id="field.keyVault">
      <notes>
        <note category="description">KeyVault field</note>
      </notes>
      <segment state="initial">
        <source>Key Vault</source>
        <target>Schlüsseltresor</target>
      </segment>
    </unit>
    <unit id="field.activationDate">
      <notes>
        <note category="description">Activatin Date field</note>
      </notes>
      <segment state="initial">
        <source>Activation Date</source>
        <target>Aktivierungsdatum</target>
      </segment>
    </unit>
    <unit id="field.expirationDate">
      <notes>
        <note category="description">Expiration Date field</note>
      </notes>
      <segment state="initial">
        <source>Expiration Date</source>
        <target>Ablaufdatum</target>
      </segment>
    </unit>
    <unit id="field.tags">
      <notes>
        <note category="description">Tags field</note>
      </notes>
      <segment state="initial">
        <source>Tags</source>
        <target>Tags</target>
      </segment>
    </unit>
    <unit id="editKeyPrefix">
      <notes>
        <note category="description">Manage Cloud Keys</note>
      </notes>
      <segment state="initial">
        <source>Edit key </source>
        <target>Schlüssel bearbeiten </target>
      </segment>
    </unit>
    <unit id="editKeySuffix">
      <notes>
        <note category="description">Manage Cloud Keys</note>
      </notes>
      <segment state="initial">
        <source> on </source>
        <target> in </target>
      </segment>
    </unit>
    <unit id="uploadKeyPrefix">
      <notes>
        <note category="description">Manage Cloud Keys</note>
      </notes>
      <segment state="initial">
        <source>Upload key </source>
        <target>Schlüssel hochladen </target>
      </segment>
    </unit>
    <unit id="uploadKeySuffix">
      <notes>
        <note category="description">Manage Cloud Keys</note>
      </notes>
      <segment state="initial">
        <source> to </source>
        <target> zu </target>
      </segment>
    </unit>
    <unit id="deleteActiveKeyConfirmation">
      <notes>
        <note category="meaning">Manage Cloud Keys-Confirmation message for key deletion</note>
      </notes>
      <segment state="initial">
        <source>Are you sure you want to delete key &lt;span class=&quot;no-break&quot;&gt;(<ph id="0" equiv="INTERPOLATION" disp="eskmKeyName"/>)&lt;/span&gt; from EKMaaS?</source>
        <target>Sind Sie sicher, dass Sie den Schlüssel (<ph id="0" equiv="INTERPOLATION" disp="eskmKeyName"/>) aus EKMaaS löschen wollen?</target>
      </segment>
    </unit>
    <unit id="deleteFromEKMaaS">
      <notes>
        <note category="meaning">Manage Cloud Keys-Message for delete from EKMaaS option</note>
      </notes>
      <segment state="initial">
        <source>Delete from EKMaaS</source>
        <target>Aus EKMaaS löschen</target>
      </segment>
    </unit>
    <unit id="deleteKeyConfirmation">
      <notes>
        <note category="meaning">Manage Cloud Keys-Confirmation message for key deletion</note>
      </notes>
      <segment state="initial">
        <source>Are you sure you want to delete key &lt;span class=&quot;no-break&quot;&gt;(<ph id="0" equiv="INTERPOLATION" disp="key.name"/>)&lt;/span&gt; from &lt;span class=&quot;no-break&quot;&gt;<ph id="1" equiv="INTERPOLATION_1" disp="this.selectedCloudInstance.instanceName"/> (<ph id="2" equiv="INTERPOLATION_2" disp="this.selectedCloudType.name"/>)&lt;/span&gt;?</source>
        <target>Sind Sie sicher, dass Sie den Schlüssel (<ph id="0" equiv="INTERPOLATION" disp="key.name"/>) aus <ph id="1" equiv="INTERPOLATION_1" disp="this.selectedCloudInstance.instanceName"/> (<ph id="2" equiv="INTERPOLATION_2" disp="this.selectedCloudType.name"/>) löschen wollen?</target>
      </segment>
    </unit>
    <unit id="keyAssociation.deleteNote">
      <notes>
        <note category="description">Manage Cloud Keys-Note on deleted keys</note>
      </notes>
      <segment state="initial">
        <source>Note: Key will be deleted only from <ph id="0" equiv="INTERPOLATION" disp="this.selectedCloudType.name"/></source>
        <target>Anmerkung: Der Schlüssel wird nur in <ph id="0" equiv="INTERPOLATION" disp="this.selectedCloudType.name"/> gelöscht</target>
      </segment>
    </unit>
    <unit id="deleteNotUploadedKeyConfirmation">
      <notes>
        <note category="meaning">Manage Cloud Keys-Confirmation message for key deletion</note>
      </notes>
      <segment state="initial">
        <source>Are you sure you want to delete key &lt;span class=&quot;no-break&quot;&gt;(<ph id="0" equiv="INTERPOLATION" disp="eskmKeyName"/>)&lt;/span&gt; from EKMaaS?</source>
        <target>Sind Sie sicher, dass Sie den Schlüssel (<ph id="0" equiv="INTERPOLATION" disp="eskmKeyName"/>) aus EKMaaS löschen wollen?</target>
      </segment>
    </unit>
    <unit id="deleteNoStatusKeyConfirmation">
      <notes>
        <note category="meaning">Manage Cloud Keys-Confirmation message for key deletion</note>
      </notes>
      <segment state="initial">
        <source>Are you sure you want to delete key &lt;span class=&quot;no-break&quot;&gt;(<ph id="0" equiv="INTERPOLATION" disp="!key.name &amp;&amp; this.selectedCloudType.name.toLowerCase().includes(&apos;salesforce-byok&apos;) ? &apos;Version - &apos; + key.keyAttributes[&apos;version&apos;] : key.name"/>)&lt;/span&gt; from &lt;span class=&quot;no-break&quot;&gt;<ph id="1" equiv="INTERPOLATION_1" disp="this.selectedCloudInstance.instanceName"/> (<ph id="2" equiv="INTERPOLATION_2" disp="this.selectedCloudType.name"/>)&lt;/span&gt;?</source>
        <target>Sind Sie sicher, dass Sie den Schlüssel (<ph id="0" equiv="INTERPOLATION" disp="!key.name &amp;&amp; this.selectedCloudType.name.toLowerCase().includes(&apos;salesforce-byok&apos;) ? &apos;Version - &apos;+key.keyAttributes[&apos;version&apos;] : key.name"/>) aus <ph id="1" equiv="INTERPOLATION_1" disp="this.selectedCloudInstance.instanceName"/> (<ph id="2" equiv="INTERPOLATION_2" disp="this.selectedCloudType.name"/>) löschen wollen?</target>
      </segment>
    </unit>
    <unit id="4359018163043204777">
      <segment state="initial">
        <source>Key Name</source>
        <target>Schlüsselname</target>
      </segment>
    </unit>
    <unit id="2702472467711841394">
      <notes>
        <note category="description">Reupload Key: label for Cloud Key Name</note>
      </notes>
      <segment state="initial">
        <source>*Cloud Key Name</source>
        <target>*Cloud Schlüsselname</target>
      </segment>
    </unit>
    <unit id="3013382452583924970">
      <notes>
        <note category="description">Reupload Key: label for EKMaas Key Name</note>
      </notes>
      <segment state="initial">
        <source>*EKMaaS Key Name</source>
        <target>*EKMaaS Schlüsselname</target>
      </segment>
    </unit>
    <unit id="121196129628862893">
      <notes>
        <note category="description">Stepper-View:button text for Upload</note>
      </notes>
      <segment state="initial">
        <source> Upload</source>
        <target> Hochladen</target>
      </segment>
    </unit>
    <unit id="3768927257183755959">
      <notes>
        <note category="description">AddKmipClient:button label for confirm</note>
      </notes>
      <segment state="initial">
        <source>Save</source>
        <target>Speichern</target>
      </segment>
    </unit>
    <unit id="alert_header">
      <notes>
        <note category="description">ReUpload Key-Header for confirmation box</note>
      </notes>
      <segment state="initial">
        <source>Alert!</source>
        <target>Warnung!</target>
      </segment>
    </unit>
    <unit id="confirmation_success_msg">
      <notes>
        <note category="description">ReUpload Key-Confirmation Header</note>
      </notes>
      <segment state="initial">
        <source>Success!</source>
        <target>Erfolg!</target>
      </segment>
    </unit>
    <unit id="version_confirmation">
      <notes>
        <note category="description">ReUpload Key-Header for confirmation box</note>
      </notes>
      <segment state="initial">
        <source>Alert!</source>
        <target>Warnung!</target>
      </segment>
    </unit>
    <unit id="confirmation_msg">
      <notes>
        <note category="description">ReUpload Key-Confirmation box content</note>
      </notes>
      <segment state="initial">
        <source>Are you sure you want to create a new version for </source>
        <target>Sind Sie sicher, dass sie eine neue Version anlegen wollen für </target>
      </segment>
    </unit>
    <unit id="3660013413509868257">
      <notes>
        <note category="description">Stepper-View: label for Select key</note>
      </notes>
      <segment state="initial">
        <source>Select Key</source>
        <target>Schlüssel wählen</target>
      </segment>
    </unit>
    <unit id="7000398097814012645">
      <notes>
        <note category="description">Stepper-View: label for key owner</note>
      </notes>
      <segment state="initial">
        <source>Key Owner</source>
        <target>Schlüsselbesitzer</target>
      </segment>
    </unit>
    <unit id="6377618342030058654">
      <notes>
        <note category="description">Stepper-View: label for key name</note>
      </notes>
      <segment state="initial">
        <source>*Key Name</source>
        <target>*Schlüsselname</target>
      </segment>
    </unit>
    <unit id="8988290600780497522">
      <notes>
        <note category="description">Stepper-View: label for algorithm</note>
      </notes>
      <segment state="initial">
        <source>*Algorithm</source>
        <target>*Algorithmus</target>
      </segment>
    </unit>
    <unit id="4207916966377787111">
      <segment state="initial">
        <source>Created</source>
        <target>Erstellen</target>
      </segment>
    </unit>
    <unit id="4824444844546691624">
      <segment state="initial">
        <source>Algorithm</source>
        <target>Algorithmus</target>
      </segment>
    </unit>
    <unit id="8514015417905907775">
      <notes>
        <note category="description">Stepper-View-FinalUpload: label for EKMaaS Key Name</note>
      </notes>
      <segment state="initial">
        <source>EKMaaS Key Name</source>
        <target>EKMaaS Schlüsselname</target>
      </segment>
    </unit>
    <unit id="key_selection_label">
      <notes>
        <note category="description">Stepper-View-Key Selection label for events in stepper view</note>
      </notes>
      <segment state="initial">
        <source>Key Selection</source>
        <target>Schlüsselauswahl</target>
      </segment>
    </unit>
    <unit id="summary_label">
      <notes>
        <note category="description">Stepper-View-Summary label for events in stepper view</note>
      </notes>
      <segment state="initial">
        <source>Summary</source>
        <target>Zusammenfassung</target>
      </segment>
    </unit>
    <unit id="upload_key_label">
      <notes>
        <note category="description">Stepper-View-Upload Key label for events in stepper view</note>
      </notes>
      <segment state="initial">
        <source>Upload Key</source>
        <target>Schlüssel hochladen</target>
      </segment>
    </unit>
    <unit id="create_upload_label">
      <notes>
        <note category="description">Stepper-View-Create key Name for radio button in stepper view</note>
      </notes>
      <segment state="initial">
        <source>Create Key &amp; Upload</source>
        <target>Schlüssel erstellen &amp; hochladen</target>
      </segment>
    </unit>
    <unit id="select_existing_label">
      <notes>
        <note category="description">Stepper-View-Existing Key Name for radio button in stepper view</note>
      </notes>
      <segment state="initial">
        <source>Select Existing Key</source>
        <target>Existierenden Schlüssel auswählen</target>
      </segment>
    </unit>
    <unit id="header">
      <notes>
        <note category="description">Stepper-View-Header for confirmation box</note>
      </notes>
      <segment state="initial">
        <source>Alert!</source>
        <target>Warnung!</target>
      </segment>
    </unit>
    <unit id="key_validation">
      <notes>
        <note category="description">Stepper-View-Msg for Validation Success</note>
      </notes>
      <segment state="initial">
        <source>Key created successfully on EKMaaS</source>
        <target>Schlüssel erfolgreich in EKMaaS erstellt</target>
      </segment>
    </unit>
    <unit id="Warning">
      <notes>
        <note category="description">Stepper-View-Search Key Warning Msg</note>
      </notes>
      <segment state="initial">
        <source>Warning: This key is already uploaded to the cloud instance</source>
        <target>Warnung: Dieser Schlüssel wurde bereits in die Cloud-Instanze hochgeladen</target>
      </segment>
    </unit>
    <unit id="success_header">
      <notes>
        <note category="description">Stepper-View-Confirmation Header</note>
      </notes>
      <segment state="initial">
        <source>Success!</source>
        <target>Erfolg!</target>
      </segment>
    </unit>
    <unit id="key_validation_warning">
      <notes>
        <note category="description">Stepper-View-Warning text</note>
      </notes>
      <segment state="initial">
        <source>Warning: </source>
        <target>Warnung: </target>
      </segment>
    </unit>
    <unit id="key_owned_text">
      <notes>
        <note category="description">Stepper-View-Key Form Validity Msg text</note>
      </notes>
      <segment state="initial">
        <source>Key is owned by </source>
        <target>Schlüssel ist in Besitz von </target>
      </segment>
    </unit>
    <unit id="instance_delete_warning">
      <notes>
        <note category="description">Stepper-View-Service Account Validation Msg</note>
      </notes>
      <segment state="initial">
        <source>Warning: Instance will be deleted if no service account are present</source>
        <target>Warnung: Instanz wird gelöscht, wenn kein Service-Konto existiert</target>
      </segment>
    </unit>
    <unit id="invalid_date">
      <notes>
        <note category="description">Stepper-View-Invalid Datetime validation Msg</note>
      </notes>
      <segment state="initial">
        <source>Invalid datetime value</source>
        <target>Ungültiger Datumswert</target>
      </segment>
    </unit>
    <unit id="4397712718833981257">
      <notes>
        <note category="description">Shared-Input: timezone text</note>
      </notes>
      <segment state="initial">
        <source><ph id="0" equiv="INTERPOLATION" disp="{{ getLocalizedDateTimeLabel(inputField) }}"/> Timezone</source>
        <target><ph id="0" equiv="INTERPOLATION" disp="{{getLocalizedDateTimeLabel(inputField)}}"/> Zeitzone</target>
      </segment>
    </unit>
    <unit id="placeholderText.keyVaults">
      <notes>
        <note category="description">SharedInput-AddCloudInstance</note>
      </notes>
      <segment state="initial">
        <source>Specify comma separated values for multiple key vault</source>
        <target>Werte für mehrere Schlüsseltresore mit Kommas trennen</target>
      </segment>
    </unit>
    <unit id="activationDate_timezone">
      <notes>
        <note category="description">SharedInput-ReUploadKey</note>
      </notes>
      <segment state="initial">
        <source>Activation Date</source>
        <target>Aktivierungsdatum</target>
      </segment>
    </unit>
    <unit id="expirationDate_timezone">
      <notes>
        <note category="description">SharedInput-ReUploadKey</note>
      </notes>
      <segment state="initial">
        <source>Expiration Date</source>
        <target>Verfallsdatum</target>
      </segment>
    </unit>
    <unit id="2545495424566277668">
      <notes>
        <note category="description">CloudListing: Button text for Edit Instance</note>
      </notes>
      <segment state="initial">
        <source> Edit Instance </source>
        <target> Instanz bearbeiten </target>
      </segment>
    </unit>
    <unit id="3822084371659290422">
      <segment state="initial">
        <source>Utimaco TaaS: Enterprise Key Manager</source>
        <target>Utimaco TaaS: Enterprise Key Manager</target>
      </segment>
    </unit>
    <unit id="6004681856910260407">
      <segment state="initial">
        <source>Creation Date</source>
        <target>Erstellungsdatum</target>
      </segment>
    </unit>
    <unit id="4089509911694721896">
      <segment state="initial">
        <source>Last Updated</source>
        <target>Zuletzt aktualisiert</target>
      </segment>
    </unit>
    <unit id="3014142150963657456">
      <segment state="initial">
        <source>Client</source>
        <target>Client</target>
      </segment>
    </unit>
    <unit id="4628738230038299640">
      <segment state="initial">
        <source>Assessment Date</source>
        <target>Assessment Date</target>
      </segment>
    </unit>
    <unit id="2535123683532102008">
      <segment state="initial">
        <source>Assessment Result</source>
        <target>Assessment Result</target>
      </segment>
    </unit>
    <unit id="5313216855975199451">
      <notes>
        <note category="description">Manage Cloud Keys: Warning text showing key deltion is irreversible</note>
      </notes>
      <segment state="initial">
        <source> ♦ Warning! This action will delete the key from </source>
        <target> ♦ Warnung! Dies wird den Schlüssel löschen von </target>
      </segment>
    </unit>
    <unit id="9155608366859514313">
      <segment state="initial">
        <source>Source</source>
        <target>Quelle</target>
      </segment>
    </unit>
    <unit id="5611592591303869712">
      <segment state="initial">
        <source>Status</source>
        <target>Status</target>
      </segment>
    </unit>
    <unit id="4816216590591222133">
      <segment state="initial">
        <source>Enabled</source>
        <target>Aktiviert</target>
      </segment>
    </unit>
    <unit id="5275254013832532154">
      <segment state="initial">
        <source>Key Vault</source>
        <target>Key Vault</target>
      </segment>
    </unit>
    <unit id="4179734860143712488">
      <notes>
        <note category="description">Stepper-View:button text for Create</note>
      </notes>
      <segment state="initial">
        <source> Create</source>
        <target> Erstellen</target>
      </segment>
    </unit>
    <unit id="6570363013146073520">
      <notes>
        <note category="description">main-menu home link</note>
      </notes>
      <segment state="initial">
        <source>Dashboard</source>
        <target>Dashboard</target>
      </segment>
    </unit>
    <unit id="6460207866734534390">
      <segment state="initial">
        <source>Cloud Clients</source>
        <target>Cloud Clients</target>
      </segment>
    </unit>
    <unit id="3499507106617527960">
      <segment state="initial">
        <source>Azure</source>
        <target>Azure</target>
      </segment>
    </unit>
    <unit id="6083409672770600534">
      <segment state="initial">
        <source>AWS</source>
        <target>AWS</target>
      </segment>
    </unit>
    <unit id="3590805807378700861">
      <segment state="initial">
        <source>Google</source>
        <target>Google</target>
      </segment>
    </unit>
    <unit id="6870744132322796712">
      <segment state="initial">
        <source>Enterprise Clients</source>
        <target>Enterprise Clients</target>
      </segment>
    </unit>
    <unit id="4221199921973829385">
      <segment state="initial">
        <source>KMIP</source>
        <target>KMIP</target>
      </segment>
    </unit>
    <unit id="8086244912297636472">
      <segment state="initial">
        <source>TDE</source>
        <target>TDE</target>
      </segment>
    </unit>
    <unit id="1356200968511280526">
      <segment state="initial">
        <source>REST</source>
        <target>REST</target>
      </segment>
    </unit>
    <unit id="8493363135660887954">
      <segment state="initial">
        <source>Access Management</source>
        <target>Zugriffsverwaltung</target>
      </segment>
    </unit>
    <unit id="4555457172864212828">
      <segment state="initial">
        <source>Users</source>
        <target>Nutzer</target>
      </segment>
    </unit>
    <unit id="5944812089887969249">
      <segment state="initial">
        <source>Groups</source>
        <target>Gruppen</target>
      </segment>
    </unit>
    <unit id="1678559204833619774">
      <segment state="initial">
        <source>Audit Log</source>
        <target>Audit Log</target>
      </segment>
    </unit>
    <unit id="6785659997897155974">
      <segment state="initial">
        <source>Setting</source>
        <target>Einstellungen</target>
      </segment>
    </unit>
    <unit id="5273501066845923426">
      <notes>
        <note category="description">AddkmipClient: label field for Client Id</note>
      </notes>
      <segment state="initial">
        <source>Client ID</source>
        <target>Client ID</target>
      </segment>
    </unit>
    <unit id="5295998403339413845">
      <notes>
        <note category="description">AddkmipClient: label field for Client Id Validation Error</note>
      </notes>
      <segment state="initial">
        <source> Client ID is required </source>
        <target> Client ID wird benötigt </target>
      </segment>
    </unit>
    <unit id="1431416938026210429">
      <notes>
        <note category="description">AddkmipClient: label field for Password</note>
      </notes>
      <segment state="initial">
        <source>Password</source>
        <target>Passwort</target>
      </segment>
    </unit>
    <unit id="2161902857876513332">
      <notes>
        <note category="description">AddkmipClient: label field for Password Validation Error</note>
      </notes>
      <segment state="initial">
        <source> Password is required and must be at least 12 characters </source>
        <target> Passwort wird benötigt und musst mindestens 12 Zeichen lang sein </target>
      </segment>
    </unit>
    <unit id="3241357959735682038">
      <notes>
        <note category="description">AddkmipClient: label field for Confirm Password</note>
      </notes>
      <segment state="initial">
        <source>Confirm Password</source>
        <target>Passwort bestätigen</target>
      </segment>
    </unit>
    <unit id="4865005494711354510">
      <notes>
        <note category="description">AddkmipClient: label field for Confirm Password Validation Error</note>
      </notes>
      <segment state="initial">
        <source> Please confirm your password </source>
        <target> Bitte bestätigen Sie Ihr Passwort </target>
      </segment>
    </unit>
    <unit id="4314910104023472121">
      <notes>
        <note category="description">AddkmipClient: label field for Password Mismatch Validation Error</note>
      </notes>
      <segment state="initial">
        <source> Passwords do not match </source>
        <target> Passwörter stimmen nicht überein </target>
      </segment>
    </unit>
    <unit id="9030336811208796738">
      <notes>
        <note category="description">AddkmipClient: label field for Client Certificate</note>
      </notes>
      <segment state="initial">
        <source>Client Certificate</source>
        <target>Client Zertifikat</target>
      </segment>
    </unit>
    <unit id="290160340331958183">
      <notes>
        <note category="description">AddkmipClient: label field for Client Certificate Validation Error</note>
      </notes>
      <segment state="initial">
        <source> Client Certificate is required </source>
        <target> Client Zertifikat wird benötigt </target>
      </segment>
    </unit>
    <unit id="3026976714330562202">
      <notes>
        <note category="description">AddkmipClient: label field for Client Certificate Note</note>
      </notes>
      <segment state="initial">
        <source> Note: Client Id and Common Name in the Client Certificate should match </source>
        <target> Hinweis: Client Id und Common Name im Client Zertifikat sollten übereinstimmen </target>
      </segment>
    </unit>
    <unit id="1200900687640843597">
      <segment state="initial">
        <source>Creation By</source>
        <target>Creation By</target>
      </segment>
    </unit>
    <unit id="165617360560009966">
      <segment state="initial">
        <source>Certificate Expiration Date</source>
        <target>Certificate Expiration Date</target>
      </segment>
    </unit>
    <unit id="2925129304308690882">
      <segment state="initial">
        <source>Last modified</source>
        <target>Zuletzt geändert</target>
      </segment>
    </unit>
    <unit id="5252837360733823238">
      <notes>
        <note category="description">ViewkmipClient: label field for Created By</note>
      </notes>
      <segment state="initial">
        <source>Created By</source>
        <target>Created By</target>
      </segment>
    </unit>
    <unit id="8820965746779312111">
      <notes>
        <note category="description">ViewkmipClient: label field for Last Modified</note>
      </notes>
      <segment state="initial">
        <source>Last Modified</source>
        <target>Zuletzt geändert</target>
      </segment>
    </unit>
    <unit id="5522236499655154147">
      <notes>
        <note category="description">ViewkmipClient: label field for Certificate Start Date</note>
      </notes>
      <segment state="initial">
        <source>Certificate Start Date</source>
        <target>Zertifikat Anfangszeitpunkt</target>
      </segment>
    </unit>
    <unit id="5491349027224642844">
      <notes>
        <note category="description">UpdatekmipClient: label field for New Certificate Entry</note>
      </notes>
      <segment state="initial">
        <source>New Certificate</source>
        <target>Neues Zertifikat</target>
      </segment>
    </unit>
    <unit id="1221596867309460035">
      <notes>
        <note category="description">UpdatekmipClient: label field for New Password Entry</note>
      </notes>
      <segment state="initial">
        <source>New Password</source>
        <target>Neues Passwort</target>
      </segment>
    </unit>
    <unit id="9132547751630599796">
      <notes>
        <note category="description">UpdatekmipClient: Error message for Password Validation</note>
      </notes>
      <segment state="initial">
        <source> Password is required </source>
        <target> Passwort wird benötigt </target>
      </segment>
    </unit>
    <unit id="7731102146667330348">
      <notes>
        <note category="description">UpdatekmipClient: Error message for Password Validation</note>
      </notes>
      <segment state="initial">
        <source> Password must be at least 8 characters long </source>
        <target> Passwort muss mindestens 8 Zeichen lang sein </target>
      </segment>
    </unit>
    <unit id="1485410082721015205">
      <notes>
        <note category="description">UpdatekmipClient: Error message for Confirm Password Validation</note>
      </notes>
      <segment state="initial">
        <source> Confirm Password is required </source>
        <target> Sie müssen das Passwort bestätigen </target>
      </segment>
    </unit>
    <unit id="6164699983643908587">
      <segment state="initial">
        <source>Object Name</source>
        <target>Object Name</target>
      </segment>
    </unit>
    <unit id="7716208024960184784">
      <segment state="initial">
        <source>UUID</source>
        <target>UUID</target>
      </segment>
    </unit>
    <unit id="682467974069032141">
      <segment state="initial">
        <source>Change Client Certificate</source>
        <target>Client Zertifikat ändern</target>
      </segment>
    </unit>
    <unit id="6582573664846559511">
      <segment state="initial">
        <source>Change Password</source>
        <target>Passort ändern</target>
      </segment>
    </unit>
    <unit id="7754924993990855042">
      <notes>
        <note category="description">AddCloudInstance: option field Select</note>
      </notes>
      <segment state="initial">
        <source> Select </source>
        <target> Wählen </target>
      </segment>
    </unit>
    <unit id="8323422358213440128">
      <notes>
        <note category="description">AddCloudInstance: Cancel</note>
      </notes>
      <segment state="initial">
        <source>
          <ph id="0" equiv="INTERPOLATION" disp="{{ cancelLocalized }}"/>
        </source>
        <target>
          <ph id="0" equiv="INTERPOLATION" disp="{{ cancelLocalized }}"/>
        </target>
      </segment>
    </unit>
    <unit id="5668632685575118472">
      <notes>
        <note category="description">AddCloudInstance:Cancel</note>
      </notes>
      <segment state="initial">
        <source> Cancel</source>
        <target> Abbrechen</target>
      </segment>
    </unit>
    <unit id="4350423050822384120">
      <notes>
        <note category="description">AddCloudInstance:title</note>
      </notes>
      <segment state="initial">
        <source> Add Cloud Instance</source>
        <target> Cloud Instanz hinzufügen</target>
      </segment>
    </unit>
    <unit id="9101591947723213375">
      <notes>
        <note category="description">EditCloudInstance:title</note>
      </notes>
      <segment state="initial">
        <source> Edit Cloud Instance</source>
        <target> Cloud Instanz bearbeiten</target>
      </segment>
    </unit>
    <unit id="8828229858764392252">
      <notes>
        <note category="description">DeleteCloudInstance:title</note>
      </notes>
      <segment state="initial">
        <source> Delete Cloud Instance</source>
        <target> Cloud Instanz löschen</target>
      </segment>
    </unit>
    <unit id="1593087966609132148">
      <notes>
        <note category="description">Upload Key:title</note>
      </notes>
      <segment state="initial">
        <source> Upload to </source>
        <target> Hochladen zu </target>
      </segment>
    </unit>
    <unit id="4406493224727225230">
      <notes>
        <note category="description">Stepper-View:button text for Next</note>
      </notes>
      <segment state="initial">
        <source> Next</source>
        <target> Nächster Schritt</target>
      </segment>
    </unit>
    <unit id="5893739794080693805">
      <notes>
        <note category="description">DeleteCloudInstance:delete message</note>
      </notes>
      <segment state="initial">
        <source> Are you sure you want to delete instance </source>
        <target> Bestätigen Sie das Löschen der Instanz </target>
      </segment>
    </unit>
    <unit id="1838419884521058275">
      <notes>
        <note category="description">AddKmipClient: Button text for Add Kmip Client</note>
      </notes>
      <segment state="initial">
        <source>Add KMIP Client</source>
        <target>Add KMIP Client</target>
      </segment>
    </unit>
    <unit id="6132406321239737282">
      <notes>
        <note category="description">KmipClientListing: Button text for Download Kmip Client Certificate</note>
      </notes>
      <segment state="initial">
        <source>Download Client Cert</source>
        <target>Download Client Cert</target>
      </segment>
    </unit>
  </file>
</xliff>
