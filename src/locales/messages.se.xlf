<xliff version="2.0" xmlns="urn:oasis:names:tc:xliff:document:2.0" srcLang="en" trgLang="se">
  <file id="ngi18n" original="ng.template">
    <unit id="4827075895754858675">
      <segment state="initial">
        <source>Error while trying to fetch supported cloud types</source>
        <target>Error while trying to fetch supported cloud types</target>
      </segment>
    </unit>
    <unit id="1076527898293452775">
      <segment state="initial">
        <source>Error while trying to fetch cloud instances</source>
        <target>Error while trying to fetch cloud instances</target>
      </segment>
    </unit>
    <unit id="802560322927792951">
      <segment state="initial">
        <source>Error while trying to delete Cloud Instance</source>
        <target>Error while trying to delete Cloud Instance</target>
      </segment>
    </unit>
    <unit id="939617489946732670">
      <notes>
        <note category="description">AddCloudInstance: label field Instance Name</note>
      </notes>
      <segment state="initial">
        <source>* Instance Name</source>
        <target>* Instance Name</target>
      </segment>
    </unit>
    <unit id="6141915193625957942">
      <notes>
        <note category="description">AddCloudInstance: label field Cloud Type</note>
      </notes>
      <segment state="initial">
        <source>* Cloud Type</source>
        <target>* Cloud Type</target>
      </segment>
    </unit>
    <unit id="field_required">
      <notes>
        <note category="description">Field Required Validation Msg</note>
      </notes>
      <segment state="initial">
        <source>Field Required</source>
        <target>Field Required</target>
      </segment>
    </unit>
    <unit id="validation_msg">
      <notes>
        <note category="description">Msg for Validation Check</note>
      </notes>
      <segment state="initial">
        <source>Value should be less than </source>
        <target>Value should be less than </target>
      </segment>
    </unit>
    <unit id="character">
      <notes>
        <note category="description">Value Comparison text Msg</note>
      </notes>
      <segment state="initial">
        <source> characters</source>
        <target> characters</target>
      </segment>
    </unit>
    <unit id="uniqueness_validation">
      <notes>
        <note category="description">Validate multi-select or value-array input</note>
      </notes>
      <segment state="initial">
        <source> Field values must be unique</source>
        <target> Field values must be unique</target>
      </segment>
    </unit>
    <unit id="field.tenantId">
      <notes>
        <note category="description">Tenant ID label</note>
      </notes>
      <segment state="initial">
        <source>Tenant ID</source>
        <target>Tenant ID</target>
      </segment>
    </unit>
    <unit id="field.clientId">
      <notes>
        <note category="description">Client ID label</note>
      </notes>
      <segment state="initial">
        <source>Client ID</source>
        <target>Client ID</target>
      </segment>
    </unit>
    <unit id="field.clientSecret">
      <notes>
        <note category="description">Client Secret label</note>
      </notes>
      <segment state="initial">
        <source>Client Secret</source>
        <target>Client Secret</target>
      </segment>
    </unit>
    <unit id="field.keyVaults">
      <notes>
        <note category="description">Key Vault label</note>
      </notes>
      <segment state="initial">
        <source>Key Vaults</source>
        <target>Key Vaults</target>
      </segment>
    </unit>
    <unit id="addBtnText.add">
      <notes>
        <note category="description">AddCloudInstance-Add</note>
      </notes>
      <segment state="initial">
        <source>Add</source>
        <target>Add</target>
      </segment>
    </unit>
    <unit id="addBtnText.verify">
      <notes>
        <note category="description">AddCloudInstance-Verify</note>
      </notes>
      <segment state="initial">
        <source>Verify</source>
        <target>Verify</target>
      </segment>
    </unit>
    <unit id="validate_instance">
      <notes>
        <note category="description">AddCloudInstance-New Cloud Instance Validation Msg</note>
      </notes>
      <segment state="initial">
        <source>Field can only contain letters, numbers, hyphens, underscores, and periods</source>
        <target>Field can only contain letters, numbers, hyphens, underscores, and periods</target>
      </segment>
    </unit>
    <unit id="9216117865911519658">
      <segment state="initial">
        <source>Action</source>
        <target>Action</target>
      </segment>
    </unit>
    <unit id="1924075649164405038">
      <notes>
        <note category="description">CloudListing: Button text for Manage Keys</note>
      </notes>
      <segment state="initial">
        <source> Manage Keys </source>
        <target> Manage Keys </target>
      </segment>
    </unit>
    <unit id="8111132081545684874">
      <notes>
        <note category="description">CloudListing: Button text for Delete Instance</note>
      </notes>
      <segment state="initial">
        <source> Delete Instance </source>
        <target> Delete Instance </target>
      </segment>
    </unit>
    <unit id="4154974451532903749">
      <notes>
        <note category="description">CloudListing: Text for No Cloud Instances</note>
      </notes>
      <segment state="initial">
        <source> No Cloud Instances Available </source>
        <target> No Cloud Instances Available </target>
      </segment>
    </unit>
    <unit id="7022070615528435141">
      <notes>
        <note category="description">KmipClientListing: Button text for Delete Kmip Client</note>
      </notes>
      <segment state="initial">
        <source>Delete</source>
        <target>Delete</target>
      </segment>
    </unit>
    <unit id="6905322870062458300">
      <segment state="initial">
        <source>Instance Name</source>
        <target>Instance Name</target>
      </segment>
    </unit>
    <unit id="7267599172607716807">
      <segment state="initial">
        <source>Cloud Type</source>
        <target>Cloud Type</target>
      </segment>
    </unit>
    <unit id="2375260419993138758">
      <notes>
        <note category="description">Edit Cloud Instance: label field for Key URI</note>
      </notes>
      <segment state="initial">
        <source>URL</source>
        <target>URL</target>
      </segment>
    </unit>
    <unit id="4134736808256671237">
      <notes>
        <note category="description">Edit Cloud Instance: text for not editable Instance</note>
      </notes>
      <segment state="initial">
        <source> Note: This instance is not editable. </source>
        <target> Note: This instance is not editable. </target>
      </segment>
    </unit>
    <unit id="editCloudInstanceButton.text">
      <notes>
        <note category="description">EditCloudInstance-button text</note>
      </notes>
      <segment state="initial">
        <source>Verify</source>
        <target>Verify</target>
      </segment>
    </unit>
    <unit id="delete_instance_warn">
      <notes>
        <note category="description">EditCloudInstance-delete instance warn</note>
      </notes>
      <segment state="initial">
        <source>Warning: Instance will be deleted</source>
        <target>Warning: Instance will be deleted</target>
      </segment>
    </unit>
    <unit id="updateCloudInstanceButton.text">
      <notes>
        <note category="description">EditCloudInstance-button text for update</note>
      </notes>
      <segment state="initial">
        <source>Update</source>
        <target>Update</target>
      </segment>
    </unit>
    <unit id="warn_update_value_array">
      <notes>
        <note category="description">EditCloudInstance-warning on update inputs</note>
      </notes>
      <segment state="initial">
        <source>Warning: Instance will be deleted if no service account are present</source>
        <target>Warning: Instance will be deleted if no service account are present</target>
      </segment>
    </unit>
    <unit id="onVerified.text">
      <notes>
        <note category="description">EditCloudInstance-button text for update</note>
      </notes>
      <segment state="initial">
        <source>Update</source>
        <target>Update</target>
      </segment>
    </unit>
    <unit id="1306823280448276106">
      <notes>
        <note category="description">Manage Cloud Keys: label for Cloud Instance</note>
      </notes>
      <segment state="initial">
        <source> Cloud Instance: </source>
        <target> Cloud Instance: </target>
      </segment>
    </unit>
    <unit id="4630944461097377180">
      <notes>
        <note category="description">Manage Cloud Keys: button text for Create/Upload key</note>
      </notes>
      <segment state="initial">
        <source>Create/Upload Key</source>
        <target>Create/Upload Key</target>
      </segment>
    </unit>
    <unit id="3243919419794524214">
      <notes>
        <note category="description">Manage Cloud Keys: button text for Create key</note>
      </notes>
      <segment state="initial">
        <source>Create Key</source>
        <target>Create Key</target>
      </segment>
    </unit>
    <unit id="5252961089687283406">
      <notes>
        <note category="description">Manage Cloud Keys: No keys available text</note>
      </notes>
      <segment state="initial">
        <source>No Keys Available</source>
        <target>No Keys Available</target>
      </segment>
    </unit>
    <unit id="8953033926734869941">
      <notes>
        <note category="description">Manage Cloud Keys-KeyDetails: label for Key Name</note>
      </notes>
      <segment state="initial">
        <source>Name</source>
        <target>Name</target>
      </segment>
    </unit>
    <unit id="4694447476013124795">
      <notes>
        <note category="description">Manage Cloud Keys: label for Key URI</note>
      </notes>
      <segment state="initial">
        <source>Key URI</source>
        <target>Key URI</target>
      </segment>
    </unit>
    <unit id="7642895943748781278">
      <notes>
        <note category="description">Manage Cloud Keys: label for DKE Key URL</note>
      </notes>
      <segment state="initial">
        <source>DKE Key URL</source>
        <target>DKE Key URL</target>
      </segment>
    </unit>
    <unit id="7650820226512704357">
      <notes>
        <note category="description">Manage Cloud Keys: label for Key Algorithm</note>
      </notes>
      <segment state="initial">
        <source>Key Algorithm</source>
        <target>Key Algorithm</target>
      </segment>
    </unit>
    <unit id="688560883283893567">
      <notes>
        <note category="description">Manage Cloud Keys: button text for Close</note>
      </notes>
      <segment state="initial">
        <source> Close</source>
        <target> Close</target>
      </segment>
    </unit>
    <unit id="delete_alert">
      <notes>
        <note category="description">Manage Cloud Key-Header for confirmation box</note>
      </notes>
      <segment state="initial">
        <source>Alert!</source>
        <target>Alert!</target>
      </segment>
    </unit>
    <unit id="versionkey_alert">
      <notes>
        <note category="description">Manage Cloud Key-Header for confirmation box</note>
      </notes>
      <segment state="initial">
        <source>Alert!</source>
        <target>Alert!</target>
      </segment>
    </unit>
    <unit id="select_label">
      <notes>
        <note category="description">Manage Cloud Keys-dropdown label for Select</note>
      </notes>
      <segment state="initial">
        <source>Select</source>
        <target>Select</target>
      </segment>
    </unit>
    <unit id="edit_label">
      <notes>
        <note category="description">Manage Cloud Keys-dropdown label for Edit</note>
      </notes>
      <segment state="initial">
        <source>Edit</source>
        <target>Edit</target>
      </segment>
    </unit>
    <unit id="upload_label">
      <notes>
        <note category="description">Manage Cloud Keys-dropdown label for Upload</note>
      </notes>
      <segment state="initial">
        <source>Upload</source>
        <target>Upload</target>
      </segment>
    </unit>
    <unit id="field.enabled">
      <notes>
        <note category="description">Enabled field</note>
      </notes>
      <segment state="initial">
        <source>Enabled</source>
        <target>Enabled</target>
      </segment>
    </unit>
    <unit id="field.keyVault">
      <notes>
        <note category="description">KeyVault field</note>
      </notes>
      <segment state="initial">
        <source>Key Vault</source>
        <target>Key Vault</target>
      </segment>
    </unit>
    <unit id="field.activationDate">
      <notes>
        <note category="description">Activatin Date field</note>
      </notes>
      <segment state="initial">
        <source>Activation Date</source>
        <target>Activation Date</target>
      </segment>
    </unit>
    <unit id="field.expirationDate">
      <notes>
        <note category="description">Expiration Date field</note>
      </notes>
      <segment state="initial">
        <source>Expiration Date</source>
        <target>Expiration Date</target>
      </segment>
    </unit>
    <unit id="field.tags">
      <notes>
        <note category="description">Tags field</note>
      </notes>
      <segment state="initial">
        <source>Tags</source>
        <target>Tags</target>
      </segment>
    </unit>
    <unit id="editKeyPrefix">
      <notes>
        <note category="description">Manage Cloud Keys</note>
      </notes>
      <segment state="initial">
        <source>Edit key </source>
        <target>Edit key </target>
      </segment>
    </unit>
    <unit id="editKeySuffix">
      <notes>
        <note category="description">Manage Cloud Keys</note>
      </notes>
      <segment state="initial">
        <source> on </source>
        <target> on </target>
      </segment>
    </unit>
    <unit id="uploadKeyPrefix">
      <notes>
        <note category="description">Manage Cloud Keys</note>
      </notes>
      <segment state="initial">
        <source>Upload key </source>
        <target>Upload key </target>
      </segment>
    </unit>
    <unit id="uploadKeySuffix">
      <notes>
        <note category="description">Manage Cloud Keys</note>
      </notes>
      <segment state="initial">
        <source> to </source>
        <target> to </target>
      </segment>
    </unit>
    <unit id="deleteActiveKeyConfirmation">
      <notes>
        <note category="meaning">Manage Cloud Keys-Confirmation message for key deletion</note>
      </notes>
      <segment state="initial">
        <source>Are you sure you want to delete key &lt;span class=&quot;no-break&quot;&gt;(<ph id="0" equiv="INTERPOLATION" disp="eskmKeyName"/>)&lt;/span&gt; from EKMaaS?</source>
        <target>Are you sure you want to delete key &lt;span class=&quot;no-break&quot;&gt;(<ph id="0" equiv="INTERPOLATION" disp="eskmKeyName"/>)&lt;/span&gt; from EKMaaS?</target>
      </segment>
    </unit>
    <unit id="deleteFromEKMaaS">
      <notes>
        <note category="meaning">Manage Cloud Keys-Message for delete from EKMaaS option</note>
      </notes>
      <segment state="initial">
        <source>Delete from EKMaaS</source>
        <target>Delete from EKMaaS</target>
      </segment>
    </unit>
    <unit id="deleteKeyConfirmation">
      <notes>
        <note category="meaning">Manage Cloud Keys-Confirmation message for key deletion</note>
      </notes>
      <segment state="initial">
        <source>Are you sure you want to delete key &lt;span class=&quot;no-break&quot;&gt;(<ph id="0" equiv="INTERPOLATION" disp="key.name"/>)&lt;/span&gt; from &lt;span class=&quot;no-break&quot;&gt;<ph id="1" equiv="INTERPOLATION_1" disp="this.selectedCloudInstance.instanceName"/> (<ph id="2" equiv="INTERPOLATION_2" disp="this.selectedCloudType.name"/>)&lt;/span&gt;?</source>
        <target>Are you sure you want to delete key &lt;span class=&quot;no-break&quot;&gt;(<ph id="0" equiv="INTERPOLATION" disp="key.name"/>)&lt;/span&gt; from &lt;span class=&quot;no-break&quot;&gt;<ph id="1" equiv="INTERPOLATION_1" disp="this.selectedCloudInstance.instanceName"/> (<ph id="2" equiv="INTERPOLATION_2" disp="this.selectedCloudType.name"/>)&lt;/span&gt;?</target>
      </segment>
    </unit>
    <unit id="keyAssociation.deleteNote">
      <notes>
        <note category="description">Manage Cloud Keys-Note on deleted keys</note>
      </notes>
      <segment state="initial">
        <source>Note: Key will be deleted only from <ph id="0" equiv="INTERPOLATION" disp="this.selectedCloudType.name"/></source>
        <target>Note: Key will be deleted only from <ph id="0" equiv="INTERPOLATION" disp="this.selectedCloudType.name"/></target>
      </segment>
    </unit>
    <unit id="deleteNotUploadedKeyConfirmation">
      <notes>
        <note category="meaning">Manage Cloud Keys-Confirmation message for key deletion</note>
      </notes>
      <segment state="initial">
        <source>Are you sure you want to delete key &lt;span class=&quot;no-break&quot;&gt;(<ph id="0" equiv="INTERPOLATION" disp="eskmKeyName"/>)&lt;/span&gt; from EKMaaS?</source>
        <target>Are you sure you want to delete key &lt;span class=&quot;no-break&quot;&gt;(<ph id="0" equiv="INTERPOLATION" disp="eskmKeyName"/>)&lt;/span&gt; from EKMaaS?</target>
      </segment>
    </unit>
    <unit id="deleteNoStatusKeyConfirmation">
      <notes>
        <note category="meaning">Manage Cloud Keys-Confirmation message for key deletion</note>
      </notes>
      <segment state="initial">
        <source>Are you sure you want to delete key &lt;span class=&quot;no-break&quot;&gt;(<ph id="0" equiv="INTERPOLATION" disp="!key.name &amp;&amp; this.selectedCloudType.name.toLowerCase().includes(&apos;salesforce-byok&apos;) ? &apos;Version - &apos; + key.keyAttributes[&apos;version&apos;] : key.name"/>)&lt;/span&gt; from &lt;span class=&quot;no-break&quot;&gt;<ph id="1" equiv="INTERPOLATION_1" disp="this.selectedCloudInstance.instanceName"/> (<ph id="2" equiv="INTERPOLATION_2" disp="this.selectedCloudType.name"/>)&lt;/span&gt;?</source>
        <target>Are you sure you want to delete key &lt;span class=&quot;no-break&quot;&gt;(<ph id="0" equiv="INTERPOLATION" disp="!key.name &amp;&amp; this.selectedCloudType.name.toLowerCase().includes(&apos;salesforce-byok&apos;) ? &apos;Version - &apos; + key.keyAttributes[&apos;version&apos;] : key.name"/>)&lt;/span&gt; from &lt;span class=&quot;no-break&quot;&gt;<ph id="1" equiv="INTERPOLATION_1" disp="this.selectedCloudInstance.instanceName"/> (<ph id="2" equiv="INTERPOLATION_2" disp="this.selectedCloudType.name"/>)&lt;/span&gt;?</target>
      </segment>
    </unit>
    <unit id="4359018163043204777">
      <segment state="initial">
        <source>Key Name</source>
        <target>Key Name</target>
      </segment>
    </unit>
    <unit id="2702472467711841394">
      <notes>
        <note category="description">Reupload Key: label for Cloud Key Name</note>
      </notes>
      <segment state="initial">
        <source>*Cloud Key Name</source>
        <target>*Cloud Key Name</target>
      </segment>
    </unit>
    <unit id="3013382452583924970">
      <notes>
        <note category="description">Reupload Key: label for EKMaas Key Name</note>
      </notes>
      <segment state="initial">
        <source>*EKMaaS Key Name</source>
        <target>*EKMaaS Key Name</target>
      </segment>
    </unit>
    <unit id="121196129628862893">
      <notes>
        <note category="description">Stepper-View:button text for Upload</note>
      </notes>
      <segment state="initial">
        <source> Upload</source>
        <target> Upload</target>
      </segment>
    </unit>
    <unit id="3768927257183755959">
      <notes>
        <note category="description">AddKmipClient:button label for confirm</note>
      </notes>
      <segment state="initial">
        <source>Save</source>
        <target>Save</target>
      </segment>
    </unit>
    <unit id="alert_header">
      <notes>
        <note category="description">ReUpload Key-Header for confirmation box</note>
      </notes>
      <segment state="initial">
        <source>Alert!</source>
        <target>Alert!</target>
      </segment>
    </unit>
    <unit id="confirmation_success_msg">
      <notes>
        <note category="description">ReUpload Key-Confirmation Header</note>
      </notes>
      <segment state="initial">
        <source>Success!</source>
        <target>Success!</target>
      </segment>
    </unit>
    <unit id="version_confirmation">
      <notes>
        <note category="description">ReUpload Key-Header for confirmation box</note>
      </notes>
      <segment state="initial">
        <source>Alert!</source>
        <target>Alert!</target>
      </segment>
    </unit>
    <unit id="confirmation_msg">
      <notes>
        <note category="description">ReUpload Key-Confirmation box content</note>
      </notes>
      <segment state="initial">
        <source>Are you sure you want to create a new version for </source>
        <target>Are you sure you want to create a new version for </target>
      </segment>
    </unit>
    <unit id="3660013413509868257">
      <notes>
        <note category="description">Stepper-View: label for Select key</note>
      </notes>
      <segment state="initial">
        <source>Select Key</source>
        <target>Select Key</target>
      </segment>
    </unit>
    <unit id="7000398097814012645">
      <notes>
        <note category="description">Stepper-View: label for key owner</note>
      </notes>
      <segment state="initial">
        <source>Key Owner</source>
        <target>Key Owner</target>
      </segment>
    </unit>
    <unit id="6377618342030058654">
      <notes>
        <note category="description">Stepper-View: label for key name</note>
      </notes>
      <segment state="initial">
        <source>*Key Name</source>
        <target>*Key Name</target>
      </segment>
    </unit>
    <unit id="8988290600780497522">
      <notes>
        <note category="description">Stepper-View: label for algorithm</note>
      </notes>
      <segment state="initial">
        <source>*Algorithm</source>
        <target>*Algorithm</target>
      </segment>
    </unit>
    <unit id="4207916966377787111">
      <segment state="initial">
        <source>Created</source>
        <target>Created</target>
      </segment>
    </unit>
    <unit id="4824444844546691624">
      <segment state="initial">
        <source>Algorithm</source>
        <target>Algorithm</target>
      </segment>
    </unit>
    <unit id="8514015417905907775">
      <notes>
        <note category="description">Stepper-View-FinalUpload: label for EKMaaS Key Name</note>
      </notes>
      <segment state="initial">
        <source>EKMaaS Key Name</source>
        <target>EKMaaS Key Name</target>
      </segment>
    </unit>
    <unit id="key_selection_label">
      <notes>
        <note category="description">Stepper-View-Key Selection label for events in stepper view</note>
      </notes>
      <segment state="initial">
        <source>Key Selection</source>
        <target>Key Selection</target>
      </segment>
    </unit>
    <unit id="summary_label">
      <notes>
        <note category="description">Stepper-View-Summary label for events in stepper view</note>
      </notes>
      <segment state="initial">
        <source>Summary</source>
        <target>Summary</target>
      </segment>
    </unit>
    <unit id="upload_key_label">
      <notes>
        <note category="description">Stepper-View-Upload Key label for events in stepper view</note>
      </notes>
      <segment state="initial">
        <source>Upload Key</source>
        <target>Upload Key</target>
      </segment>
    </unit>
    <unit id="create_upload_label">
      <notes>
        <note category="description">Stepper-View-Create key Name for radio button in stepper view</note>
      </notes>
      <segment state="initial">
        <source>Create Key &amp; Upload</source>
        <target>Create Key &amp; Upload</target>
      </segment>
    </unit>
    <unit id="select_existing_label">
      <notes>
        <note category="description">Stepper-View-Existing Key Name for radio button in stepper view</note>
      </notes>
      <segment state="initial">
        <source>Select Existing Key</source>
        <target>Select Existing Key</target>
      </segment>
    </unit>
    <unit id="header">
      <notes>
        <note category="description">Stepper-View-Header for confirmation box</note>
      </notes>
      <segment state="initial">
        <source>Alert!</source>
        <target>Alert!</target>
      </segment>
    </unit>
    <unit id="key_validation">
      <notes>
        <note category="description">Stepper-View-Msg for Validation Success</note>
      </notes>
      <segment state="initial">
        <source>Key created successfully on EKMaaS</source>
        <target>Key created successfully on EKMaaS</target>
      </segment>
    </unit>
    <unit id="Warning">
      <notes>
        <note category="description">Stepper-View-Search Key Warning Msg</note>
      </notes>
      <segment state="initial">
        <source>Warning: This key is already uploaded to the cloud instance</source>
        <target>Warning: This key is already uploaded to the cloud instance</target>
      </segment>
    </unit>
    <unit id="success_header">
      <notes>
        <note category="description">Stepper-View-Confirmation Header</note>
      </notes>
      <segment state="initial">
        <source>Success!</source>
        <target>Success!</target>
      </segment>
    </unit>
    <unit id="key_validation_warning">
      <notes>
        <note category="description">Stepper-View-Warning text</note>
      </notes>
      <segment state="initial">
        <source>Warning: </source>
        <target>Warning: </target>
      </segment>
    </unit>
    <unit id="key_owned_text">
      <notes>
        <note category="description">Stepper-View-Key Form Validity Msg text</note>
      </notes>
      <segment state="initial">
        <source>Key is owned by </source>
        <target>Key is owned by </target>
      </segment>
    </unit>
    <unit id="instance_delete_warning">
      <notes>
        <note category="description">Stepper-View-Service Account Validation Msg</note>
      </notes>
      <segment state="initial">
        <source>Warning: Instance will be deleted if no service account are present</source>
        <target>Warning: Instance will be deleted if no service account are present</target>
      </segment>
    </unit>
    <unit id="invalid_date">
      <notes>
        <note category="description">Stepper-View-Invalid Datetime validation Msg</note>
      </notes>
      <segment state="initial">
        <source>Invalid datetime value</source>
        <target>Invalid datetime value</target>
      </segment>
    </unit>
    <unit id="4397712718833981257">
      <notes>
        <note category="description">Shared-Input: timezone text</note>
      </notes>
      <segment state="initial">
        <source><ph id="0" equiv="INTERPOLATION" disp="{{ getLocalizedDateTimeLabel(inputField) }}"/> Timezone</source>
        <target><ph id="0" equiv="INTERPOLATION" disp="{{ getLocalizedDateTimeLabel(inputField) }}"/> Timezone</target>
      </segment>
    </unit>
    <unit id="placeholderText.keyVaults">
      <notes>
        <note category="description">SharedInput-AddCloudInstance</note>
      </notes>
      <segment state="initial">
        <source>Specify comma separated values for multiple key vault</source>
        <target>Specify comma separated values for multiple key vault</target>
      </segment>
    </unit>
    <unit id="activationDate_timezone">
      <notes>
        <note category="description">SharedInput-ReUploadKey</note>
      </notes>
      <segment state="initial">
        <source>Activation Date</source>
        <target>Activation Date</target>
      </segment>
    </unit>
    <unit id="expirationDate_timezone">
      <notes>
        <note category="description">SharedInput-ReUploadKey</note>
      </notes>
      <segment state="initial">
        <source>Expiration Date</source>
        <target>Expiration Date</target>
      </segment>
    </unit>
    <unit id="2545495424566277668">
      <notes>
        <note category="description">CloudListing: Button text for Edit Instance</note>
      </notes>
      <segment state="initial">
        <source> Edit Instance </source>
        <target> Edit Instance </target>
      </segment>
    </unit>
    <unit id="3822084371659290422">
      <segment state="initial">
        <source>Utimaco TaaS: Enterprise Key Manager</source>
        <target>Utimaco TaaS: Enterprise Key Manager</target>
      </segment>
    </unit>
    <unit id="6004681856910260407">
      <segment state="initial">
        <source>Creation Date</source>
        <target>Creation Date</target>
      </segment>
    </unit>
    <unit id="4089509911694721896">
      <segment state="initial">
        <source>Last Updated</source>
        <target>Last Updated</target>
      </segment>
    </unit>
    <unit id="3014142150963657456">
      <segment state="initial">
        <source>Client</source>
        <target>Client</target>
      </segment>
    </unit>
    <unit id="4628738230038299640">
      <segment state="initial">
        <source>Assessment Date</source>
        <target>Assessment Date</target>
      </segment>
    </unit>
    <unit id="2535123683532102008">
      <segment state="initial">
        <source>Assessment Result</source>
        <target>Assessment Result</target>
      </segment>
    </unit>
    <unit id="5313216855975199451">
      <notes>
        <note category="description">Manage Cloud Keys: Warning text showing key deltion is irreversible</note>
      </notes>
      <segment state="initial">
        <source> ♦ Warning! This action will delete the key from </source>
        <target> ♦ Warning! This action will delete the key from </target>
      </segment>
    </unit>
    <unit id="9155608366859514313">
      <segment state="initial">
        <source>Source</source>
        <target>Source</target>
      </segment>
    </unit>
    <unit id="5611592591303869712">
      <segment state="initial">
        <source>Status</source>
        <target>Status</target>
      </segment>
    </unit>
    <unit id="4816216590591222133">
      <segment state="initial">
        <source>Enabled</source>
        <target>Enabled</target>
      </segment>
    </unit>
    <unit id="5275254013832532154">
      <segment state="initial">
        <source>Key Vault</source>
        <target>Key Vault</target>
      </segment>
    </unit>
    <unit id="4179734860143712488">
      <notes>
        <note category="description">Stepper-View:button text for Create</note>
      </notes>
      <segment state="initial">
        <source> Create</source>
        <target> Create</target>
      </segment>
    </unit>
    <unit id="6570363013146073520">
      <notes>
        <note category="description">main-menu home link</note>
      </notes>
      <segment state="initial">
        <source>Dashboard</source>
        <target>Dashboard</target>
      </segment>
    </unit>
    <unit id="6460207866734534390">
      <segment state="initial">
        <source>Cloud Clients</source>
        <target>Cloud Clients</target>
      </segment>
    </unit>
    <unit id="3499507106617527960">
      <segment state="initial">
        <source>Azure</source>
        <target>Azure</target>
      </segment>
    </unit>
    <unit id="6083409672770600534">
      <segment state="initial">
        <source>AWS</source>
        <target>AWS</target>
      </segment>
    </unit>
    <unit id="3590805807378700861">
      <segment state="initial">
        <source>Google</source>
        <target>Google</target>
      </segment>
    </unit>
    <unit id="6870744132322796712">
      <segment state="initial">
        <source>Enterprise Clients</source>
        <target>Enterprise Clients</target>
      </segment>
    </unit>
    <unit id="4221199921973829385">
      <segment state="initial">
        <source>KMIP</source>
        <target>KMIP</target>
      </segment>
    </unit>
    <unit id="8086244912297636472">
      <segment state="initial">
        <source>TDE</source>
        <target>TDE</target>
      </segment>
    </unit>
    <unit id="1356200968511280526">
      <segment state="initial">
        <source>REST</source>
        <target>REST</target>
      </segment>
    </unit>
    <unit id="8493363135660887954">
      <segment state="initial">
        <source>Access Management</source>
        <target>Access Management</target>
      </segment>
    </unit>
    <unit id="4555457172864212828">
      <segment state="initial">
        <source>Users</source>
        <target>Users</target>
      </segment>
    </unit>
    <unit id="5944812089887969249">
      <segment state="initial">
        <source>Groups</source>
        <target>Groups</target>
      </segment>
    </unit>
    <unit id="1678559204833619774">
      <segment state="initial">
        <source>Audit Log</source>
        <target>Audit Log</target>
      </segment>
    </unit>
    <unit id="6785659997897155974">
      <segment state="initial">
        <source>Setting</source>
        <target>Setting</target>
      </segment>
    </unit>
    <unit id="5273501066845923426">
      <notes>
        <note category="description">AddkmipClient: label field for Client Id</note>
      </notes>
      <segment state="initial">
        <source>Client ID</source>
        <target>Client ID</target>
      </segment>
    </unit>
    <unit id="5295998403339413845">
      <notes>
        <note category="description">AddkmipClient: label field for Client Id Validation Error</note>
      </notes>
      <segment state="initial">
        <source> Client ID is required </source>
        <target> Client ID is required </target>
      </segment>
    </unit>
    <unit id="1431416938026210429">
      <notes>
        <note category="description">AddkmipClient: label field for Password</note>
      </notes>
      <segment state="initial">
        <source>Password</source>
        <target>Password</target>
      </segment>
    </unit>
    <unit id="2161902857876513332">
      <notes>
        <note category="description">AddkmipClient: label field for Password Validation Error</note>
      </notes>
      <segment state="initial">
        <source> Password is required and must be at least 12 characters </source>
        <target> Password is required and must be at least 12 characters </target>
      </segment>
    </unit>
    <unit id="3241357959735682038">
      <notes>
        <note category="description">AddkmipClient: label field for Confirm Password</note>
      </notes>
      <segment state="initial">
        <source>Confirm Password</source>
        <target>Confirm Password</target>
      </segment>
    </unit>
    <unit id="4865005494711354510">
      <notes>
        <note category="description">AddkmipClient: label field for Confirm Password Validation Error</note>
      </notes>
      <segment state="initial">
        <source> Please confirm your password </source>
        <target> Please confirm your password </target>
      </segment>
    </unit>
    <unit id="4314910104023472121">
      <notes>
        <note category="description">AddkmipClient: label field for Password Mismatch Validation Error</note>
      </notes>
      <segment state="initial">
        <source> Passwords do not match </source>
        <target> Passwords do not match </target>
      </segment>
    </unit>
    <unit id="9030336811208796738">
      <notes>
        <note category="description">AddkmipClient: label field for Client Certificate</note>
      </notes>
      <segment state="initial">
        <source>Client Certificate</source>
        <target>Client Certificate</target>
      </segment>
    </unit>
    <unit id="290160340331958183">
      <notes>
        <note category="description">AddkmipClient: label field for Client Certificate Validation Error</note>
      </notes>
      <segment state="initial">
        <source> Client Certificate is required </source>
        <target> Client Certificate is required </target>
      </segment>
    </unit>
    <unit id="3026976714330562202">
      <notes>
        <note category="description">AddkmipClient: label field for Client Certificate Note</note>
      </notes>
      <segment state="initial">
        <source> Note: Client Id and Common Name in the Client Certificate should match </source>
        <target> Note: Client Id and Common Name in the Client Certificate should match </target>
      </segment>
    </unit>
    <unit id="1200900687640843597">
      <segment state="initial">
        <source>Creation By</source>
        <target>Creation By</target>
      </segment>
    </unit>
    <unit id="165617360560009966">
      <segment state="initial">
        <source>Certificate Expiration Date</source>
        <target>Certificate Expiration Date</target>
      </segment>
    </unit>
    <unit id="2925129304308690882">
      <segment state="initial">
        <source>Last modified</source>
        <target>Last modified</target>
      </segment>
    </unit>
    <unit id="5252837360733823238">
      <notes>
        <note category="description">ViewkmipClient: label field for Created By</note>
      </notes>
      <segment state="initial">
        <source>Created By</source>
        <target>Created By</target>
      </segment>
    </unit>
    <unit id="8820965746779312111">
      <notes>
        <note category="description">ViewkmipClient: label field for Last Modified</note>
      </notes>
      <segment state="initial">
        <source>Last Modified</source>
        <target>Last Modified</target>
      </segment>
    </unit>
    <unit id="5522236499655154147">
      <notes>
        <note category="description">ViewkmipClient: label field for Certificate Start Date</note>
      </notes>
      <segment state="initial">
        <source>Certificate Start Date</source>
        <target>Certificate Start Date</target>
      </segment>
    </unit>
    <unit id="5491349027224642844">
      <notes>
        <note category="description">UpdatekmipClient: label field for New Certificate Entry</note>
      </notes>
      <segment state="initial">
        <source>New Certificate</source>
        <target>New Certificate</target>
      </segment>
    </unit>
    <unit id="1221596867309460035">
      <notes>
        <note category="description">UpdatekmipClient: label field for New Password Entry</note>
      </notes>
      <segment state="initial">
        <source>New Password</source>
        <target>New Password</target>
      </segment>
    </unit>
    <unit id="9132547751630599796">
      <notes>
        <note category="description">UpdatekmipClient: Error message for Password Validation</note>
      </notes>
      <segment state="initial">
        <source> Password is required </source>
        <target> Password is required </target>
      </segment>
    </unit>
    <unit id="7731102146667330348">
      <notes>
        <note category="description">UpdatekmipClient: Error message for Password Validation</note>
      </notes>
      <segment state="initial">
        <source> Password must be at least 8 characters long </source>
        <target> Password must be at least 8 characters long </target>
      </segment>
    </unit>
    <unit id="1485410082721015205">
      <notes>
        <note category="description">UpdatekmipClient: Error message for Confirm Password Validation</note>
      </notes>
      <segment state="initial">
        <source> Confirm Password is required </source>
        <target> Confirm Password is required </target>
      </segment>
    </unit>
    <unit id="6164699983643908587">
      <segment state="initial">
        <source>Object Name</source>
        <target>Object Name</target>
      </segment>
    </unit>
    <unit id="7716208024960184784">
      <segment state="initial">
        <source>UUID</source>
        <target>UUID</target>
      </segment>
    </unit>
    <unit id="682467974069032141">
      <segment state="initial">
        <source>Change Client Certificate</source>
        <target>Change Client Certificate</target>
      </segment>
    </unit>
    <unit id="6582573664846559511">
      <segment state="initial">
        <source>Change Password</source>
        <target>Change Password</target>
      </segment>
    </unit>
    <unit id="7754924993990855042">
      <notes>
        <note category="description">AddCloudInstance: option field Select</note>
      </notes>
      <segment state="initial">
        <source> Select </source>
        <target> Select </target>
      </segment>
    </unit>
    <unit id="8323422358213440128">
      <notes>
        <note category="description">AddCloudInstance: Cancel</note>
      </notes>
      <segment state="initial">
        <source>
          <ph id="0" equiv="INTERPOLATION" disp="{{ cancelLocalized }}"/>
        </source>
        <target>
          <ph id="0" equiv="INTERPOLATION" disp="{{ cancelLocalized }}"/>
        </target>
      </segment>
    </unit>
    <unit id="5668632685575118472">
      <notes>
        <note category="description">AddCloudInstance:Cancel</note>
      </notes>
      <segment state="initial">
        <source> Cancel</source>
        <target> Cancel</target>
      </segment>
    </unit>
    <unit id="4350423050822384120">
      <notes>
        <note category="description">AddCloudInstance:title</note>
      </notes>
      <segment state="initial">
        <source> Add Cloud Instance</source>
        <target> Add Cloud Instance</target>
      </segment>
    </unit>
    <unit id="9101591947723213375">
      <notes>
        <note category="description">EditCloudInstance:title</note>
      </notes>
      <segment state="initial">
        <source> Edit Cloud Instance</source>
        <target> Edit Cloud Instance</target>
      </segment>
    </unit>
    <unit id="8828229858764392252">
      <notes>
        <note category="description">DeleteCloudInstance:title</note>
      </notes>
      <segment state="initial">
        <source> Delete Cloud Instance</source>
        <target> Delete Cloud Instance</target>
      </segment>
    </unit>
    <unit id="1593087966609132148">
      <notes>
        <note category="description">Upload Key:title</note>
      </notes>
      <segment state="initial">
        <source> Upload to </source>
        <target> Upload to </target>
      </segment>
    </unit>
    <unit id="4406493224727225230">
      <notes>
        <note category="description">Stepper-View:button text for Next</note>
      </notes>
      <segment state="initial">
        <source> Next</source>
        <target> Next</target>
      </segment>
    </unit>
    <unit id="5893739794080693805">
      <notes>
        <note category="description">DeleteCloudInstance:delete message</note>
      </notes>
      <segment state="initial">
        <source> Are you sure you want to delete instance </source>
        <target> Are you sure you want to delete instance </target>
      </segment>
    </unit>
    <unit id="1838419884521058275">
      <notes>
        <note category="description">AddKmipClient: Button text for Add Kmip Client</note>
      </notes>
      <segment state="initial">
        <source>Add KMIP Client</source>
        <target>Add KMIP Client</target>
      </segment>
    </unit>
    <unit id="6132406321239737282">
      <notes>
        <note category="description">KmipClientListing: Button text for Download Kmip Client Certificate</note>
      </notes>
      <segment state="initial">
        <source>Download Client Cert</source>
        <target>Download Client Cert</target>
      </segment>
    </unit>
  </file>
</xliff>