include:
  - project: taas/taas-shared-pipelines
    ref: develop
    file: taas-angular-frontend-pipeline/base-frontend-pipeline-template.yml
    inputs:
      dev_storage_account_name: ${DEV_EKMAAS_STORAGE_ACCOUNT_NAME}
      dev_storage_account_accesskey: ${DEV_EKMAAS_STORAGE_ACCOUNT_ACCESSKEY}

      preprod_storage_account_name: ${PREPROD_EKMAAS_STORAGE_ACCOUNT_NAME}
      preprod_storage_account_accesskey: ${PREPROD_EKMAAS_STORAGE_ACCOUNT_ACCESSKEY}

      prod_storage_account_name: ${PROD_EKMAAS_STORAGE_ACCOUNT_NAME}
      prod_storage_account_accesskey: ${PROD_EKMAAS_STORAGE_ACCOUNT_ACCESSKEY}

      folder_to_deploy: 'dist/taas-km-frontend-angular/browser/'
      cdn_endpoint_name_prefix: 'cdn-st-ekmaas'

# second build and deployment for POC frontend
  - project: taas/taas-shared-pipelines
    ref: develop
    file: taas-angular-frontend-pipeline/jobs/npm-build.yml
    inputs:
      environment: poc
      ca_cert: ${ROOT_CA}

  - project: taas/taas-shared-pipelines
    ref: develop
    file: taas-angular-frontend-pipeline/jobs/deploy-azure.yml
    inputs:
      # poc is needed here to prevent the imported job to overwrite the existing preprod job
      environment: poc
      azure_api_id: ${PREPROD_AZURE_APP_ID}
      azure_secret: ${PREPROD_AZURE_SECRET}
      azure_tenant_id: ${PREPROD_AZURE_TENANT_ID}
      azure_subscription: taas-preprod-001
      storage_account_name: ${PREPROD_POC_EKMAAS_STORAGE_ACCOUNT_NAME}
      storage_account_accesskey: ${PREPROD_POC_EKMAAS_STORAGE_ACCOUNT_ACCESSKEY}
      folder_to_deploy: dist/taas-km-frontend-angular/browser/
      cdn_profile_name: cdn-profile-taas-static-websites-preprod
      cdn_resource_group_name: rg-staticwebapp-preprod-001
      cdn_endpoint_name: cdn-st-ekmaas-poc-001

Build_for_POC:
  stage: build
  rules:
    - if: $CI_COMMIT_BRANCH == "preprod"
  extends: 
    - .npm-build-poc
  environment:
    name: preprod
  when:
    manual
  needs:
    - Install_dependencies

Deploy_to_Azure_POC:
  stage: deploy
  rules:
    - if: $CI_COMMIT_BRANCH == "preprod"
  extends: 
    - .deploy-azure-poc
  environment:
    name: preprod
  when:
    manual
  needs: 
    - Build_for_POC
